<template>
	<view>
		<tn-popup v-model="popShow" height="60%" mode="bottom" :borderRadius="20">
			<view class="search-title">
				{{title}}
			</view>
,
			<view class="search-slot">
				<slot></slot>
			</view>
			
		</tn-popup>
	</view>
</template>

<script>
	export default {
		name: 'people',
		props: {
			show: {
				type: Boolean,
				default: false
			},
			// 弹出方向 left/right/top/bottom/center
			mode: {
				type: String,
				default: 'bottom'
			},
			title:{
				type: String,
				default: '设置'
			}
		},
		data() {
			return {
				popShow: this.show,
				search:''
			}
		},
		methods: {
			handelClose() {
				this.$emit('close')
			},
			handelConfirm() {
				this.popShow = false;
				this.$emit('confirm',{search:this.search})
			}
		},
		watch: {
			popShow(val) {
				if (val === false) {
					this.$emit('close')
				}
			},
			show(val) {
				this.popShow = val
			}
		}
	}
</script>

<style lang="scss" scoped>
.search-title{
	height: 88rpx;
	width: 100%;
	line-height: 88rpx;
	text-align: center;
	font-size: 32rpx;
	font-weight: bold;
	position: fixed;
	z-index: 99;
	box-shadow: 0 0 10rpx rgba(0,0,0,0.2);
	background-color: #fff;
}

.search-input{
	width: 94%;
	margin: 0 auto;
	height: 70rpx;
	padding-top: 120rpx;
	
	.input{
		box-shadow: 0 0 10rpx rgba(0,0,0,0.2);
	}
}

.search-slot{
	width: 94%;
	margin: 0 auto;
	padding-top: 120rpx;
}

.search-btn{
	width:100%;
	height: 120rpx;
	position: fixed;
	bottom: 0;
	z-index: 99;
	
	.btn-box{
		width: 94%;
		margin: 0 auto;
	}
}
</style>