<template>
	<view class="container">
		<view v-for="(item, index) in dataList" class="list-item">
			<view class="list-info">
				<image class="avatar" :src="item.avatar" mode=""></image>
				<view class="info-box">
					<text class="info-item">职能：{{ item.name }}</text>
					<text class="info-item">负责人：{{ item.real_name ? item.real_name : '--' }}</text>
				</view>
			</view>
			<view class="button-box">
				<tn-button v-if="item.state=='未设置'" margin="0 10rpx" backgroundColor="tn-main-gradient-aquablue" fontColor="#fff" size="sm"
					@click="handleSetClick(item.id,item.name)">
					<text class="text">设置</text>
				</tn-button>

				<tn-button v-if="item.state=='正常'" margin="0 10rpx" backgroundColor="tn-main-gradient-orangered" fontColor="#fff" size="sm"
					@click="handleChangeClick(item.id,item.name)">
					<text class="text">换人</text>
				</tn-button>

				<tn-button v-if="item.state=='正常'" margin="0 10rpx" backgroundColor="tn-main-gradient-red" fontColor="#fff" size="sm"
					@click="handleClearClick(item.id)">
					<text class="text">清空</text>
				</tn-button>

				<People :show="openPeople" @close="closePeople" :title="openTitle">
					<view v-for="(item, index) in userList">
						<view class="user-list-info" @click="confirm(item.id,item.real_name)">
							<image class="avatar" :src="item.avatar" mode=""></image>
							<view class="info-box">
								<text class="info-item">姓名：{{ item.real_name }}</text>
								<text class="info-item">岗位：{{ item.title }}</text>
							</view>
						</view>
					</view>
				</People>

			</view>
		</view>
	</view>
</template>

<script>
	import People from '@/components/people.vue'
	import {
		getFunctionalList,
		cleraFunctionalPerson,
		setFunctionalPerson,
		changeFunctionalPerson
	} from '@/api/process.js'

	export default {
		components: {
			People
		},
		data() {
			return {
				flow_custom_id: '', // 线索id
				dataList: [], // 数据列表
				userList:[],//用户列表
				openPeople: false,
				openTitle: '设置',
				action: 'set',
				duty_id: 0,
				duty_name: '职能名称',
				paramsObj: ''
			}
		},
		onLoad(options) {
			this.flow_custom_id = options.flow_custom_id
			// 获取列表
			this.getList()
		},
		onShow() {
			console
			// this.flow_custom_id = options.flow_custom_id
			// 获取列表
			this.getList()
		},
		methods: {
			// 获取列表
			getList() {
				getFunctionalList({
					flow_custom_id: this.flow_custom_id
				}).then(res => {
					if (res) {
						this.dataList = res.list;
						this.userList = res.user;
					}
				})
			},
			handleClearClick(duty_id) {
				let that = this;
				that.$api.modal('操作确认', '您确认要清空该职能负责人吗?', {}, function(res) {
					if (res.status == 'confirm') {
						cleraFunctionalPerson({
							id: duty_id
						}).then(res1 => {
							if (res1) {
								that.$api.toast(res1.info);
								setTimeout(() => {
									that.getList();
								}, 2000);
							}
						})
					}
				})
			},
			handleSetClick(duty_id, duty_name) {
				this.duty_id = duty_id;
				this.duty_name = duty_name;
				this.openTitle = '设置 - ' + duty_name;
				this.action = 'set';
				this.paramsObj = {
					duty_id: this.duty_id,
					duty_name: this.duty_name,
					openTitle: this.openTitle,
					action: this.action
				}
				uni.navigateTo({
					url: `/pages/functional-setting/candidate?data=${encodeURIComponent(JSON.stringify(this.paramsObj))}`,
				})
				// this.openPeople = true;
			},
			handleChangeClick(duty_id, duty_name) {
				this.duty_id = duty_id;
				this.duty_name = duty_name;
				this.openTitle = '更换负责人 - ' + duty_name;
				this.action = 'change';
				// this.openPeople = true;
				this.paramsObj = {
					duty_id: this.duty_id,
					duty_name: this.duty_name,
					openTitle: this.openTitle,
					action: this.action
				}
				uni.navigateTo({
					url: `/pages/functional-setting/candidate?data=${encodeURIComponent(JSON.stringify(this.paramsObj))}`,
				})
			},
			closePeople() {
				this.openPeople = false;
			},
			confirm(user_id,real_name) {
				let that = this;
				that.openPeople = false;
				if (that.action == 'set') {
					that.$api.modal('操作确认', '您确认要设置该用户【'+real_name+'】负责【' + that.duty_name + '】职能吗?', {}, function(res) {
						if (res.status == 'confirm') {
							setFunctionalPerson({
								id: that.duty_id,
								user_id: user_id
							}).then(res1 => {
								if (res1) {
									that.$api.toast(res1.info);
									setTimeout(() => {
										that.getList();
									}, 2000);
								}
							})
						}
					})
				}else if(that.action == 'change'){
					that.$api.modal('操作确认', '您确认要更换该用户【'+real_name+'】负责【' + that.duty_name + '】职能吗?', {}, function(res) {
						if (res.status == 'confirm') {
							changeFunctionalPerson({
								id: that.duty_id,
								user_id: user_id
							}).then(res1 => {
								if (res1) {
									that.$api.toast(res1.info);
									setTimeout(() => {
										that.getList();
									}, 2000);
								}
							})
						}
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 40rpx;
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));

		.list-item {
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			&+.list-item {
				margin-top: 30rpx;
			}

			.list-info {
				display: flex;
				align-items: center;
				padding: 32rpx;

				.avatar {
					width: 100rpx;
					height: 100rpx;
					border-radius: 50%;
					border: 2rpx solid #e6e6e6;
				}

				.info-box {
					padding-left: 30rpx;

					.info-item {
						display: block;
						font-weight: 400;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;

						&+.info-item {
							margin-top: 10rpx;
						}
					}
				}
			}

			.button-box {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				padding-right: 30rpx;
				height: 80rpx;
				border-top: 2rpx solid #E6E6E6;
			}
		}
	}

	.user-list-info {
		display: flex;
		align-items: center;
		padding: 32rpx;
		margin: 0rpx auto 30rpx auto;
		border-radius: 10rpx;
		box-shadow: 0rpx 0rpx 10rpx 10rpx rgba(20, 20, 22, 0.1);

		.avatar {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			border: 2rpx solid #e6e6e6;
		}

		.info-box {
			padding-left: 30rpx;

			.info-item {
				display: block;
				font-weight: 400;
				font-size: 28rpx;
				color: #555555;
				line-height: normal;

				&+.info-item {
					margin-top: 10rpx;
				}
			}
		}
	}
</style>