<template>
	<view class="container">
		<view v-for="(item, index) in flowList" class="flow-item" :key="index">
			<view v-if="item.children && item.children.length">
				<view class="flow-name">{{ item.work_name }}</view>
				<view v-for="(cItem, cIndex) in item.children">
					<view v-if="['text', 'number', 'finance', 'date', 'datetime', 'template', 'radio', 'geo'].indexOf(cItem.form_type) !== -1" class="flow-form-item form-input">
						<view class="label">{{ cItem.work_name }}</view>
						<text class="text selected">{{ cItem.val ? cItem.val : cItem.val_file }}</text>
					</view>
					<!-- textarea -->
					<view v-if="cItem.form_type === 'textarea'" class="flow-form-item form-textarea">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="textarea-text">{{ cItem.val ? cItem.val : cItem.val_file }}</view>
					</view>
					<!-- 多选 -->
					<view v-if="cItem.form_type === 'checkbox'" class="flow-form-item form-select" @tap="selectCheckbox(index, cIndex)">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="text selected" :class="{'selected': cItem.val}">
							{{ cItem.val ? cItem.val.join(', ') : '请选择' }}
						</view>
					</view>
					<!-- 图片 -->
					<view v-if="cItem.form_type === 'image'" class="flow-form-item form-image">
						<view class="label">{{ cItem.work_name }}</view>
						<view v-if="Array.isArray(cItem.val_file)" class="image-wrap">
							<image v-for="(sItem, sIndex) in cItem.val_file" class="image-item" :src="sItem" mode="aspectFill" @tap="previewMedia(cItem.val_file, sIndex, 'image')"></image>
						</view>
					</view>
					<!-- 视频 -->
					<view v-if="cItem.form_type === 'video'" class="flow-form-item form-image">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="video-wrap">
							<view class="video-item">视频1</view>
						</view>
						<view v-if="Array.isArray(cItem.val_file)" class="video-wrap">
							<view v-for="(sItem, sIndex) in cItem.val_file" class="video-item" @tap="previewMedia(cItem.val_file, sIndex, 'video')">视频{{ sIndex + 1 }}</view>
						</view>
					</view>
					<!-- 文件 -->
					<view v-if="['cad'].indexOf(cItem.form_type) !== -1" class="flow-form-item form-file">
						<view class="label">{{ cItem.work_name }}</view>
						<view v-if="Array.isArray(cItem.val_file)" class="file-wrap">
							<image v-for="(sItem, sIndex) in cItem.val_file" class="file-icon" :src="sItem" mode="aspectFill" @tap="previewMedia(cItem.val_file, sIndex, 'image')"></image>
						</view>
					</view>
					<!-- pdf -->
					<view v-if="['file'].indexOf(cItem.form_type) !== -1" class="flow-form-item form-file">
						<view class="label">{{ cItem.work_name }}</view>
						<view v-if="Array.isArray(cItem.val_file)" class="file-wrap">
							<image v-for="(sItem, sIndex) in cItem.val_file" class="file-icon" :src="cItem.icon[sIndex]" mode="aspectFill" @click="previewPDF(sItem)"></image>
						</view>
						<view class="file-wrap" v-else>
							未提交
						</view>
					</view>
				</view>
				
			</view>
			<view v-else>
				<view class="flow-name">{{ item.work_name }}</view>
				<view class="flow-form-item form-textarea">
					<view class="label">负责人</view>
					<view class="textarea-text">{{ item.real_name }}</view>
				</view>
				<view class="flow-form-item form-textarea">
					<view class="label" style="width: auto;">{{ item.full_name }}</view>
					<view class="textarea-text">{{ item.state }}</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getCustomerWorkDetails
	} from '@/api/process.js'

	export default {
		data() {
			return {
				flow_id: '', // 数据id
				flowList: [], // 工作流列表
			}
		},
		onLoad(options) {
			console.log(123)
			this.flow_id = options.flow_id
			// 获取数据
			this.getData()
			uni.setNavigationBarTitle({
				title: options.work_name
			})
		},
		methods: {
			async previewPDF(url){
				const pdfUrl = url;
				console.log(pdfUrl);
				 try {
				        // 步骤1：下载文件
						const res = await uni.downloadFile({
						url: pdfUrl
					});
					console.log(res[1].tempFilePath);
					
				        // 步骤2：打开文档
				        await uni.openDocument({
				          filePath: res[1].tempFilePath,
				          success: () => {
				            console.log('打开文档成功');
				          }
				        });
				      } catch (error) {
				        console.log('预览失败:', error);
				      }
			},
			// 获取数据
			getData() {
				getCustomerWorkDetails({
					id: this.flow_id
				}).then(res => {
					if (res) {
						this.flowList = res.flow
					}
				})
			},
			// 预览多媒体
			previewMedia(list, index, type) {
				if (type === 'image') {
					uni.previewImage({
						current: index,
						urls: list
					})
				}
				if (type === 'video') {
					wx.previewMedia({
						sources: [{
							url: list[index], // 视频的 URL
							type: 'video', // 设置类型为视频
						}]
					})
				}
			},
			// 预览文件
			previewFile(url) {
				uni.downloadFile({
					url: url,
					success: function(res) {
						var filePath = res.tempFilePath
						uni.openDocument({
							filePath: filePath,
							fail: () => {
								this.$util.showToast('文件预览失败，请使用PC端查看');
							}
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 40rpx;
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));

		.customer-information {
			margin-bottom: 40rpx;
			padding: 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			.main-info {
				font-weight: 500;
				font-size: 30rpx;
				color: #3A3B40;
				line-height: 30rpx;
			}

			.secondary-info {
				display: block;
				margin-top: 16rpx;
				font-weight: 500;
				font-size: 26rpx;
				color: #74747B;
				line-height: 26rpx;
			}

			.main-info+.secondary-info {
				margin-top: 32rpx !important;
			}
		}

		.flow-item {
			position: relative;
			padding: 0 32rpx 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			&+.flow-item {
				margin-top: 38rpx;

				&::before {
					content: '';
					position: absolute;
					top: -50rpx;
					left: 50rpx;
					width: 28rpx;
					height: 68rpx;
					background: url(../../static/icon/<EMAIL>) center/100% repeat;
					z-index: -1;
				}

				&::after {
					content: '';
					position: absolute;
					top: -50rpx;
					right: 50rpx;
					width: 28rpx;
					height: 68rpx;
					background: url(../../static/icon/<EMAIL>) center/100% repeat;
					z-index: -1;
				}
			}

			.flow-name {
				padding: 48rpx 0 20rpx;
				font-weight: 700;
				font-size: 32rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.flow-textarea {
				height: 156rpx;
				font-size: 28rpx;
				color: #555555;
				line-height: normal;
			}

			.flow-form-item {
				display: flex;
				padding: 20rpx 0;
				border-bottom: 2rpx solid #F0F0F0;

				.label {
					width: 200rpx;
					font-size: 28rpx;
					color: #3A3B40;
					line-height: normal;
				}

				.text {
					flex: 1;
					font-size: 28rpx;
					color: #BBBEC6;
					line-height: normal;
					text-align: right;

					&.selected {
						color: #555555;
					}
				}

				.icon {
					display: block;
					width: 28rpx;
					height: 16rpx;
					margin-left: 30rpx;
				}

				// input输入框
				&.form-input {
					display: flex;
					align-items: center;
					min-height: 96rpx;

					.input {
						flex: 1;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}
				}

				// textarea输入框
				&.form-textarea {
					display: flex;
					padding: 34rpx 0;

					.textarea {
						flex: 1;
						height: 96rpx;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}

					.textarea-text {
						flex: 1;
						min-height: 96rpx;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}
				}

				// select选择框
				&.form-select {
					display: flex;
					align-items: center;
					min-height: 96rpx;
				}

				// image类型
				&.form-image {
					display: flex;
					padding: 22rpx 0;

					.image-wrap {
						flex: 1;
						display: flex;
						flex-wrap: wrap;
						justify-content: flex-end;

						.image-item {
							width: 80px;
							height: 80px;
							margin: 0 0 8px 8px;
						}
					}

					.video-wrap {
						flex: 1;
						display: flex;
						flex-wrap: wrap;
						justify-content: flex-end;

						.video-item {
							width: 100rpx;
							margin: 0 0 8px 8px;
							padding: 4rpx 0;
							background: #6FB2E2;
							border-radius: 8rpx;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							line-height: normal;
							text-align: center;
						}
					}
				}

				// file类型
				&.form-file {
					display: flex;
					padding: 22rpx 0;

					.label {
						display: flex;
						align-items: center;
						height: 56rpx;
					}

					.file-box {
						flex: 1;
						display: flex;
						flex-direction: column;
						align-items: flex-end;

						.button {
							display: flex;
							justify-content: center;
							align-items: center;
							height: 56rpx;
							padding: 0 20rpx;
							background: $theme-color;
							border-radius: 8rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
							line-height: normal;
						}

						.file-item {
							width: 100%;
							display: flex;
							align-items: center;
							margin-top: 10rpx;

							.file-name {
								flex: 1;
								font-weight: 400;
								font-size: 28rpx;
								color: #555555;
								line-height: normal;
								text-align: right;
							}

							.delete-icon {
								width: 20rpx;
								height: 20rpx;
								margin-left: 16rpx;
							}
						}
					}

					.file-wrap {
						flex: 1;
						display: flex;
						flex-wrap: wrap;
						justify-content: flex-end;

						.file-item {
							width: 100rpx;
							margin: 0 0 8px 8px;
							padding: 4rpx 0;
							background: #6FB2E2;
							border-radius: 8rpx;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							line-height: normal;
							text-align: center;
						}
					}
				}

				&.form-geo {
					display: flex;
					align-items: center;
					min-height: 96rpx;

					.value {
						flex: 1;
						display: flex;
						justify-content: flex-end;

						.text {
							flex: 1;
							font-size: 28rpx;
							color: #555555;
							line-height: normal;
							text-align: right;
						}

						.button {
							display: flex;
							justify-content: center;
							align-items: center;
							height: 56rpx;
							padding: 0 20rpx;
							background: $theme-color;
							border-radius: 8rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
							line-height: normal;
						}
					}
				}
			}
		}

		.button-box {
			display: flex;
			align-items: center;
			margin-top: 80rpx;

			.button {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 100rpx;
				background: $theme-color;
				border-radius: 8rpx;

				&.reject {
					background: #E94848;
				}

				&+.button {
					margin-left: 30rpx;
				}

				.text {
					font-weight: 500;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: normal;
				}
			}
		}
	}

	::v-deep .input-placeholder,
	::v-deep .textarea-placeholder {
		color: #BBBEC6;
	}

	::v-deep .checkbox-box {
		padding: 80rpx 40rpx 40rpx;

		.tn-checkbox-class {
			margin-top: 20rpx;
		}
	}

	::v-deep .reject-box {
		box-sizing: border-box;
		width: calc(100vw - 80rpx);
		padding: 0 32rpx 32rpx;

		.header {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			font-weight: 700;
			font-size: 32rpx;
			color: #4A4A4A;
			line-height: normal;
		}

		.node {
			display: flex;
			align-items: center;
			height: 96rpx;
			border-bottom: 2rpx solid #F0F0F0;

			.label {
				width: 200rpx;
				font-size: 28rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.text {
				flex: 1;
				font-size: 28rpx;
				color: #BBBEC6;
				line-height: normal;
				text-align: right;

				&.selected {
					color: #555555;
				}
			}

			.icon {
				display: block;
				width: 28rpx;
				height: 16rpx;
				margin-left: 30rpx;
			}
		}

		.remark {
			display: flex;
			padding: 34rpx 0;
			border-bottom: 2rpx solid #F0F0F0;

			.label {
				width: 200rpx;
				font-size: 28rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.textarea {
				flex: 1;
				height: 96rpx;
				font-size: 28rpx;
				color: #555555;
				line-height: normal;
				text-align: right;
			}
		}

		.button-box {
			display: flex;
			align-items: center;
			margin-top: 40rpx;

			.button {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 80rpx;
				background: #E94848;
				border-radius: 8rpx;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: normal;

				&.cancel {
					background: #FFFFFF;
					border: 2rpx solid #DCDFE6;
					color: #666666;
				}
			}
		}
	}
	
	.file-icon{
		width: 60rpx;
		height: 60rpx;
	}
</style>