import Vue from "vue";
import Vuex from "vuex";

Vue.use(Vuex);

const store = new Vuex.Store({
	state: {
		userInfo: {
			token: uni.getStorageSync('token') || null
		},
		token: uni.getStorageSync('token') || null,
		vuex_custom_bar_height: 0,
		vuex_status_bar_height: 0
	},
	mutations: {
		SET_USER_INFO(state, userInfo) {
			state.userInfo = userInfo;
			uni.setStorageSync('userInfo', userInfo);
		},
		SET_TOKEN(state, token) {
			state.token = token;
			uni.setStorageSync('token', token);
		},
		SET_CUSTOM_BAR_HEIGHT(state, height) {
			state.vuex_custom_bar_height = height;
		},
		SET_STATUS_BAR_HEIGHT(state, height) {
			state.vuex_status_bar_height = height;
		}
	},
	actions: {
		updateCustomBarHeight({ commit }, height) {
			commit('SET_CUSTOM_BAR_HEIGHT', height);
		},
		updateStatusBarHeight({ commit }, height) {
			commit('SET_STATUS_BAR_HEIGHT', height);
		}
	}
});

export default store;