<template>
	<!-- 预警 -->
	<view class="box">
		<!-- #ifdef H5 -->
		<view style="height: 44px;width: 100%;"></view>
		<!-- #endif -->
		<view class="tabs">
			<view class="tab-item" :class="{ active: activeTab === 1 }" @click="changeTab(1)">
				<text>预警</text>
				<view class="badge">{{count}}</view>
				<view v-if="activeTab === 1" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 2 }" @click="changeTab(2)">
				<text>报警</text>
				<view class="badge">{{ count1 }}</view>
				<view v-if="activeTab === 2" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 3 }" @click="changeTab(3)">
				<text>延时完成</text>
				<view class="badge">{{ count2 }}</view>
				<view v-if="activeTab === 3" class="active-line"></view>
			</view>
		</view>
		<view class="list_arr" v-if="allData.length">
			<view class="list_item" v-for="(item, index) in allData" :key="index">
				<view class="item_top">
					<view class="item_top_title">
						<!-- <text style="color: red;">{{ item.real_name }}</text> -->
						{{ item.flow_custom_name }}
					</view>
					<view class="status" :style="{color: activeTab == 1 ? '#FBBD12' : activeTab == 2 ? '#E83A30' : '#E72F8C' }">
						{{ item.level }}
					</view>
				</view>
				<view class="item_name">
					<view> <text style="font-weight: 550;">负责人：</text><text>{{ item.real_name }}</text> </view>
				</view>
				<view class="item_name">
					<view> <text style="font-weight: 550;">负责节点：</text>{{ item.work_name }} </view>
				</view>
				<view class="item_name">
					<text style="font-weight: 550;">线索名称：</text>{{ item.clue_name ? item.clue_name : '--' }}
				</view>
				<view class="item_name" v-if="item.create_time">
					<text style="font-weight: 550;">创建时间：</text>{{ item.create_time }}
				</view>
				<view class="item_name" v-if="item.start_time">
					<text style="font-weight: 550;">开始时间：</text>{{ item.start_time }}
				</view>
				<view class="item_name" v-if="item.end_time">
					<text style="font-weight: 550;">结束时间：</text>{{ item.end_time }}
				</view>
				<view class="item_name" v-if="item.finish_time">
					<text style="font-weight: 550;">完成时间：</text>{{ item.finish_time }}
				</view>
				<view class="item_mian" :style="{color: activeTab == 1 ? '#FBBD12' : activeTab == 2 ? '#E83A30' : '#E72F8C' }">
					{{ item.title }}
				</view>
			</view>
		</view>
		<view class="miann_app" v-if="!loging && !allData.length">
			<tn-empty mode="data"></tn-empty>
		</view>
		<view class="loading-status">
			<text v-if="isLoading">正在加载...</text>
			<text v-if="noMoreData">没有更多数据了</text>
		</view>
		<view class="" style="position: fixed;top: 40%;z-index: 999;left: 40%;">
			<u-loading-icon :show="loging" size="60" color="#9EBEFF" mode="semicircle"></u-loading-icon>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return {
				activeTab: 1,
				count: 0,
				count1: 0,
				count2: 0,
				pageSize: 10,
				allData: [],
				isLoading: false,
				noMoreData: false,
				loging: false
			}
		},
		onLoad() {
			this.getData()
			this.getCount()
		},
		onShow() {
			this.getData()
			this.getCount()
		},
		onReachBottom() {
			console.log('触发滚动到底部事件')
			if(this.activeTab == 1){
				if(this.count > this.pageSize){
					this.pageSize += 10
					this.isLoading = true
					this.getData();
				} else{
					this.noMoreData = false
				}
			}else if(this.activeTab == 2){
				if(this.count1 > this.pageSize){
					this.pageSize += 10
					this.isLoading = true
					this.getData();
				} else{
					this.noMoreData = false
				}
			} else{
				if(this.count2 > this.pageSize){
					this.pageSize += 10
					this.isLoading = true
					this.getData();
				} else{
					this.noMoreData = false
				}
			}
			
		},
		methods: {
			getCount(){
				this.$api.request('alarm/listing', {pageNo: 1, pageSize: 1, level: 1}, (res)=>{
					if(res.status == 'ok'){
						this.count = res.count
					}
				})
				this.$api.request('alarm/listing', {pageNo: 1, pageSize: 1, level: 2}, (res)=>{
					if(res.status == 'ok'){
						this.count1 = res.count
					}
				})
				this.$api.request('alarm/listing', {pageNo: 1, pageSize: 1, level: 3}, (res)=>{
					if(res.status == 'ok'){
						this.count2 = res.count
					}
				})
			},
			getData(){
				this.loging = true
				this.$api.request('alarm/listing', {pageNo: 1, pageSize: this.pageSize, level: this.activeTab}, (res)=>{
					this.loging = false
					if(res.status == 'ok'){
						this.allData = res.list
					}else{
						uni.showToast({
							title: res.info,
							icon: 'none'
						})
					}
				})
			},
			changeTab(index) {
				if (this.activeTab === index) return;
				this.activeTab = index;
				this.pageSize = 10; // 重置页码
				this.allData = []; // 清空旧数据
				this.getData()
				this.getCount()
			},
		}
	}
</script>

<style lang="scss" scoped>
	.loading-status {
		text-align: center;
		padding: 60rpx;
		padding-bottom: 100rpx;
		color: #999;
		font-size: 28rpx;
	}
	.miann_app{
		width: 100%;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.box{
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
		.tabs {
			display: flex;
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			justify-content: space-around;
			background-color: #ffffff;
			padding-bottom: 10rpx;
			.tab-item {
				position: relative;
				padding: 20rpx 0;
				flex: 1;
				text-align: center;
				font-size: 30rpx;
				color: #333;
				display: flex;
				justify-content: center;
				align-items: center;
				.active-line {
					position: absolute;
					bottom: 0;
					width: 60rpx;
					height: 6rpx;
					background-color: #00AAFF;
					border-radius: 3rpx;
				}
				
				.badge {
					background-color: #FF4D4F;
					color: white;
					font-size: 24rpx;
					height: 36rpx;
					min-width: 36rpx;
					padding: 0 6rpx;
					border-radius: 18rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-left: 6rpx;
				}
			}
		}
		.active {
			color: #00AAFF;
			font-weight: bold;
		}
		.list_arr{
			width: 100%;
			position: relative;
			top: 45px;
			.list_item{
				width: calc(100% - 64rpx);
				margin: 20rpx auto;
				padding: 20rpx;
				background: #fff;
				border-radius: 10rpx;
				.item_top{
					display: flex;
					align-items: center;
					justify-content: space-between;
					.item_top_title{
						font-size: 32rpx;
						font-weight: 550;
					}
				}
				.item_name{
					font-size: 28rpx;
					margin-top: 10rpx;
				}
				.item_mian{
					font-size: 28rpx;
					margin-top: 10rpx;
				}
			}
		}
		
		
	}
</style>