/** 宽度 **/
.api-box-95{
	width: 95%;
	margin-left: auto;
	margin-right: auto;
}

.api-box-90{
	width: 90%;
	margin-left: auto;
	margin-right: auto;
}

/** 圆角 **/
.api-radius-5{
	border-radius: 5rpx;
}
.api-radius-10{
	border-radius: 10rpx;
}
.api-radius-20{
	border-radius: 20rpx;
}
.api-radius-top-5{
	border-top-left-radius: 5rpx;
	border-top-right-radius: 5rpx;
}
.api-radius-top-10{
	border-top-left-radius: 10rpx;
	border-top-right-radius: 10rpx;
}
.api-radius-top-20{
	border-top-left-radius: 20rpx;
	border-top-right-radius: 20rpx;
}
.api-radius-bottom-5{
	border-bottom-left-radius: 5rpx;
	border-bottom-right-radius: 5rpx;
}
.api-radius-bottom-10{
	border-bottom-left-radius: 10rpx;
	border-bottom-right-radius: 10rpx;
}
.api-radius-bottom-20{
	border-bottom-left-radius: 20rpx;
	border-bottom-right-radius: 20rpx;
}
.api-top-5{
	margin-top: 5rpx;
}
.api-top-10{
	margin-top: 10rpx;
}
.api-top-20{
	margin-top: 20rpx;
}

.api-red{
	color:#ff0000
}

/** 阴影 **/
.api-shadow-ddd{
	box-shadow: 5rpx 5rpx 10rpx 5rpx #dddddd;
}

/** 没有数据的 **/
.api-load-box{
	margin-top: 40rpx;
}

.api-load-more{
	margin-top: 60rpx;
}

/** 没有数据的 **/

/** 按钮 **/
.api-btn{
	width: 95%;
	margin: 40rpx auto;
}
/** 按钮 **/

.api-right{
	margin-left: auto;
}

/**
 * 弹窗
 */
.tui-modal-custom {
	text-align: center;
		
	.tui-prompt-title {
		padding-bottom: 10rpx;
		font-size: 34rpx;
	}
		
	.tui-input__box {
		width: 100%;
		height: 60rpx;
		margin: 20rpx auto 20rpx auto;
	}
		
	.tui-modal-input {
		border-bottom: 1rpx solid #e6e6e6;
		height: 60rpx;
		font-size: 28rpx;
		text-align: center;
	}
	
	.tui-prompt-error{
		font-size: 24rpx;
		color:#ff0000;
		margin: 0 auto 20rpx auto;
	}
	
	.tui-prompt-btn{
		margin-top: 40rpx;
	}
}

.tn-strip{
	width: 100%;
	border-bottom: 20rpx solid #F8F9FB;
}

.tn-strip-min{
	width: 100%;
	border-bottom: 1rpx solid #F8F9FB;
}


/**分享**/
.tui-share {
		background: #e8e8e8;
		position: relative;
		.tui-share-title {
			font-size: 26rpx;
			color: #7E7E7E;
			text-align: center;
			line-height: 26rpx;
			padding: 20rpx 0 50rpx 0;
		}
		
		.tui-share-top,
		.tui-share-bottom {
			min-width: 101%;
			padding: 0 20rpx 0 30rpx;
			white-space: nowrap;
		}
		
		.tui-mt {
			margin-top: 30rpx;
			padding-bottom: 150rpx;
		}
		
		.tui-share-item {
			width: 126rpx;
			height: 300rpx;
			display: inline-block;
			margin-right: 24rpx;
			text-align: center;
		}
		
		.tui-item-last {
			margin: 0;
		}
		
		.tui-empty {
			display: inline-block;
			width: 30rpx;
			visibility: hidden;
		}
		
		.tui-share-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			background: #fafafa;
			height: 126rpx;
			width: 126rpx;
			border-radius: 32rpx;
		}
		
		.tui-share-text {
			font-size: 24rpx;
			color: #7E7E7E;
			line-height: 24rpx;
			padding: 20rpx 0;
			white-space: nowrap;
		}
		
		.tui-btn-cancle {
			width: 100%;
			height: 100rpx;
			position: absolute;
			left: 0;
			bottom: 0;
			background: #f6f6f6;
			font-size: 36rpx;
			color: #3e3e3e;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.tui-hover {
			background: rgba(0, 0, 0, 0.2)
		}
		
}

.share-btn {
	padding: 0 !important;
}

/* 标签内容 start*/
  .tn-tag-content {
    &__item {
      display: inline-block;
      line-height: 45rpx;
      padding: 10rpx 30rpx;
      margin: 20rpx 20rpx 5rpx 0rpx;
      
      &--prefix {
        padding-right: 10rpx;
      }  
    }
  }
  /* 标签内容 end*/
  
  .border {
	border: 1rpx solid red;
  }
.c1 {
	color: #9ebeff;
}
.bg1 {
	background-color:#9ebeff;
}
	