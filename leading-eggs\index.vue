<template>
	<view class="app">
		<!-- 每日准备 -->
		<view class="title">
			每日准备
		</view>
		<view class="input_app">
			<view class="input_info" v-if="statusNow != 'detail'">
				<view class="item_list">
					<view class="lable">
						省市区
					</view>
					<view class="value" @click="showPicker = true">
						<view class="tn-color-gray" v-if="selectedAddress == ''">
							请选择省市区
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="" v-else>
							{{ selectedAddress }}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						街道/村庄
					</view>
					<view class="value" @click="showStreetPickerStatus">
						<view class="tn-color-gray" v-if="selectedStreet == ''">
							请选择街道/小区
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="" v-else>
							{{ selectedStreet }}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						详细位置
					</view>
					<view class="value">
						<input style="text-align: right;" placeholder="请输入详细地址" name="input" placeholder-style="color:#AAAAAA" v-model="formData.address"></input>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						村户数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.depositor"></tn-number-box>
						</view>
					</view>
				</view>
				
				<picker @change="(e) => bindPickerChange(e)" :value="indexChannel" :range="channelSourceArr" range-key="title">
					<view class="item_list">
						<view class="lable">
							营销活动
						</view>
						<view class="value">
							<view class="tn-color-gray" v-if="indexChannel === -1">
								请选择
								<text class="tn-icon-right tn-padding-left-xs"></text>
							</view>
							<view class="" v-else>
								{{ channelSourceArr[indexChannel].title }}
								<text class="tn-icon-right tn-padding-left-xs"></text>
							</view>
						</view>
					</view>
				</picker>
				<view class="item_list">
					<view class="lable">
						<view> 获取经纬度</view>
						<view style="font-size: 20rpx;color: red;" v-if="formData.lat_long">点击重新弄获取</view>
					</view>
					<view class="value">
						<tn-button backgroundColor="tn-main-gradient-blue" size="sm" @click="getGeo">{{ formData.lat_long ? formData.lat_long : '获取经纬度' }}</tn-button>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						<view>周围环境</view>
					</view>
					<view class="value">
						<tn-image-upload ref="imageUpload" @on-remove="removeImg" :action="action" :width="150" :height="150" :formData="formImg" :fileList="fileList" :disabled="disabled" :autoUpload="autoUpload" :maxCount="maxCount" :showUploadList="showUploadList" :showProgress="showProgress" :deleteable="deleteable" :customBtn="customBtn" @sort-list="onSortList" @on-choose-complete="beforeUpload" />
					</view>
				</view>
				<view style="background-color: #FFFFFF;">
					<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min">
						<view class="justify-content-item">
							<text class="lable">备注</text>
						</view>
					</view>
					<view class="tn-color-gray--dark tn-text-justify">
						<view class="tn-bg-gray--light" style="border-radius: 10rpx;margin: 20rpx 0rpx 20rpx 0rpx;">
							<textarea v-model="formData.remarks" maxlength="300" placeholder="请填写备注" placeholder-style="color:#AAAAAA" style="height: 160rpx;padding:30rpx 0 20rpx 30rpx;"></textarea>
						</view>
					</view>
				</view>
			</view>
			<view class="input_info" v-else>
				<view class="item_list">
					<view class="lable">
						省市区
					</view>
					<view class="value">
						<view class="">
							{{ selectedAddress }}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						街道/村庄
					</view>
					<view class="value">
						<view>
							{{ selectedStreet }}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						详细位置
					</view>
					<view class="value">
						{{ formData.address }}
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						村户数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{ formData.depositor }}
						</view>
					</view>
				</view>
				
				<view class="item_list">
					<view class="lable">
						营销活动
					</view>
					<view class="value">
						<view>
							{{ channelSourceArr[indexChannel].title }}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						<view> 获取经纬度</view>
					</view>
					<view class="value">
						{{ formData.lat_long ? formData.lat_long : '未获取经纬度' }}
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						<view>周围环境</view>
					</view>
					<view class="value">
						<!-- <image v-for="(v, i) in fileList" style="width: 150rpx;height: 150rpx;margin: 8rpx;" :src="v.url" mode=""></image> -->
						<tn-image-upload ref="imageUpload" @on-remove="removeImg" :action="action" :width="150" :height="150" :formData="formImg" :fileList="fileList" :disabled="true" :autoUpload="autoUpload" :maxCount="maxCount" :showUploadList="showUploadList" :showProgress="showProgress" :deleteable="false" :customBtn="customBtn" @sort-list="onSortList" @on-choose-complete="beforeUpload" />
					</view>
				</view>
				<view style="background-color: #FFFFFF;">
					<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min">
						<view class="justify-content-item">
							<text class="lable">备注</text>
						</view>
					</view>
					<view class="tn-color-gray--dark tn-text-justify">
						<view class="tn-bg-gray--light" style="border-radius: 10rpx;margin: 20rpx 0rpx 20rpx 0rpx;">
							<view style="height: 160rpx;padding:30rpx 0 20rpx 30rpx;">
								{{ formData.remarks }}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="buton" @click="addView" v-if="statusNow == 'add'"> 签到 </view>
		<view class="buton" style="background: #FBBD12;" @click="editView" v-if="statusNow == 'edit'"> 确认修改 </view>
		<u-picker :show="showStreetPicker" :columns="streetColumns" keyName="name" @confirm="confirmStreet" @cancel="showStreetPicker = false" @change="streetChange" :defaultIndex="treetPicker" :key="pickerKey"/>
		<u-picker :show="showPicker" :columns="columns" keyName="name" @confirm="confirmHandler" @cancel="showPicker = false" @change="changeHandler" :defaultIndex="defaultIndex" :key="pickerKey" />
	</view>
</template>

<script>
import httpApi from '@/config/app.js'
	export default{
		data(){
			return{
				statusNow: 'add',
				showPicker: false,
				showStreetPicker: false,
				streetColumns: [[], []], // 街道列和小区列
				selectedStreet: '',
				currentDistrictCode: null, // 保存当前选择的区级编码
				columns: [[], [], []], // 三级数据容器
				rawData: [],          // 原始接口数据
				selectedAddress: '',   // 选中结果
				channelSourceArr: [],
				defaultIndex: [0, 0, 0],
				treetPicker: [0, 0],
				indexChannel: -1,
				formData: {
					activity_id: '',
					lat_long: '',
					address: '',
					remarks: '',
					province: '',
					city: '',
					area: '',
					county: '',
					village: '',
					depositor: '',
					photo: ''
				},
				formImg: {
					dir: 'profile',
					type: 'image',
				},
				action: httpApi + '/api/mini.main/upload',
				fileList: [],
				disabled: false,
				autoUpload: true,
				maxCount: 9,
				showUploadList: true,
				showProgress: true,
				deleteable: true,
				customBtn: false,
				pickerKey: 0,
				setCode: '',
				isS: 0
			}
		},
		onLoad(e) {
			this.getActivity()
			this.getCity()
			this.statusNow = e.status
			this.isS = e.isS
			console.log(this.formData)
			if(e.id != 0){
				this.formData.id = e.id
				this.detail()
			}
			
		},
		methods: {
			removeImg(index, lists, name){
				console.log(this.$refs.imageUpload)
				console.log(index, lists, name)
			},
			detail(){
				this.$api.request('activityDayWork/detail', {id: this.formData.id}, (res)=>{
					if(res.status == 'ok'){
						this.formData.activity_id = res.data.activity_id
						this.formData.lat_long = res.data.lat_long
						this.formData.address = res.data.address
						this.formData.remarks = res.data.remarks
						this.formData.province = res.data.province
						this.formData.city = res.data.city
						this.formData.area = res.data.area
						this.formData.county = res.data.county
						this.formData.village = res.data.village
						this.formData.depositor = res.data.depositor
						this.formData.photo = res.data.photo
						this.selectedAddress = `${res.data.province}-${res.data.city}-${res.data.area}`;
						this.selectedStreet = `${res.data.county}/${res.data.village}`
						this.indexChannel = this.channelSourceArr.findIndex(item => item.id == res.data.activity_id)
						this.$nextTick(()=>{
							let that = this
							that.rawData.map((v, i) => {
								if(v.name == res.data.province){
									that.defaultIndex[0] = i
									v.children.map((x, y)=>{
										if(x.name == res.data.city){
											that.defaultIndex[1] = y
											x.children.map((x1, y1)=>{
												if(x1.name == res.data.area){
													that.defaultIndex[2] = y1
													this.getParent(x1.code)
												}
											})
										}
									})
								}
							})
							console.log(this.defaultIndex, 'rawData')
						})
						
						if(res.detail.photoarr.length){
							let arrOne = res.detail.photoarr
							let strOne = res.data.photo.split(',')
							for (let i = 0; i < arrOne.length; i++) {
								let objOne = {
									url: arrOne[i],
									postLine: strOne[i]
								}
								this.fileList.push(objOne)
							}
							this.$nextTick(()=>{
								this.$refs.imageUpload.lists.map((item, index) => {
									console.log(index)
									item.postLine = strOne[index]
								})
							})
							
						}
					}
				})
			},
			setTimeIndex(){
				setTimeout(()=>{
					uni.navigateBack(1)
				}, 500)
			},
			handleScan() {
				uni.scanCode({
					onlyFromCamera: true,
					success: res => {
						if (res.errMsg == 'scanCode:ok') {
							console.log(res)
							console.log(res)
							if(res.path.includes('%')){
								uni.navigateTo({
									url: '/' + res.path
								})
							}else {
								let str = res.path.split('scene=')[0] + res.path.split('scene=')[1]
								console.log(str, '我是跳转地址')
								uni.navigateTo({
									url: '/' + str
								})
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: '识别失败，请联系管理员!'
							})	
						}
					},
					fail() {
						console.log('取消识别')
					}
				});
			},
			addView(){
				let arrs = []
				if (this.$refs.imageUpload !== undefined) {
					this.$refs.imageUpload.lists.map(item => {
						arrs.push(item.postLine)
					})
				}
				this.formData.photo = arrs.join(',')
				this.$api.request('activityDayWork/add', this.formData, (res)=>{
					if(res.status == 'ok'){
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						console.log(this.isS)
						if(this.isS){
							this.handleScan()
						} else{
							this.setTimeIndex()
						}
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			editView(){
				let arrs = []
				if (this.$refs.imageUpload !== undefined) {
					this.$refs.imageUpload.lists.map(item => {
						arrs.push(item.postLine)
					})
				}
				this.formData.photo = arrs.join(',')
				
				this.$api.request('activityDayWork/edit', this.formData, (res)=>{
					if(res.status == 'ok'){
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.setTimeIndex()
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			showStreetPickerStatus(){
				if(this.selectedAddress == '') return uni.showToast({ icon: 'none', title: '请先选择省市区' })
				this.showStreetPicker = true
			},
			// 街道选择变化
			streetChange(e) {
			    if (e.columnIndex === 0) {
					const street = e.value[0]
					this.$set(this.streetColumns, 1, street.children || [])
			    }
			},
			// 街道/小区确认
			confirmStreet(e) {
			    const [street, community] = e.value
			    this.selectedStreet = `${street.name}/${community.name}`
			    this.showStreetPicker = false
			    // 获取完整信息
				this.formData.county = street.name
				this.formData.village = community.name
			    console.log({
			        streetCode: street.code,
			        communityCode: community.code
			    })
			},
			formatData(list) {
			    return list.map(item => ({
			        ...item,
					children: item.children || []  // 确保 children 字段存在
			    }))
			},
			
			// 初始化嵌套列数据（核心修复方法）
			initNestedColumns() {
			    // 第一列
			    const provinces = this.formatData(this.rawData)
			    // 第二列（取第一个省的子级）
			    const cities = provinces[0]?.children || []
			    this.$set(this.columns, 1, this.formatData(cities))
			    // 第三列（取第一个市的子级）
			    const districts = cities[0]?.children || []
			    this.$set(this.columns, 2, this.formatData(districts))
			},
			// 联动更新逻辑（优化版）
			changeHandler(e) {
			    const { columnIndex, values } = e
			    // 省份变化时更新市、区
			    if (columnIndex === 0) {
			        const cities = values[0]?.children || []
			        this.$set(this.columns, 1, this.formatData(cities))
			        const districts = cities[0]?.children || []
			        this.$set(this.columns, 2, this.formatData(districts))
			        // 更新默认选中索引
			        this.defaultIndex = [e.indexs[0], 0, 0]
			    }
			    // 市变化时更新区
			    if (columnIndex === 1) {
			        const districts = values[1]?.children || []
			        this.$set(this.columns, 2, this.formatData(districts))
			        this.defaultIndex = [this.defaultIndex[0], e.indexs[1], 0]
			    }
				console.log(this.defaultIndex)
			},
			
			// 确认选择
			confirmHandler(e) {
				const [province, city, district] = e.value;
				this.selectedAddress = `${province.name}-${city.name}-${district.name}`;
				this.showPicker = false;
				this.formData.province = province.name
				this.formData.city = city.name
				this.formData.area = district.name
				// 获取完整编码信息
				console.log({provinceCode: province.code, cityCode: city.code, districtCode: district.code});
				this.getParent(district.code)
			},
			getCity(){
				this.$api.request('common/getNewProcitydisList', {}, (res)=>{
					if(res.status == 'ok'){
						this.rawData = res.list;
						// 初始化第一列（省份）
						this.$set(this.columns, 0, this.formatData(this.rawData))
						this.initNestedColumns()
					}
				})
			},
			// common/getNewStreetviList
			// 获取
			getParent(code){
				this.$api.request('common/getNewStreetviList', {code: code}, (res)=>{
					if(res.status == 'ok'){
						// 格式化街道数据
						const streets = res.list.map(item => ({ ...item, children: item.children || []}))
						// 初始化街道列和小区列
						this.$set(this.streetColumns, 0, streets)
						this.$set(this.streetColumns, 1, streets[0]?.children || [])
						let that = this
						if(this.formData.county){
							res.list.map((v, i)=>{
								if(v.name == that.formData.county){
									that.treetPicker[0] = i
									v.children.map((x, y)=>{
										if(x.name == that.formData.village){
											that.treetPicker[1] = y
											that.pickerKey += 1
										}
									})
								}
							})
							console.log(that.treetPicker, 'treetPicker')
						}
					}
				})
			},
			beforeUpload(file) {
				const token = uni.getStorageSync('token');
				uni.uploadFile({
					url: this.action,
					filePath: file[file.length - 1].url, // 必须使用本地文件路径（file.path）
					name: 'file', // 与后端接口字段一致
					formData: {
						dir: 'profile',
						type: 'image',
					},
					header: {
						'token': token,
						'Content-Type': 'application/json'
					},
					success: (res) => {
						if (res.statusCode === 200) {
							const response = JSON.parse(res.data);
							this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].onLine = response.url
							this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].postLine = response.path
							console.log(this.$refs.imageUpload)
						}
					},
					fail(err) {
						console.log(err)
					}
				});
				return false; // 阻止组件默认上传
			},
			onSortList(files) {
				this.fileList = files; // 直接赋值新数组
			},
			getActivity(){
				this.$api.request('activity/listing', {}, (res)=>{
					if(res.status == 'ok'){
						this.channelSourceArr = res.list
					}
				})
			},
			bindPickerChange(e){
				const index = e.detail.value
				this.indexChannel = index
				this.formData.activity_id = this.channelSourceArr[index].id
			},
			async getGeo(){
				const codes = await this.getLocation()
				if(this.formData.lat_long == codes){
					this.$util.showToast('请勿在相同位置重复点击！')
					return
				}
				this.formData.lat_long = codes
				
				console.log(codes)
			},
			// 获取位置信息
			getLocation() {
				return new Promise(resolve => {
					this.$util.showLoading('正在获取位置')
					uni.getLocation({
						type: 'gcj02', // 坐标格式
						success: (res) => {
							resolve(`${res.longitude.toFixed(6)},${res.latitude.toFixed(6)}`)
						},
						fail: (err) => {
							this.$util.showToast('获取位置失败')
						},
						complete: () => {
							this.$util.hideLoading()
						}
					})
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.buton{
		width: 400rpx;
		height: 70rpx;
		text-align: center;
		margin: 30rpx auto 0;
		line-height: 70rpx;
		border-radius: 40rpx;
		color: #fff;
		background: #01BEFF;
		margin-bottom: 50rpx;
	}
	.app{
		width: 100%;
		.title{
			width: 100%;
			text-align: center;
			font-size: 32rpx;
			font-weight: 550;
			padding: 50rpx 0;
		}
		.input_app{
			width: calc(100% - 64rpx);
			background: #fff;
			padding: 20rpx 0;
			border-radius: 10rpx;
			margin: 0 auto;
			.lable{
				// text-align: center;
				min-width: 170rpx;
			}
			.input_info{
				width: calc(100% - 40rpx);
				margin: 0 auto;
				padding: 10rpx;
				// background-color: plum;
			}
			.item_list{
				display: flex;
				margin-top: 20rpx;
				justify-content: space-between;
			}
		}
	}
</style>