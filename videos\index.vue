<template>
	<view class="video-container">
		<!-- 隐藏原生全屏按钮，使用自定义按钮 -->
		<!-- <video id="myVideo" src="./static/videos.mp4" :show-fullscreen-btn="false" :controls="false" :muted="true" ref="videoRef" -->
			<!-- class="video-player"></video> -->

		<!-- 自定义全屏按钮 -->
		<view class="custom-fullscreen-btn" @click="handleFullscreen">
			<!-- <image src="/static/fullscreen-icon.png" mode="aspectFit"></image> -->
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				videoUrl: 'your-video.mp4',
				isAndroid: false
			};
		},
		mounted() {
			// 判断平台
			uni.getSystemInfo({
				success: (res) => {
					this.isAndroid = res.platform === 'android';
				}
			});
		},
		methods: {
			async handleFullscreen() {
				const video = this.$refs.videoRef;
				// 安卓设备：强制横屏 + 全屏
				if (this.isAndroid) {
					await this.forceLandscape();
					await this.requestFullscreen();
				} else {
					// iOS 依赖原生全屏按钮
					video.play();
				}
			},
			// 强制横屏布局
			forceLandscape() {
				const el = document.getElementById('myVideo');
				el.style.width = '100vh';
				el.style.height = '100vw';
				el.style.transform = 'rotate(90deg) translate(-50%, -50%)';
				el.style.position = 'fixed';
				el.style.top = '50%';
				el.style.left = '50%';
			},
			// 触发浏览器全屏
			requestFullscreen() {
				const dom = document.documentElement;
				const rfs = dom.requestFullscreen || dom.webkitRequestFullScreen;
				if (rfs) rfs.call(dom);
			}
		}
	};
</script>

<style>
	.video-container {
		position: relative;
		width: 100vw;
		height: 100vh;
		overflow: hidden;
	}

	.video-player {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.custom-fullscreen-btn {
		position: absolute;
		bottom: 20px;
		right: 20px;
		z-index: 1000;
	}
</style>