<template>
	<view class="container">
		
		<tn-sticky>
			<view class="header">
				<view class="search-box">
					<text class="tn-icon-search"></text>
					<input v-model="filter.search" class="input" type="text" placeholder="搜索客户姓名/手机号" maxlength="64" confirm-type="search" @confirm="search">
					<text class="button" @tap="search">搜索</text>
				</view>
				<tn-tabs :list="tabList" :isScroll="false" :current="tabCurrent" name="name" @change="tabChange"></tn-tabs>
			</view>
		</tn-sticky>



		<view class="main">
			<template v-if="dataList.length">
				<!-- <view v-for="(item, index) in dataList" class="list-item">
					<view class="list-content">
						<view class="main-info">
							<text class="text">{{ item.flow_custom_name || item.customer_name || item.clue_name }}</text>
							<text class="status" style="margin-right: 10rpx;">{{ item.state || '状态' }}</text>
						</view>
						<view class="shareView" style="font-size: 40rpx;" @click="getShare(item.flow_custom_id)">
							<text class="tn-icon-share-square tn-color-gradient-blue"></text>
						</view>
						<text class="secondary-info">线索名称：{{ item.clue_name }}</text>
						<text class="secondary-info">当前流程：{{ item.full_name }}</text>
						<text class="secondary-info" v-if="filter.type=='wait'">开启时间：{{ item.start_time ? item.start_time : '--' }}</text>
						<text class="secondary-info" v-if="filter.type=='wait'">截止时间：{{ item.end_time ? item.end_time : '--' }}</text>
						<text class="secondary-info" v-if="item.alarm">
							提示信息：
							<text class="tn-color-red tn-text-bold">{{ item.alarm }}</text>
						</text>
						<text class="secondary-info" v-if="item.remark">
							备注信息：
							<text class="tn-color-red tn-text-bold">{{ item.remark }}</text>
						</text>
					</view>
					<view v-if="item.flow_btn.length" class="list-button-wrap">
						<template v-for="(cItem, cIndex) in item.flow_btn">
							<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleButtonClick(index, cIndex)">
								<text :class="cItem.icon"></text>
								<text class="text">{{ cItem.title }}</text>
							</tn-button>

						</template>
					</view>
				</view>
				<tn-load-more :status="loadStatus" :fontSize="28"></tn-load-more> -->
				<!-- 我的待办 -->
				<view v-if="tabCurrent == 0">
					<view v-for="(item, index) in dataList" class="list-item">
						<view class="list-content">
							<view class="main-info">
								<text class="text">{{ item.flow_custom_name || item.clue_name }}</text>
								<text class="status" style="margin-right: 20rpx;">{{ item.state || '状态' }}</text>
							</view>
							<view class="shareView" style="font-size: 40rpx;" @click="getShare(item.flow_custom_id)">
								<text class="tn-icon-share-square tn-color-gradient-blue"></text>
							</view>
							<text class="secondary-info">线索名称：{{ item.clue_name }}</text>
							<text class="secondary-info">当前流程：{{ item.work_name }}</text>
							<text class="secondary-info">开启时间：{{ item.start_time ? item.start_time : '--' }}</text>
							<text class="secondary-info">截止时间：{{ item.end_time ? item.end_time : '--' }}</text>
							<text class="secondary-info" v-if="item.alarm">
								报警信息：
								<text class="tn-color-red tn-text-bold">{{ item.alarm }}</text>
							</text>
							<text class="secondary-info" v-if="item.remark">
								提示信息：
								<text class="tn-text-bold" style="color: #CC851E;">{{ item.remark }}</text>
							</text>
						</view>
						<view v-if="item.flow_btn.length" class="list-button-wrap">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleButtonClick(index, cIndex)">
									<text :class="cItem.icon"></text>
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
						
							</template>
						</view>
					</view>
				</view>
				<!-- 下属待办 -->
				<view v-if="tabCurrent == 1">
					<view v-for="(item, index) in dataList" class="list-item">
						<view class="list-content">
							<view class="main-info">
								<text class="text">{{ item.flow_custom_name || '暂未设置' }}</text>
								<text class="status" style="margin-right: 20rpx;">{{ item.state || '状态' }}</text>
							</view>
							<view class="shareView" style="font-size: 40rpx;" @click="getShare(item.flow_custom_id)">
								<text class="tn-icon-share-square tn-color-gradient-blue"></text>
							</view>
							<text class="secondary-info">线索名称：{{ item.clue_name }}</text>
							<text class="secondary-info">销售：{{ item.real_name }}</text>
							<text class="secondary-info">当前流程：{{ item.work_name }}</text>
							<text class="secondary-info">流程负责人：{{ item.duty_user_name }}</text>
							<!-- <te xt class="secondary-info">流程负责人：{{ v.work_name }}</text> -->
							<text class="secondary-info">开启时间：{{ item.start_time ? item.start_time : '--' }}</text>
							<text class="secondary-info">截止时间：{{ item.end_time ? item.end_time : '--' }}</text>
							<text class="secondary-info" v-if="item.alarm">
								报警信息：
								<text class="tn-color-red tn-text-bold">{{ item.alarm }}</text>
							</text>
							<text class="secondary-info" v-if="item.remark">
								提示信息：
								<text class="tn-text-bold" style="color: #CC851E;">{{ item.remark }}</text>
							</text>
						</view>
						<view v-if="item.flow_btn.length" class="list-button-wrap">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleButtonClick(index, cIndex)">
									<text :class="cItem.icon"></text>
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
							</template>
						</view>
					</view>
				</view>
				<!-- 我的客户 -->
				<view v-if="tabCurrent == 2">
					<view v-for="(item, index) in dataList" class="list-item">
						<view class="list-content">
							<view class="main-info">
								<text class="text">{{ item.flow_custom_name}}</text>
							</view>
							<view class="shareView" style="font-size: 40rpx;" @click="getShare(item.flow_custom_id)">
								<text class="tn-icon-share-square tn-color-gradient-blue"></text>
							</view>
							<view v-for="(v, i) in item.flow" style="margin-top: 10rpx;">
								<view style="font-weight: 550;">
									<text>流程{{ i + 1 }}：</text>
									<text class="secondary-info">流程名称：{{ v.work_name }}</text>
									<text class="secondary-info">负责人：{{ v.real_name ? v.real_name : '--' }}</text>
									<text class="secondary-info">开始时间：{{ v.start_time ? v.start_time : '--'  }}</text>
									<text class="secondary-info">结束时间：{{ v.end_time ? v.end_time : '--'  }}</text>
									<text class="secondary-info" v-if="item.alarm">
										提示信息：<text class="tn-color-red tn-text-bold">{{ item.alarm }}</text>
									</text>
								</view>
							</view>
						</view>
						<view v-if="item.flow_btn.length" class="list-button-wrap">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleButtonClick(index, cIndex)">
									<text :class="cItem.icon"></text>
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
						
							</template>
						</view>
					</view>
				</view>
				<!-- 下属客户 -->
				<view v-if="tabCurrent == 3">
					<view v-for="(item, index) in dataList" class="list-item">
						<view class="list-content">
							<view class="main-info">
								<text class="text">{{ item.flow_custom_name}}</text>
							</view>
							<view class="shareView" style="font-size: 40rpx;" @click="getShare(item.flow_custom_id)">
								<text class="tn-icon-share-square tn-color-gradient-blue"></text>
							</view>
							<view v-for="(v, i) in item.flow" style="margin-top: 10rpx;">
								<view style="font-weight: 550;">
									<text>流程{{ i + 1 }}：</text>
									<text class="secondary-info">流程名称：{{ v.work_name }}</text>
									<text class="secondary-info">负责人：{{ v.real_name ? v.real_name : '--' }}</text>
									<text class="secondary-info">开始时间：{{ v.start_time ? v.start_time : '--'  }}</text>
									<text class="secondary-info">结束时间：{{ v.end_time ? v.end_time : '--'  }}</text>
									<text class="secondary-info" v-if="item.alarm">
										提示信息：<text class="tn-color-red tn-text-bold">{{ item.alarm }}</text>
									</text>
								</view>
							</view>
						</view>
						<view v-if="item.flow_btn.length" class="list-button-wrap">
							<template v-for="(cItem, cIndex) in item.flow_btn">
								<tn-button :backgroundColor="cItem.color" fontColor="#fff" size="sm" @click="handleButtonClick(index, cIndex)">
									<text :class="cItem.icon"></text>
									<text class="text">{{ cItem.title }}</text>
								</tn-button>
						
							</template>
						</view>
					</view>
				</view>
			</template>
			<template v-else>
				<tn-empty mode="data"></tn-empty>
			</template>
		</view>
		<!-- 触底模仿 -->
		
		<!-- 选择节点 -->
		<tn-popup v-model="show" mode="center" borderRadius="20">
			<view class="select_node">
				<view class="select_title" >
					选择分享的节点
				</view>
				<tn-checkbox-group v-model="defaultCheckValue" @change="checkboxGroupChange">
					<view v-for="(item, index) in shareList" :key="index" style="margin-top: 15rpx;">
						<tn-checkbox @change="checkboxChange" :name="item.id">{{item.work_name}}</tn-checkbox>
					</view>
				</tn-checkbox-group>
				<button open-type="share" :data-list="defaultCheckValue" :data-id="share_id" class="custom-btn" v-if="defaultCheckValue.length">分享</button>
				<!-- <view class="custom-btn" @click="handleShare"> 分享</view> -->
			</view>
		</tn-popup>
	</view>
</template>

<script>
	import {
		getProcessList,
		executeOperationRequest
	} from '@/api/process.js'
	import tree from '@/components/tree.vue'
	export default {
		components: { tree },
		data() {
			return {
				share_id: '',
				shareList: [],
				defaultCheckValue: [],
				show: false,
				userInfo: getApp().globalData.userInfo, // 用户信息 
				isRefresh: false, // 是否刷新数据
				loading: true, // 加载标识，骨架屏
				loadMore: false, // 上拉加载控制器
				loadStatus: 'loadmore', // 加载更多组件状态
				// 搜索条件
				filter: {
					search: '', // 搜索关键字
					// type: 'wait', // 分类
					pageNo: 1, // 分页
					pageSize: 10 // 单页数量
				},
				dataList: [], // 数据列表
				// tab列表
				tabList: [{
					name: '我的待办',
					type: 'wait'
				},{
					name: '员工待办',
					type: 'all'
				},{
					name: '我的客户',
					type: 'all'
				},{
					name: '员工客户',
					type: 'all'
				}
				
				],
				tabCurrent: 0 // tab选中
			}
		},
		 
		props: {
			nameKey: {
				type: Number,
				default: 1
			},
			showTrue: {
				type: Number,
				default: 1
			}
		},

		// 监听页面滚动到底部事件
		onReachBottom() {
			this.filter.pageSize += 10
			this.getListAll(this.tabCurrent + 1)
		},
		onShow() {
			this.filter.pageSize += 10
			this.getListAll(this.tabCurrent + 1)
		},
		onLoad() {
			this.filter.pageSize += 10
			this.getListAll(this.tabCurrent + 1)
		},
		mounted() {
			this.getListAll(this.tabCurrent + 1)
		},
		beforeDestroy() {
			// 判断是否需要刷新数据
			if (this.isRefresh) {
				// 需要刷新，修改状态，执行刷新请求
				this.isRefresh = false
				// 请求数据
				this.getList(null, {
					pageNo: 1,
					pageSize: this.filter.pageNo * this.filter.pageSize
				})
			}
			// #ifdef MP-WEIXIN
			const curPages = getCurrentPages()[0]
			if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
				curPages.getTabBar().setData({
					selected: 1 // custom-tab-bar中index.js文件list的数组对应数据下标
				})
			}
			// #endif
		},
		destroyed() {
			// 移除监听事件
			uni.$off('refreshProcess')
			uni.$off('login', this.userLogin)
		},
		onShareAppMessage(res) {
			console.log(res.target.dataset.id)
			console.log(res.target.dataset.list.map(({id}) => id).filter(Boolean).join(','))
			const promise = new Promise((resolve, reject) => {
				// 调用封装后的接口方法
				this.$api.request('work/flowshare', { flow_custom_id: res.target.dataset.id, work_id: res.target.dataset.list.map(({id}) => id).filter(Boolean).join(',') }, (res) => { // 回调函数
					// 成功回调处理
					if (res.status === 'ok') {
						resolve({
							path: `linkWeb/index?id=${res.id}&key=${res.key}` || 'pages/index/index',
							title: res.title || '博笛智家',
							imagePath: res.cover || '',
							scene: 0 // 固定参数
						})
					}
				})
			})
			return {
				path: 'pages/index/index',
				title: '标题',
				imagePath: '',
				scene: 0,
				promise
			}
		},
		methods: {
			triggerShare() {
			      return new Promise((resolve, reject) => {
			        wx.showShareMenu({
			          withShareTicket: true,
			          success: () => {
			            wx.shareAppMessage({
			              ...this.shareConfig,
			              success: (res) => {
			                this.$emit('share-success', res)
			                resolve(res)
			              },
			              fail: (err) => {
			                this.$emit('share-fail', err)
			                reject(err)
			              }
			            })
			          },
			          fail: reject
			        })
			      })
			    },
			async preCheckShare() {
			     if (!this.defaultCheckValue.length) {
			             uni.showToast({
			               title: '请先完成年度任务再分享',
			               icon: 'none',
			               duration: 2000
			             })
			             return
			           }
			           
			           // 手动触发小程序分享
			           try {
			             await this.triggerShare()
			             console.log('分享流程已启动')
			           } catch (err) {
			             console.error('分享失败:', err)
			           }
			},
			handleShare() {
			    if (this.defaultCheckValue.length) {
			        // 触发分享功能
					this.shareContent()
				} else {
					// 显示提示信息
					uni.showToast({ title: '请选择需要分享的节点！', icon: 'none'})
				}
			},
				shareContent() {
					// 通用分享方法（需根据平台适配）
					uni.share({
						provider: 'weixin',
						type: 0,
						title: '分享标题',
						scene: 'WXSceneSession',
						summary: '分享内容描述',
						href: 'https://your-domain.com',
						success: (res) => {
							console.log('分享成功:', res)
						},
						fail: (err) => {
							console.log('分享失败:', err)
						}
					})
				},
			// 选中某个复选框时，由checkbox时触发
			      checkboxChange(e) {
			        //console.log(e);
			      },
			      // 选中任一checkbox时，由checkbox-group触发
			      checkboxGroupChange(e) {
			        // console.log(e);
			      },
			// 获取分享流程 work/share
			getShare(id){
				this.$api.request('work/share', {flow_custom_id: id}, (res)=>{
					if(res.list.length){
						this.defaultCheckValue = []
						this.shareList = res.list
						this.share_id = id
						this.show = true
					}else{
						uni.showToast({
							icon: 'none',
							title: '当前没有可分享的节点'
						})
					}
				})
			},
			// 用户登录和退出事件
			userLogin() {
				// 更新用户信息，重置页面参数
				this.userInfo = getApp().globalData.userInfo
				this.filter.pageNo = 1
				this.filter.searchKey = ''
				this.dataList = []
			},
			// 搜索
			search() {
				// 执行搜索
				this.filter.pageNo = 1
				this.filter.pageSize = 10
				this.getListAll(this.tabCurrent + 1)
			},
			// 类型切换
			tabChange(index) {
				this.tabCurrent = index
				this.filter.type = this.tabList[index].type
				// 执行搜索
				this.filter.pageNo = 1
				this.filter.pageSize = 10
				this.filter.search = ''
				this.getListAll(this.tabCurrent + 1)
			},
			getListAll(type){
				// 我的待办 work/my_wait
				if(type == 1){
					// this.filter.pageNo = 1
					// this.filter.pageSize = 10
					// this.filter.search = ''
					this.$api.request('work/my_wait', this.filter, (res)=>{
						this.dataList = res.list
						console.log(res)
					})
				}
				// 下属待办 work/staff_wait
				if(type == 2){
					this.$api.request('work/staff_wait', this.filter, (res)=>{
						this.dataList = res.list
					})
				}
				// 我的客户 work/my_custom
				if(type == 3){
					this.$api.request('work/my_custom', this.filter, (res)=>{
						this.dataList = res.list
					})
				}
				// 下属客户 work/staff_custom
				if(type == 4){
					this.$api.request('work/staff_custom', this.filter, (res)=>{
						this.dataList = res.list
					})
				}
			},
			// 获取数据
			getList(type, exParams = {}) {
				return new Promise((reslove, reject) => {
					// 下拉刷新，不显示loading
					let custom = {};
					if (type === 'refresh') {
						custom = {
							ShowLoading: false
						}
					}
					let params = JSON.parse(JSON.stringify(this.filter))
					Object.assign(params, exParams)
					getProcessList(params, custom).then(res => {
						if (type === 'refresh') {
							// 下拉刷新，关闭下拉刷新组件
							uni.stopPullDownRefresh()
						}
						if(res.list.length == 0){
							this.loadStatus = 'nomore'
							// 	// 上拉加载关闭
							this.loadMore = false
							return
						}
						if (res) {
							if (type === 'search') {
								// 数据筛选，将页面滚动至顶部
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 0
								})
							}
							if (type === 'loadmore') {
								// 上拉加载，注入新数据
								this.dataList.push(...res.list)
							} else {
								// 赋值
								this.dataList = res.list
							}
							// if (this.loading) {
							// 	setTimeout(() => {
							// 		// 修改骨架屏加载标识为false
									this.loading = true
							// 	}, 300)
							// }
						}
					}).catch(err => {
						console.log(err, 'err')
						if (type === 'refresh') {
							// 下拉刷新，关闭下拉刷新组件
							uni.stopPullDownRefresh()
						}
					})
				})
			},
			// 处理按钮点击
			handleButtonClick(index, btnIndex) {
				const { type, param, path } = this.dataList[index].flow_btn[btnIndex]
				switch (type) {
					case 'tel':
						// 拨打电话
						uni.makePhoneCall({
							phoneNumber: param.tel
						})
						break
					case 'navigate':
						// navigate跳转
						this.$util.navigateTo(path, param)
						break
					case 'redirect':
						// redirect跳转
						this.$util.redirectTo(this.dataList[index].flow_btn[btnIndex].path, param)
						break
					case 'reLaunch':
						// reLaunch跳转
						this.$util.reLaunch(this.dataList[index].flow_btn[btnIndex].path, param)
						break
					case 'switch':
						// switch跳转
						// #ifdef MP-WEIXIN
						// 更新tabbar
						const curPages = getCurrentPages()[0]
						if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
							curPages.getTabBar().changeTab(param.index)
						}
						// #endif
						break
					case 'func':
						// 确认操作
						this.$util.showConfirm(param.desc).then(ret => {
							if (ret === 'confirm') {
								// 执行操作
								this.performOperations(index, btnIndex)
							}
						})
						break
					case 'prompt':
						// 输入并确认操作
						uni.showModal({
							title: param.desc,
							cancelText: '取消',
							cancelColor: '#999',
							confirmText: '确定',
							confirmColor: '#6F87F8',
							editable: true,
							placeholderText: '请输入原因',
							success: (res) => {
								if (res.confirm) {
									if (!res.content) {
										this.$util.showToast('请输入原因')
									}
									// 执行操作
									this.performOperations(index, btnIndex, res.content)
								}
							}
						})
						break
					default:
						this.$util.showToast(`无效的按钮，请检查配置。type=${type}`)
						break
				}
			},
			// 执行操作
			performOperations(index, btnIndex, remark) {
				let {
					api,
					param
				} = this.dataList[index].flow_btn[btnIndex]
				if (remark) {
					param.remark = remark
				}
				executeOperationRequest(api, param).then(res => {
					if (res) {
						if (res.path) {
							this.$util.navigateTo(res.path, res.param)
						} else {
							this.$util.showToast(res.info)
						}
						if (res.del) {
							// 重新请求数据
							this.getList(null, {
								pageNo: 1,
								pageSize: this.filter.pageNo * this.filter.pageSize
							})
						}
					}
				})
			}
		},
		watch: {
			// 数据列表发生变化
			dataList(newVal) {
				
			},
			// 监听 nameKey 的变化
			nameKey(newVal, oldVal) {
				// 上拉加载开启状态下才可以执行请求
				// if (this.loadMore) {
				// 	// 上拉加载关闭，防止滚动页面造成多次执行请求
				// 	this.loadMore = false
				// 	// 设置加载更多组件状态为加载中
				// 	this.loadStatus = 'loading'
				// 	// 请求数据
				// 	this.getList('loadmore')
				// }
				this.filter.pageSize += 10
				this.getListAll(this.tabCurrent + 1)
			},
			showTrue(newVal, oldVal) {
				
				// 重置page
				this.filter.pageNo = 1
				this.filter.pageSize = 10
				// 请求数据
				this.getListAll(this.tabCurrent + 1)
				// // 获取数据
				// this.getList('loadmore')
				// // 监听刷新事件
				// uni.$on('refreshProcess', (res) => {
				// 	// 设置需要刷新数据状态
				// 	this.isRefresh = true
				// })
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 通用样式 */
	.custom-btn {
		width: 100%;
		margin-top: 20rpx;
		background-color: #82B2FF !important; /* 覆盖默认背景色 */
		color: white !important; /* 文字颜色 */
		border-radius: 20px !important; /* 圆角 */
		font-size: 16px;
		// padding: 10px 20px;
		text-align: center;
		border: none !important; /* 去除边框 */
	}
	
	/* 去除按钮点击态效果 */
	.custom-btn::after {
		border: none !important;
	}
	
	/* 禁用默认 hover 效果 */
	button[disabled] {
	  opacity: 0.6;
	}
	.shareView{
		position: absolute;
		width: 40rpx;
		height: 40rpx;
		top: 10rpx;
		right: 10rpx;
	}
	.select_node{
		width: 100%;
		padding: 40rpx 150rpx;
		.select_title{
			font-weight: 550;
			font-size: 32rpx;
			text-align: center;
		}
	}
	.container {
		// 适配自定义tabbar 112rpx是tabbar的高度
		/* #ifdef MP-WEIXIN */
		padding-bottom: 112rpx;
		padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
		/* #endif */

		.header {
			padding: 20rpx 40rpx 10rpx;
			background: #fff;
			box-shadow: 0 0 8rpx 0 rgba(20, 20, 22, 0.08);

			.search-box {
				display: flex;
				align-items: center;
				box-sizing: border-box;
				height: 72rpx;
				background: #F9F9F9;
				border-radius: 12rpx;
				border: 2rpx solid #E8EBF1;

				.tn-icon-search {
					margin-left: 30rpx;
					font-size: 32rpx;
					color: #38383A;
				}

				.input {
					flex: 1;
					margin-left: 20rpx;
					height: 40rpx;
					min-height: 40rpx;
					font-size: 28rpx;
					font-family: PingFang SC, PingFang SC;
					font-weight: bold;
					color: #38383A;
					line-height: normal;
				}

				.button {
					display: flex;
					justify-content: center;
					align-items: center;
					width: 120rpx;
					height: 72rpx;
					font-size: 28rpx;
					font-weight: 500;
					color: $theme-color;
					line-height: normal;
				}
			}
		}

		.main {
			padding: 30rpx 20rpx 0rpx 20rpx;
			.list-item {
				background: #FFFFFF;
				box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
				border-radius: 12rpx;
				position: relative;
				&+.list-item {
					margin-top: 30rpx;
				}

				.list-content {
					padding: 40rpx 40rpx 30rpx;

					.main-info {
						display: flex;
						align-items: center;

						.text {
							flex: 1;
							font-weight: bold;
							font-size: 32rpx;
							color: #3A3B40;
							line-height: 30rpx;
						}

						.status {
							font-size: 28rpx;
							color: #4FD1C5;
							line-height: 28rpx;
						}
					}

					.secondary-info {
						display: block;
						margin-top: 16rpx;
						font-weight: 500;
						font-size: 26rpx;
						color: #74747B;
						line-height: 26rpx;
					}

					.main-info+.secondary-info {
						margin-top: 32rpx !important;
					}
				}

				.list-button-wrap {
					padding: 8rpx 10rpx 24rpx 40rpx;
					border-top: 2rpx solid #E3E6EE;

					::v-deep .tn-btn {
						margin-top: 16rpx;
						margin-right: 24rpx;

						.text {
							margin-left: 8rpx;
						}
					}
				}
			}

			::v-deep .tn-load-more {
				margin-top: 30rpx;
			}

			::v-deep .tn-empty {
				margin-top: 200rpx;
			}
		}
	}
</style>