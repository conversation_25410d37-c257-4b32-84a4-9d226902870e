<template>
	<!-- h5链接 -->
	<view class="box">
		<!-- 顶部 -->
		<view class="headImg">
			<image style="width: 100%;" src="https://bodhi.kaikungroup.com/mini/banner.jpg" mode="widthFix"></image>
		</view>
		<!-- 流程进度 -->
		<!-- 客户基本信息 -->
		<view class="mian_content">
			<!-- 客户基本信息 -->
			<!-- <view class="mian_top">
				<view class="mian_info">
					<view class="mian_title">
						<text class="tn-icon-my tn-main-gradient-indigo--light"></text>
						<text style="margin-left: 10rpx;">客户资料</text>
					</view>
					<view class="user_list">
						<view class="user_item">
							<view class="user_left">
								客户名称：
							</view>
							<view class="user_right">
								王贺
							</view>
						</view>
						<view class="user_item">
							<view class="user_left">
								联系电话：
							</view>
							<view class="user_right">
								18813082663
							</view>
						</view>
						<view class="user_item">
							<view class="user_left">
								服务地址：
							</view>
							<view class="user_right">
								北京市顺义区机场航站楼220号三号厅
							</view>
						</view>
					</view>
				</view>
			</view> -->
			<view class="mian_top" style="margin-top: 20rpx;" v-for="(v, i) in list" :key="i">
				<view class="mian_info">
					<view class="mian_title">
						<text class="tn-icon-my tn-main-gradient-indigo--light"></text>
						<text style="margin-left: 10rpx;">服务信息</text>
					</view>
					<view class="user_list">
						<view class="user_item">
							<view class="user_left"> 负责人：</view>
							<view class="user_right">{{ v.real_name ? v.real_name : '--' }}</view>
						</view>
						<view class="user_item">
							<view class="user_left">当前状态： </view>
							<view class="user_right">{{ v.state ? v.state : '--' }}</view>
						</view>
						<view class="user_item">
							<view class="user_left">当前节点： </view>
							<view class="user_right"> {{ v.full_name }} </view>
						</view>
					</view>
				</view>
				<view class="mian_info" style="margin-top: 40rpx;">
					<view class="mian_title">
						<text class="tn-icon-my tn-main-gradient-indigo--light"></text>
						<text style="margin-left: 10rpx;">服务细节</text>
					</view>
					<view class="content_info">
						<view class="info_title">
							{{ v.full_name }}
						</view>
						<view class="info_con">
							<view class="info_list" v-for="(item, index) in v.children" :key="index">
								<view class="info_list_item" v-if="['text', 'number', 'finance', 'date', 'datetime', 'template', 'radio', 'geo', 'disable', 'duration'].indexOf(item.form_type) !== -1">
									<text class="label">{{ item.work_name }}：</text>
									<text class="text selected">{{ item.val ? item.val : item.val_file }}</text>
								</view>
								<view class="info_list_item" v-if="item.form_type === 'textarea'">
									<text class="label">{{ item.work_name }}：</text>
									<text class="text selected">{{ item.val ? item.val : item.val_file }}</text>
								</view>
								<view class="info_list_item" v-if="item.form_type === 'image'" >
									<view class="item_title">{{ item.work_name }}</view>
									<view class="item_img">
										<image v-for="(sItem, sIndex) in item.val_file" class="imgs" :src="sItem" mode="aspectFill" @tap="previewMedia(item.val_file, sIndex, 'image')"></image>
									</view>
								</view>
								<view class="info_list_item" v-if="item.form_type === 'video'" >
									<view class="item_title">{{ item.work_name }}</view>
									<view class="item_img">
										<view v-for="(sItem, sIndex) in item.val_file" class="video-item" @tap="previewMedia(item.val_file, sIndex, 'video')">视频{{ sIndex + 1 }}</view>
									</view>
								</view>
								<!-- <view class="info_list_item" v-if="['file'].indexOf(item.form_type) !== -1" >
									<view class="item_title">{{ item.work_name }}</view>
									<view class="item_img">
										<image v-for="(sItem, sIndex) in item.val_file" class="file-icon" :src="item.icon[sIndex]" mode="aspectFill" @click="previewPDF(sItem)"></image>
									</view>
								</view> -->
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import httpApi from '@/config/app.js'
	export default{
		data(){
			return {
				imgUrl: {},
				dataObj: {},
				list: []
			}
		},
		onLoad(e) {
			this.imgUrl = httpApi + '/mini/banner.jpg'
			this.dataObj.id = e.id
			this.dataObj.key = e.key
			console.log(this.dataObj, '我是新请求')
			this.getData()
		},
		onShow() {
			
		},
		methods: {
			// 预览多媒体
			previewMedia(list, index, type) {
				if (type === 'image') {
					uni.previewImage({
						current: index,
						urls: list
					})
				}
				if (type === 'video') {
					wx.previewMedia({
						sources: [{
							url: list[index], // 视频的 URL
							type: 'video', // 设置类型为视频
						}]
					})
				}
			},
			async previewPDF(url){
				const pdfUrl = url;
				console.log(pdfUrl);
				 try {
				        // 步骤1：下载文件
						const res = await uni.downloadFile({
						url: pdfUrl
					});
					console.log(res[1].tempFilePath);
					
				        // 步骤2：打开文档
				        await uni.openDocument({
				          filePath: res[1].tempFilePath,
				          success: () => {
				            console.log('打开文档成功');
				          }
				        });
				      } catch (error) {
				        console.log('预览失败:', error);
				      }
			},
			getData(){
				// this.$api.request('')
				// api/share.work/flow
				uni.request({
					url: httpApi + '/api/share.work/flow',
					method: 'POST',
					header: {
					  'Content-Type': 'application/json'
					},
					data: this.dataObj,
					success: (res) => {
						// console.log(res)
						this.list = res.data.flow
						
					},
					fail: (err) => reject(err)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.box{
		width: 100%;
		height: 100vh;
		.headImg{
			width: 100%;
		}
		.mian_content{
			width: calc(100% - 64rpx);
			margin: 0 auto;
			padding: 20rpx 0 40rpx;
			.mian_top{
				width: 100%;
				padding: 40rpx 0;
				background-color: #fff;
				border-radius: 10rpx;
				.mian_info{
					width: calc(100% - 48rpx);
					margin: 0 auto;
					.mian_title{
						font-size: 28rpx;
						padding-bottom: 20rpx;
						border-bottom: 1rpx solid #aaa;
					}
					.user_list{
						padding-top: 15rpx;
						font-size: 26rpx;
						.user_item{
							display: flex;
							align-items: center;
							gap: 10rpx;
							margin-top: 10rpx;
						}
					}
					.content_info{
						width: calc(100% - 24rpx);
						margin: 0 auto;
						margin-top: 30rpx;
						.info_title{
							font-size: 24rpx;
						}
						.info_con{
							width: 100%;
							margin-top: 10rpx;
							background: #ecf5ff;
							padding-bottom: 30rpx;
							border-radius: 10rpx;
							.info_list{
								width: calc(100% - 24rpx);
								margin: 0 auto;
								.info_list_item{
									padding-top: 20rpx;
									.item_title{
										font-size: 22rpx;
										
									}
									.item_img{
										margin-top: 10rpx;
										display: flex;
										gap: 15rpx;
										align-items: center;
										flex-wrap: wrap;
										.imgs{
											min-width: 100rpx;
											min-height: 100rpx;
										}
									}
								}
							}
						}
					}
				}
			}
		}
	}



.label {
		width: 200rpx;
		font-size: 28rpx;
		color: #3A3B40;
		line-height: normal;
	}
	
	.text {
		flex: 1;
		font-size: 28rpx;
		color: #BBBEC6 !important;
		line-height: normal;
		text-align: right;
	
		&.selected {
			color: #555555;
		}
	}
	.video-item {
		width: 100rpx;
		height: 100rpx;
		margin: 0 0 8px 8px;
		padding: 4rpx 0;
		background: #6FB2E2;
		border-radius: 8rpx;
		font-size: 28rpx;
		font-weight: 400;
		color: #FFFFFF;
		line-height: normal;
		text-align: center;
	}
</style>