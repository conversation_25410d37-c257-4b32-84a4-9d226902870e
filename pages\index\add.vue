<template>
	<view class="container">
		<view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
			<view class="justify-content-item tn-flex-1">
				<view class="tn-text-bold tn-text-lg">
					客户编码 <text class="tn-color-red tn-padding-left-xs">*</text>
				</view>
				<view class="tn-color-gray tn-padding-top-xs tn-color-black">
					<input v-model="code" placeholder="请输入客户编号(GC开头的),多个使用,分隔" name="input"
						placeholder-style="color:#AAAAAA" value=""></input>
				</view>
			</view>
			<view class="justify-content-item tn-text-xl tn-color-grey tn-margin-left">
				<!-- <view class="tn-icon-right tn-padding-top"></view> -->
			</view>
		</view>

		<view>
			<picker v-if="classifyList.length>0" @change="bindClassifyChange" :value="classifyIndex" range-key="Title"
				:range="classifyList">
				<view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
					<view class="justify-content-item tn-flex-1">
						<view class="tn-text-bold tn-text-lg">
							紧急分类 <text class="tn-color-red tn-padding-left-xs">*</text>
						</view>
						<view class="tn-color-gray tn-padding-top-xs" v-if="classifyIndex===''">
							请选择紧急分类
						</view>
						<view class="tn-color-333 tn-padding-top-xs" v-else>
							{{classifyList[classifyIndex].Title}}
						</view>
					</view>
					<view class="justify-content-item tn-text-lg tn-color-grey tn-margin-left">
						<view class="tn-icon-right tn-padding-top"></view>
					</view>
				</view>
			</picker>

			<picker v-if="classList.length>0" @change="bindClassChange" :value="classIndex" range-key="Label"
				:range="classList">
				<view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
					<view class="justify-content-item tn-flex-1">
						<view class="tn-text-bold tn-text-lg">
							问题分类 <text class="tn-color-red tn-padding-left-xs">*</text>
						</view>
						<view class="tn-color-gray tn-padding-top-xs" v-if="classIndex===''">
							请选择问题分类
						</view>
						<view class="tn-color-333 tn-padding-top-xs" v-else>
							{{classList[classIndex].Label}}
						</view>
					</view>
					<view class="justify-content-item tn-text-lg tn-color-grey tn-margin-left">
						<view class="tn-icon-right tn-padding-top"></view>
					</view>
				</view>
			</picker>
			
			<picker v-if="describeList.length>0" @change="bindDescribeChange" :value="describeIndex" range-key="Label"
				:range="describeList">
				<view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
					<view class="justify-content-item tn-flex-1">
						<view class="tn-text-bold tn-text-lg">
							问题说明 <text class="tn-color-red tn-padding-left-xs">*</text>
						</view>
						<view class="tn-color-gray tn-padding-top-xs" v-if="describeIndex===''">
							请选择问题
						</view>
						<view class="tn-color-333 tn-padding-top-xs" v-else>
							{{describeList[describeIndex].Label}}
						</view>
					</view>
					<view class="justify-content-item tn-text-lg tn-color-grey tn-margin-left">
						<view class="tn-icon-right tn-padding-top"></view>
					</view>
				</view>
			</picker>

			<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding">
				<view class="tn-flex justify-content-item">
					<view class="tn-text-bold tn-text-lg">
						解决建议
					</view>
				</view>
			</view>
			<view class="tn-bg-gray--light tn-padding" style="border-radius: 10rpx;margin: 0 30rpx 30rpx 30rpx;">
				<textarea v-model="advice" maxlength="500" placeholder="请输入解决建议" placeholder-style="color:#AAAAAA"
					style="height: 160rpx;width: 100%;"></textarea>
			</view>


			<!-- 按钮-->
			<view class="tn-flex tn-padding">
				<view class="tn-flex-1 justify-content-item tn-margin-left-xs tn-text-center">
					<tn-button backgroundColor="tn-cool-bg-color-5" padding="40rpx 0" width="100%" :fontSize="28"
						fontColor="#FFFFFF" shape="radius" @click="add">
						<text class="">提 交</text>
					</tn-button>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				customList: [{
					Id: 1,
					RealName: "张三",
					Code: 'GJ2024010236589',
					Capacity: 20.36,
					Town: '木林镇',
					Village: '木林村'
				}],
				classifyIndex: 0,
				classifyList: [{
					Id: 1,
					Title: '提示'
				}, {
					Id: 2,
					Title: '一般'
				}, {
					Id: 3,
					Title: '紧急'
				}],
				classIndex: 0,
				classList: [{
					Id: 1,
					Title: '低发'
				}, {
					Id: 2,
					Title: '超发'
				}, {
					Id: 3,
					Title: '排查'
				}],
				describeIndex: 0,
				describeList: [],
				code: '',
				advice: '', //备注
				btnDisabled: false,
			}
		},
		onLoad() {
			this.getOption();
		},
		methods: {
			bindClassifyChange: function(e) {
				this.classifyIndex = e.detail.value
			},
			bindClassChange: function(e) {
				this.classIndex = e.detail.value
			},
			bindDescribeChange: function(e) {
				this.describeIndex = e.detail.value
			},
			//加载信息
			getOption() {
				let that = this;
				let data = {};
				data.Options = 'Describe,Class';

				//加载客户维护配置
				that.$api.request('Maintain/MaintainOption', data, function(res) {
					if (res.status == 'ok') {
						that.describeList = res.option.Describe;
						that.classList = res.option.Class;
					} else {
						//错误提示
						that.$api.toast(res.info);
					}
				});
			},
			add() {
				let that = this;
				// 提示层
				that.$api.showLoading("正在提交信息");
				// 禁用按钮，防止反复提交
				that.btnDisabled = true;

				//检测指定参数是否都填写
				let dataArray = [];
				dataArray = [
					[that.code, '客户编码'],
					[that.advice, '建议']
				];
				
				// 一种写法
				// that.$api.valid(dataArray,function(res){
				// 	that.$api.hideLoading();
				// 	//验证失败
				// 	if (res.status==false){
				// 		//验证失败
				// 		console.log("验证失败了哦，信息为：" + res.info);
				// 		// 这里写其他逻辑
				// 		return false;
				// 	}else if(res.status === true){
				// 		// 验证成功
				// 		// 这里写其他逻辑
				// 	}
				// })
				// 注意：第一种写法时，后面不应该有逻辑代码了
				
				// 另一种写法
				if (that.$api.valid(dataArray)){
					// 验证成功
					//构建参数
					let data = {};
					data.Code = that.code;
					data.Advice = that.advice;
					
					that.$api.request('Maintain/AddOk', data, function(data) {
						that.$api.hideLoading();
						if (data.status == 'ok') {
							// 处理请求成功
							that.$api.toast(data.info);
							setTimeout(() => {
								that.btnDisabled = false;
								that.$api.navigateBack();
							}, 2000);
						} else {
							// 处理失败
							that.$api.toast(data.info);
							that.btnDisabled = false;
						}
					})
				}else{
					//验证失败
					console.log("这里验证失败了");
					that.btnDisabled = false;
					that.$api.hideLoading();
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.custom-select-box {
		margin: 10rpx auto;
		width: 100%;

		&_empty {
			height: 100rpx;
			line-height: 100rpx;
			text-align: center;
			color: #00aaff;
			border-bottom: 10rpx solid #EEEEEE;
		}
	}

	.custom-list {
		margin-top: 168rpx;
		padding-bottom: 30rpx;

	}

	.custom-box {
		background-color: #FFFFFF;
		border-radius: 10rpx;
		padding: 30rpx;
		box-shadow: 0 0 10rpx 5rpx rgba(0, 0, 0, 0.1);
		margin: 20rpx 20rpx 0 20rpx;
	}

	.tn-classify {
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		z-index: 10;
		width: 0rpx;
		height: 0rpx;

		&__top-right {
			top: 0;
			right: 20rpx;
			border-top: 70rpx solid;
			border-left: 70rpx solid transparent;
			border-top-color: #ff0000 !important;
			border-bottom-color: #ff0000 !important;
		}
	}

	.add-more {
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		color: #00aaff;
		border-bottom: 10rpx solid #EEEEEE;
	}
</style>