// 流程api
const http = uni.$u.http

// 获取流程列表
export const getProcessList = (params, custom = {}) => {
	return http.post('api/mini.work/listing', {
		...params
	}, {
		custom
	})
}

// 执行操作请求
export const executeOperationRequest = (url, params) => {
	return http.post(`api/mini.${url}`, {
		...params
	})
}

// 获取工作详情
export const getWorkDetails = (params) => {
	return http.post('api/mini.work/workdetail', {
		...params
	})
}

// 上传文件
export const uploadFile = (params) => {
	return http.upload('api/mini.main/upload', {
		...params
	})
}

// 提交工作
export const submitWork = (params) => {
	return http.post('api/mini.work/worksubmit', {
		...params
	})
}

// 驳回工作
export const rejectWork = (params) => {
	return http.post('api/mini.work/workabort', {
		...params
	})
}

// 获取客户资料
export const getCustomerInfo = (params) => {
	return http.post('api/mini.work/profile', {
		...params
	})
}

export const submitInfoSet = (params) => {
	return http.post('api/mini.work/profileedit', {
		...params
	})
}

// 获取客户流程列表
export const getCustomerWorkList = (params) => {
	return http.post('api/mini.work/flow', {
		...params
	})
}

// 获取客户流程详情
export const getCustomerWorkDetails = (params) => {
	return http.post('api/mini.work/flowdetail', {
		...params
	})
}

// 获取职能列表
export const getFunctionalList = (params) => {
	return http.post('api/mini.work/dutylisting', {
		...params
	})
}

// 设置职能负责人
export const setFunctionalPerson = (params) => {
	return http.post('api/mini.work/dutyset', {
		...params
	})
}

// 清空职能负责人
export const cleraFunctionalPerson = (params) => {
	return http.post('api/mini.work/dutyclear', {
		...params
	})
}

// 跟换职能负责人
export const changeFunctionalPerson = (params) => {
	return http.post('api/mini.work/dutychange', {
		...params
	})
}

// 提交工期设置
export const submitDurationSet = (params) => {
	return http.post('api/mini.work/workduration', {
		...params
	})
}

// 设置职能负责人
export const dutyset = (params) => {
	return http.post('api/mini.EstateWork/dutyset', {
		...params
	})
}

// 跟换职能负责人
export const dutychange = (params) => {
	return http.post('api/mini.EstateWork/dutychange', {
		...params
	})
}