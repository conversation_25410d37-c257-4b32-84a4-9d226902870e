<template>
    <view class="container">
        <view class="form-group">
            <!-- 跟进方式 -->
            <view class="form-item">
                <view class="form-header">
                    <text class="label">跟进方式</text>
                    <view class="form-control">
                        <view class="radio-group">
                            <view v-for="(item, idx) in followupMethodArr" :key="idx" 
                                class="radio-option"
                                :class="[form.followup_method === item.value ? 'active' : '']"
                                @click="handleFollowupMethodChange(item.value)">
                                {{ item.label }}
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 跟进状态 -->
            <view class="form-item">
                <view class="form-header">
                    <text class="label">跟进状态<text class="required">*</text></text>
                    <view class="form-control">
                        <view class="radio-group">
                            <view v-for="(item, idx) in getFollowupStatusArr()" :key="idx" 
                                class="radio-option"
                                :class="[form.followup_status === item.value ? 'active' : '']"
                                @click="form.followup_status = item.value">
                                {{ item.label }}
                            </view>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 照片上传 -->
            <view class="form-item">
                <view class="form-header">
                    <text class="label">
                        <text v-if="form.followup_method === 'meeting'">见面照片</text>
                        <text v-else>沟通照片</text>
                        <text v-if="form.followup_method === 'meeting'" class="required">*</text>
                    </text>
                </view>
                <view class="upload-box">
                    <tn-image-upload ref="imageUpload" 
                        :action="action" 
                        :width="200" 
                        :height="200"
                        :formData="formData" 
                        :fileList="fileList" 
                        :disabled="disabled" 
                        :autoUpload="autoUpload"
                        :maxCount="maxCount" 
                        :showUploadList="showUploadList" 
                        :showProgress="showProgress"
                        :deleteable="deleteable" 
                        :customBtn="customBtn" 
                        @sort-list="onSortList"
                        @on-choose-complete="beforeUpload" />
                </view>
            </view>

            <!-- 见面场所（仅见面方式必填） -->
            <view v-if="form.followup_method === 'meeting' && meeting_placeArr.length" class="form-item">
                <view class="form-header">
                    <text class="label">见面场所<text class="required">*</text></text>
                    <view class="form-control">
                        <picker @change="handleMeetingPlaceChange" 
                            :value="meeting_placeIndex" 
                            :range="meeting_placeArr"
                            range-key="name">
                            <view class="picker-value">
                                {{ meeting_placeIndex !== -1 ? meeting_placeArr[meeting_placeIndex].name : "请选择" }}
                                <text class="tn-icon-right"></text>
                            </view>
                        </picker>
                    </view>
                </view>
            </view>

            <!-- 下次跟进时间 -->
            <view class="form-item">
                <view class="form-header">
                    <text class="label">下次跟进时间</text>
                    <view class="form-control">
                        <view class="picker-value" @tap="showPicker">
                            {{ result || "请选择" }}
                            <text class="tn-icon-right"></text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 跟进内容 -->
            <view class="form-item textarea-item">
                <view class="form-header">
                    <text class="label">跟进内容<text class="required">*</text></text>
                    <view class="form-control">
                        <textarea class="textarea" 
                            v-model="form.remarks" 
                            maxlength="300" 
                            placeholder="请填写跟进内容"
                            placeholder-style="color:#AAAAAA"></textarea>
                    </view>
                </view>
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section" v-if="!isDetail">
            <button class="submit-btn" @click="submit">添加跟进</button>
        </view>

        <tn-picker mode="time" v-model="show" :params="params" :startYear="startYear" :endYear="endYear"
            @confirm="handleConfirmDate"></tn-picker>
    </view>
</template>

<script>
import index from "../../tuniao-ui";
import template_page_mixin from "@/components/template_page_mixin.js";
import httpApi from "@/config/app.js";
export default {
    name: "TemplateCost",
    mixins: [template_page_mixin],
    data() {
        return {
            params: {
                year: true,
                month: true,
                day: true,
                hour: false,
                minute: false,
                second: false,
            },
            meeting_placeArr: [],
            meeting_placeIndex: -1,
            form: {
                followup_method: "",
                followup_status: "",
                meeting_place: "",
                remarks: "",
                sketch: "",
                crm_clue_id: "",
            },
            followupMethodArr: [
                { label: "电话", value: "call", ext_value: 1 },
                { label: "见面", value: "meeting", ext_value: 2 },
            ],
            allFollowupStatusArr: [
                { label: "有效", value: "valid", ext_value: 1 },
                { label: "无效", value: "invalid", ext_value: 2 },
                { label: "未接通", value: "unanswered", ext_value: 3 },
                { label: "达成合作", value: "ok", ext_value: 10 },
            ],
            meetingPlaceArr: [
                { label: "客户家", value: "home" },
                { label: "门店", value: "store" },
                { label: "总部", value: "headquarters" },
            ],
            indexMeetingPlace: -1,
            action: httpApi + "/api/mini.main/upload",
            formData: {
                type: "customer",
                file_type: 1,
            },
            fileList: [],
            disabled: false,
            autoUpload: true,
            maxCount: 5,
            showUploadList: true,
            showProgress: true,
            deleteable: true,
            customBtn: false,
            vuex_custom_bar_height: 1,
            result: "",
            show: false,
            startYear: new Date().getFullYear(),
            endYear: new Date().getFullYear() + 10,
            dateParams: {
                yearInterval: 1,
                monthInterval: 1,
                dayInterval: 1,
                format: "yyyy-MM-dd",
            },
            listArr: [],
            imgArr: [],
        };
    },
    onLoad(e) {
        this.getType();
        this.form.crm_clue_id = e.id;
    },
    methods: {
        // 获取列表
        getType() {
            this.$api.request("common/dictionaryType", {  }, (res) => {
                console.log("res:", res);
                this.$api.request( "common/dictionaryTypeList", { type: 6 }, (res1) => {
                        console.log("res1:", res1.info);
                        this.listArr = res1.info;
                        res1.info.map((item) => {
                            if (item.ext_name == "jianmianchangsuo") {
                                this.meeting_placeArr = item.children;
                            }
                        });
                        console.log(this.meeting_placeArr, "meeting_placeArr");
                    }
                );
            });
        },
        // 添加更近 api/mini.customerFollowup/add
        handleFollowupMethodChange(method) {
            this.form.followup_method = method;
            if (method === "meeting") {
                this.form.followup_status = "";
            }
        },
        getFollowupStatusArr() {
            if (this.form.followup_method === "meeting") {
                return this.allFollowupStatusArr.filter(
                    (item) => item.value !== "unanswered"
                );
            }
            return this.allFollowupStatusArr;
        },
        handleMeetingPlaceChange(e) {
            const index = e.detail.value;
            this.indexMeetingPlace = index;
            this.meeting_placeIndex = index;
            this.form.meeting_place = this.meeting_placeArr[index].ext_value;
        },
        showPicker() {
            this.openPicker();
        },

        // 打开Picker
        openPicker() {
            this.show = true;
        },

        handleConfirmDate(value) {
            //打印
            this.result = value.year + "-" + value.month + "-" + value.day;
            this.show = false;
        },
        hidePicker() {
            this.show = false;
        },
        getExtValueByName(arr, value) {
            const item = arr.find((it) => it.value === value);
            return item?.ext_value;
        },
        submit() {
            // img   JSON.str name_path
            let arrs = [];
            if (this.$refs.imageUpload !== undefined) {
                for (let i = 0; i < this.$refs.imageUpload.lists.length; i++) {
                    if (this.$refs.imageUpload.lists[i].progress == 100) {
                        // let objs = {
                        //     name_path: this.$refs.imageUpload.lists[i].postLine,
                        // };
                        let str = this.$refs.imageUpload.lists[i].postLine
                        arrs.push(str);
                    }
                }
            }
            if (!this.form.remarks.trim()) {
                uni.showToast({ title: "请填写跟进内容", icon: "none" });
                return;
            }
            let obj = {
                follow_way: this.getExtValueByName( this.followupMethodArr, this.form.followup_method),
                follow_state: this.getExtValueByName( this.allFollowupStatusArr, this.form.followup_status ),
                content: this.form.remarks,
                followup_img: JSON.stringify(arrs),
                next_followup_time: this.result,
                id: this.form.crm_clue_id,
            };
            if (this.form.followup_method === "meeting") {
                if (arrs.length === 0) {
                    uni.showToast({ title: "请上传见面照片", icon: "none" });
                    return;
                }
                if (!this.form.meeting_place) {
                    uni.showToast({ title: "请选择见面场所", icon: "none" });
                    return;
                } else {
                    obj.followup_venue = this.meeting_placeArr[this.meeting_placeIndex].ext_value;
                    
                }
            }
            this.$api.request("partner/followup", obj, (res) => {
                console.log(res);
                if (res.status == "ok") {
                    uni.showToast({
                        title: "提交成功",
                        icon: "success",
                    });
                    const timer = setTimeout(() => {
                        uni.navigateBack({
                            delta: 1,
                            animationType: "pop-in",
                            animationDuration: 500,
                        });
                        clearTimeout(timer);
                    }, 500);
                } else {
                    uni.showToast({
                        icon: "none",
                        title: res.info,
                    });
                }
            });
        },
        onSortList(files) {
            this.fileList = files;
        },
        beforeUpload(file) {
            // const token = "Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM";
            const token = uni.getStorageSync("token");
            uni.uploadFile({
                url: this.action,
                filePath: file[file.length - 1].url,
                name: "file",
                formData: {
                    dir: "profile",
                    type: "image",
                },
                header: {
                    token: token,
                    "Content-Type": "application/json",
                },
                success: (res) => {
                    if (res.statusCode === 200) {
                        const response = JSON.parse(res.data);
                        this.$refs.imageUpload.lists[
                            this.$refs.imageUpload.lists.length - 1
                        ].onLine = response.url;
                        this.$refs.imageUpload.lists[
                            this.$refs.imageUpload.lists.length - 1
                        ].postLine = response.path;
                    }
                },
            });
            return false;
        },
    },
};
</script>

<style lang="scss" scoped>
.container {
    padding: 30rpx;
    background-color: #f8f8f8;
    min-height: 100vh;
}

.form-group {
    margin-bottom: 40rpx;
    background: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
        transform: translateY(-2rpx);
        box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
    }
}

.form-item {
    display: flex;
    flex-direction: column;
    padding: 32rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .form-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    &.textarea-item {
        .form-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 20rpx;

            .label {
                width: 100%;
            }

            .form-control {
                width: 100%;
            }
        }
    }
}

.label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.required {
    color: #f56c6c;
    margin-left: 4rpx;
}

.form-control {
    flex: 1;
    margin-left: 20rpx;
}

.radio-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.radio-option {
    padding: 12rpx 24rpx;
    border-radius: 6rpx;
    font-size: 26rpx;
    color: #666;
    background-color: #f5f7fa;
    transition: all 0.3s ease;

    &.active {
        color: #fff;
        background-color: #409eff;
    }
}

.picker-value {
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 20rpx;
    font-size: 26rpx;
    color: #666;
    text-align: right;
}

.textarea {
    width: 100%;
    height: 200rpx;
    padding: 20rpx;
    line-height: 1.5;
    text-align: left;
    background: #f8f8f8;
    border-radius: 12rpx;
    font-size: 26rpx;
}

.upload-box {
    margin-top: 20rpx;
}

.submit-section {
    padding: 40rpx 30rpx;
    margin-top: 30rpx;
}

.submit-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #409eff;
    color: #fff;
    border-radius: 40rpx;
    font-size: 28rpx;
    transition: all 0.3s ease;

    &:active {
        opacity: 0.8;
    }
}

button[disabled],
input[disabled],
textarea[disabled],
picker[disabled] {
    opacity: 0.6;
    cursor: not-allowed;
}
</style>
