<template>
	<view class="container">
		<tn-sticky>
			<tn-tabs :list="tabList" :isScroll="false" :current="tabCurrent" name="name" @change="tabChange"></tn-tabs>
		</tn-sticky>

		<view class="main">
			<template v-if="dataList.length">
				<view v-for="(item, index) in dataList" class="list-item">
					<view class="list-content">
						<view class="main-info">
							<text class="text">{{ item.customer_name || '客户名称' }}</text>
							<text class="status">{{ item.state || '状态' }}</text>
						</view>
						<text class="secondary-info">线索名称：{{ item.clue_name }}</text>
						<text class="secondary-info">建筑面积：{{ item.area }}</text>
						<text class="secondary-info">当前流程：{{ item.full_name }}</text>
					</view>
				</view>
				<tn-load-more :status="loadStatus" :fontSize="28"></tn-load-more>
			</template>
			<template v-else>
				<tn-empty mode="data"></tn-empty>
			</template>
		</view>
	</view>
</template>

<script>
	import {
		getProcessList
	} from '@/api/process.js'

	export default {
		data() {
			return {
				userInfo: getApp().globalData.userInfo, // 用户信息
				isRefresh: false, // 是否刷新数据
				loading: true, // 加载标识，骨架屏
				loadMore: false, // 上拉加载控制器
				loadStatus: 'loadmore', // 加载更多组件状态
				// 搜索条件
				filter: {
					type: 'all', // 分类
					pageNo: 1, // 分页
					pageSize: 10 // 单页数量
				},
				dataList: [], // 数据列表
				// tab列表
				tabList: [{
					name: '预警',
					type: 'all'
				}, {
					name: '报警',
					type: 'wait'
				}, {
					name: '超时',
					type: 'finish'
				}],
				tabCurrent: 0 // tab选中
			}
		},
		// 小程序点击分享
		onShareAppMessage() {
			return {
				title: '博迪智家',
				path: '/pages/main/main'
			}
		},
		// 监听页面下拉刷新动作
		onPullDownRefresh() {
			// 重置page
			this.filter.pageNo = 1
			// 请求数据
			this.getList('refresh')
		},
		// 监听页面滚动到底部事件
		onReachBottom() {
			// 上拉加载开启状态下才可以执行请求
			if (this.loadMore) {
				// 上拉加载关闭，防止滚动页面造成多次执行请求
				this.loadMore = false
				// 设置加载更多组件状态为加载中
				this.loadStatus = 'loading'
				// 请求数据
				this.getList('loadmore')
			}
		},
		onLoad() {
			// 获取数据
			this.getList()
			// 监听登录事件
			uni.$on('login', this.userLogin)
		},
		onShow() {
			// #ifdef MP-WEIXIN
			const curPages = getCurrentPages()[0]
			if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
				curPages.getTabBar().setData({
					selected: 2 // custom-tab-bar中index.js文件list的数组对应数据下标
				})
			}
			// #endif
		},
		onUnload() {
			// 移除监听事件
			uni.$off('login', this.userLogin)
		},
		methods: {
			// 用户登录和退出事件
			userLogin() {
				// 更新用户信息，重置页面参数
				this.userInfo = getApp().globalData.userInfo
				this.filter.pageNo = 1
				this.dataList = []
			},
			// 类型切换
			tabChange(index) {
				this.tabCurrent = index
				this.filter.type = this.tabList[index].type
				// 执行搜索
				this.filter.pageNo = 1
				this.getList('search')
			},
			// 获取数据
			getList(type, exParams = {}) {
				return new Promise((reslove, reject) => {
					// 下拉刷新，不显示loading
					let custom = {};
					if (type === 'refresh') {
						custom = {
							ShowLoading: false
						}
					}
					let params = JSON.parse(JSON.stringify(this.filter))
					Object.assign(params, exParams)
					getProcessList(params, custom).then(res => {
						if (type === 'refresh') {
							// 下拉刷新，关闭下拉刷新组件
							uni.stopPullDownRefresh()
						}
						if (res) {
							if (type === 'search') {
								// 数据筛选，将页面滚动至顶部
								uni.pageScrollTo({
									scrollTop: 0,
									duration: 0
								})
							}
							if (type === 'loadmore') {
								// 上拉加载，注入新数据
								this.dataList.push(...res.list)
							} else {
								// 赋值
								this.dataList = res.list
							}
							if (this.loading) {
								setTimeout(() => {
									// 修改骨架屏加载标识为false
									this.loading = false
								}, 300)
							}
						}
					}).catch(err => {
						if (type === 'refresh') {
							// 下拉刷新，关闭下拉刷新组件
							uni.stopPullDownRefresh()
						}
					})
				})
			}
		},
		watch: {
			// 数据列表发生变化
			dataList(newVal) {
				// 处理上拉加载状态
				const listPage = newVal.length / this.filter.pageSize // 数据总条数，除以设定的单页数量
				if (listPage < this.filter.pageNo) {
					// 结果小于当前页，不存在下一页
					// 取整赋值page
					this.filter.pageNo = parseInt(listPage) + 1
					// 设置加载更多组件状态为没有数据
					this.loadStatus = 'nomore'
					// 上拉加载关闭
					this.loadMore = false
				} else {
					// 结果大于或等于当前页
					if (Number.isInteger(listPage)) {
						// 结果为整数，存在下一页
						// 请求分页值+1
						this.filter.pageNo = parseInt(listPage) + 1
						// 设置加载更多组件状态为待加载
						this.loadStatus = 'loadmore'
						// 上拉加载开启
						this.loadMore = true
					} else {
						// 结果为小数，不存在下一页
						// 取整赋值page
						this.filter.pageNo = parseInt(listPage) + 1
						// 设置加载更多组件状态为没有数据
						this.loadStatus = 'nomore'
						// 上拉加载关闭
						this.loadMore = false
					}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		// 适配自定义tabbar 112rpx是tabbar的高度
		/* #ifdef MP-WEIXIN */
		padding-bottom: 112rpx;
		padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
		/* #endif */

		::v-deep .tn-tabs {
			background: #fff;
			box-shadow: 0 0 8rpx 0 rgba(20, 20, 22, 0.08);
		}

		.main {
			padding: 40rpx;

			.list-item {
				background: #FFFFFF;
				box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
				border-radius: 12rpx;

				&+.list-item {
					margin-top: 30rpx;
				}

				.list-content {
					padding: 40rpx 40rpx 30rpx;

					.main-info {
						display: flex;
						align-items: center;

						.text {
							flex: 1;
							font-weight: 500;
							font-size: 30rpx;
							color: #3A3B40;
							line-height: 30rpx;
						}

						.status {
							font-size: 28rpx;
							color: #4FD1C5;
							line-height: 28rpx;
						}
					}

					.secondary-info {
						display: block;
						margin-top: 16rpx;
						font-weight: 500;
						font-size: 26rpx;
						color: #74747B;
						line-height: 26rpx;
					}

					.main-info+.secondary-info {
						margin-top: 32rpx !important;
					}
				}

				.list-button-wrap {
					padding: 0 40rpx 24rpx;
					border-top: 2rpx solid #E3E6EE;

					::v-deep .tn-btn {
						margin-top: 24rpx;
						margin-right: 24rpx;

						.text {
							margin-left: 8rpx;
						}
					}
				}
			}

			::v-deep .tn-load-more {
				margin-top: 30rpx;
			}

			::v-deep .tn-empty {
				margin-top: 200rpx;
			}
		}
	}
</style>