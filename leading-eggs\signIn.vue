<template>
	<!-- 获取登录 -->
	<view class="app">
		<button open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber" type="primary" class="app_title">授权手机号登录 </button>
		<view style="text-align: center;color: red;position: relative;top: 110rpx;font-size: 24rpx;">
			登陆之后获取核销码进行核销
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				dataCode: {}
			}
		},
		onLoad(e) {
			console.log(e, 'e2')
			if(e.hasOwnProperty( 'scene') ){
				if (e.scene.includes('%')) {
				  // 包含编码字符，说明未被自动解码
				  this.dataCode = this.$api.praseScene(decodeURIComponent(e.scene))
				  console.log(e, '我是苹果解密之后')
				} else {
				  // 已被解码，直接使用
				  this.dataCode = e
				  console.log(e, '我是安卓未解密之后')
				}
			} else{
				this.dataCode = e
				console.log(e, '我是安卓解密之后')
			}
			
			console.log(this.dataCode, 'dataCode')
			// this.getCode()
		},
		methods: {
			async onGetPhoneNumber(e) {
				if (e.detail.errMsg === 'getPhoneNumber:ok') {
					// 获取加密数据
					const {
						encryptedData,
						iv,
						code
					} = e.detail
					console.log(e)
					// 发送到服务器解密
					this.$api.request('common/submitInfo', {code: code, activity_id: this.dataCode.aid, belong_user_id: this.dataCode.uid}, (res)=>{
						if(res.status == 'ok'){
							console.log(res)
							uni.setStorageSync('hex', res.user_info )
							uni.navigateTo({
								url: `/leading-eggs/writeOff`
							})
						}else {
							uni.showToast({
								icon: 'none',
								title: res.info
							})
						}
					})
				} else {
					// 用户拒绝授权
					uni.showToast({
						title: '需要手机号授权才能继续',
						icon: 'none'
					})
					
					// 
				}
			},
			getCode() {	
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						
					},
					fail() {
						uni.showToast({
							title: '登录失败',
							icon: 'none',
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.app {
		width: 100%;

		.app_title {
			width: 80%;
			border-radius: 40rpx;
			margin: 0 auto;
			color: #fff;
			position: relative;
			top: 100rpx;
			text-align: center;
			font-size: 32rpx;
			font-weight: 550;
			background: #01BEFF;
		}

		.list {
			width: calc(100% - 100rpx);
			margin: 0 auto;

			.list_info {
				.list_title {
					font-size: 28rpx;
					font-weight: 550;
				}

				.ewms {
					width: 350rpx;
					height: 350rpx;
					background: plum;
					margin: 20rpx auto 0;
				}
			}
		}
	}
</style>