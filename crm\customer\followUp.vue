<template>
    <view class="oa-content">
        <view :style="{ paddingTop: vuex_custom_bar_height + 'px' }">
            <view class="tn-margin" style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx;">
                <!-- 跟进方式 -->
                <view class="tn-flex tn-flex-row-between tn-padding-vertical">
                    <view class="tn-text-bold">跟进方式</view>
                    <view class="tn-flex tn-justify-end" style="gap: 12rpx;">
                        <view
                            v-for="(item, idx) in followupMethodArr"
                            :key="idx"
                            class="tn-radio-button"
                            :class="[form.followup_method === item.value ? 'active' : '']"
                            @click="handleFollowupMethodChange(item.value)"
                        >
                            {{ item.label }}
                        </view>
                    </view>
                </view>

                <!-- 跟进状态 -->
                <view class="tn-flex tn-flex-row-between tn-padding-vertical">
                    <view class="tn-text-bold">跟进状态<text class="tn-color-red">*</text></view>
                    <view class="tn-flex tn-justify-end" style="gap: 12rpx;">
                        <view v-for="(item, idx) in getFollowupStatusArr()" :key="idx" class="tn-radio-button" :class="[form.followup_status === item.value ? 'active' : '']" @click="form.followup_status = item.value" >
                            {{ item.label }}
                        </view>
                    </view>
                </view>

                <!-- 照片上传 -->
                <view>
                    <view class="tn-flex tn-flex-row-between tn-padding-vertical">
                        <view class="tn-text-bold">
                            <text v-if="form.followup_method === 'meeting'">见面照片</text>
                            <text v-else>沟通照片</text>
                            <text v-if="form.followup_method === 'meeting'" class="tn-color-red">*</text>
                        </view>
                    </view>
                    <view style="padding: 20rpx 0;">
                        <tn-image-upload
                            ref="imageUpload"
                            :action="action"
                            :width="200"
                            :height="200"
                            :formData="formData"
                            :fileList="fileList"
                            :disabled="disabled"
                            :autoUpload="autoUpload"
                            :maxCount="maxCount"
                            :showUploadList="showUploadList"
                            :showProgress="showProgress"
                            :deleteable="deleteable"
                            :customBtn="customBtn"
                            @sort-list="onSortList"
                            @on-choose-complete="beforeUpload"
                        />
                    </view>
                </view>

                <!-- 见面场所（仅见面方式必填） -->
                <view v-if="form.followup_method === 'meeting' && meeting_placeArr.length">
                    <picker @change="handleMeetingPlaceChange" :value="meeting_placeIndex" :range="meeting_placeArr" range-key="name">
                        <view class="tn-flex tn-flex-row-between tn-padding-vertical">
                            <view class="tn-text-bold">见面场所<text class="tn-color-red">*</text></view>
                            <view class="tn-color-gray tn-text-right">
                                {{ meeting_placeIndex !== -1 ? meeting_placeArr[meeting_placeIndex].name : '请选择' }}
                                <text class="tn-icon-right tn-padding-left-xs"></text>
                            </view>
                        </view>
                    </picker>
                </view>




<!-- <picker @change="(e) => bindPickerChange(e, 'income_source')" :value="indexIncome_source" :range="income_sourceArr" range-key="label">
                    <view
                            class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
                        <view class="justify-content-item" style="min-width: 150rpx;">
                            <view class="tn-text-bold">
                                收入来源
                            </view>
                        </view>
                        <view class="justify-content-item tn-color-grey tn-text-right">
                            <view class="tn-color-gray tn-color-black" style="max-width:50vw">
                                <view class="tn-color-gray" v-if="indexIncome_source === -1">
                                    请选择
                                    <text class="tn-icon-right tn-padding-left-xs"></text>
                                </view>
                                <view class="" v-else>
                                    {{income_sourceArr[indexIncome_source].label}}
                                    <text class="tn-icon-right tn-padding-left-xs"></text>
                                </view>
                            </view>
                        </view>
                    </view>
                </picker> -->




                <!-- 下次跟进时间 -->
                <view class="tn-flex tn-flex-row-between tn-padding-vertical" @tap="showPicker">
                    <view class="tn-text-bold tn-text-lg">下次跟进时间</view>
                    <view class="tn-color-gray tn-padding-top-xs" v-if="!result">请选择</view>
                    <view class="tn-padding-top-xs" v-else>{{ result}}</view>
                </view>

      <view class="">
        <tn-picker mode="time" v-model="show" :params="params" :startYear="startYear" :endYear="endYear" @confirm="handleConfirmDate"></tn-picker>
      </view>
                <!-- 跟进内容 -->
                <view class="tn-padding-vertical">
                    <view class="tn-text-bold">跟进内容<text class="tn-color-red">*</text></view>
                    <textarea
                        v-model="form.remarks"
                        maxlength="300"
                        placeholder="请填写跟进内容"
                        placeholder-style="color:#AAAAAA"
                        style="height: 160rpx;padding:30rpx 0 20rpx 30rpx;"
                    ></textarea>
                </view>

                <!-- 提交按钮 -->
                
            </view>
        </view>
		<view class="tn-footerfixed tn-padding">
		    <view class="tn-flex-1 justify-content-item tn-text-center">
		        <tn-button
		            backgroundColor="#9ebeff "
		            padding="40rpx 0"
		            width="60%"
		            :fontSize="28"
		            fontColor="#FFFFFF"
		            shape="round"
		            @click="submit"
		        >
		            <text class="">添加跟进</text>
		        </tn-button>
		    </view>
		</view>
        <view class="tn-tabbar-height"></view>

    </view>
</template>

<script>
import template_page_mixin from '@/components/template_page_mixin.js'
import httpApi from '@/config/app.js'
export default {
	name: 'TemplateCost',
	mixins: [template_page_mixin],
    data() {
        return {
			params: {
				year: true,
				month: true,
				day: true,
				hour: false,
				minute: false,
				second: false
			},
			meeting_placeArr: [],
			meeting_placeIndex: -1,
            form: {
                followup_method: '',
                followup_status: '',
                meeting_place: '',
                remarks: '',
                sketch: '',
				crm_clue_id: ''
            },
            followupMethodArr: [
                { label: '电话', value: 'call', ext_value: 1 },
                { label: '见面', value: 'meeting', ext_value: 2 }
            ],
            allFollowupStatusArr: [
                { label: '有效', value: 'valid', ext_value: 1 },
                { label: '无效', value: 'invalid', ext_value: 2 },
                { label: '未接通', value: 'unanswered', ext_value: 3 }
            ],
            meetingPlaceArr: [
                { label: '客户家', value: 'home' },
                { label: '门店', value: 'store' },
                { label: '总部', value: 'headquarters' }
            ],
            indexMeetingPlace: -1,
            action: httpApi + '/api/mini.main/upload',
            formData: {
                type: 'customer',
                file_type: 1
            },
            fileList: [],
            disabled: false,
            autoUpload: true,
            maxCount: 5,
            showUploadList: true,
            showProgress: true,
            deleteable: true,
            customBtn: false,
            vuex_custom_bar_height: 1,
            result: '',
            show: false,
            startYear: new Date().getFullYear(),
            endYear: new Date().getFullYear() + 10,
            dateParams: {
                yearInterval: 1,
                monthInterval: 1,
                dayInterval: 1,
                format: 'yyyy-MM-dd'
            },
			listArr: [],
			imgArr: []
        };
    },
	onLoad(e) {
		this.getType()
		this.form.crm_clue_id = e.id
	},
    methods: {
		// 获取列表
		getType(){
			this.$api.request('common/dictionaryType', {}, (res)=>{	
				console.log('res:', res)
				// if(res){
					
				// }
				this.$api.request('common/dictionaryTypeList', {type: res.list[0].id} , (res1)=>{
					console.log('res1:', res1.info)
					this.listArr = res1.info
					res1.info.map((item) =>{
						if(item.ext_name == "jianmianchangsuo"){
							this.meeting_placeArr = item.children
						}
					})
					console.log(this.meeting_placeArr, 'meeting_placeArr')
				})
			})
		},
		// 添加更近 api/mini.customerFollowup/add
        handleFollowupMethodChange(method) {
            this.form.followup_method = method;
            if (method === 'meeting') {
                this.form.followup_status = '';
            }
        },
        getFollowupStatusArr() {
            if (this.form.followup_method === 'meeting') {
                return this.allFollowupStatusArr.filter(item => item.value!== 'unanswered');
            }
            return this.allFollowupStatusArr;
        },
        handleMeetingPlaceChange(e) {
            const index = e.detail.value;
            this.indexMeetingPlace = index;
            this.meeting_placeIndex = index;
            this.form.meeting_place = this.meeting_placeArr[index].ext_value;
        },
        showPicker() {
			this.openPicker()
        },
		
		  // 打开Picker
		  openPicker() {
			this.show = true
		  },
		
		
        handleConfirmDate(value) {
			//打印
            this.result = value.year+"-"+value.month+"-"+value.day;
            this.show = false;
        },
        hidePicker() {
            this.show = false;
        },
        getExtValueByName(arr, value){
			const item = arr.find(it => it.value === value);
			return item?.ext_value;
		},
		submit() {
			// img   JSON.str name_path
			let arrs = []
			if(this.$refs.imageUpload !== undefined){
				for(let i = 0; i < this.$refs.imageUpload.lists.length; i++){
					if(this.$refs.imageUpload.lists[i].progress == 100){
						let objs = {
							name_path: this.$refs.imageUpload.lists[i].postLine
						}
						arrs.push(objs)
					}
				}
			}
            if (!this.form.remarks.trim()) {
                uni.showToast({ title: '请填写跟进内容', icon: 'none' });
                return;
            }
			let obj = {
				is_way: this.getExtValueByName(this.followupMethodArr, this.form.followup_method),
				is_state: this.getExtValueByName(this.allFollowupStatusArr, this.form.followup_status),
				content: this.form.remarks,
				img: JSON.stringify(arrs),
				next_follow_time: this.result,
				crm_clue_id: this.form.crm_clue_id,
				
			}
            if (this.form.followup_method === 'meeting') {
                if (arrs.length === 0) {
                    uni.showToast({ title: '请上传见面照片', icon: 'none' });
                    return;
                }
                if (!this.form.meeting_place) {	
                    uni.showToast({ title: '请选择见面场所', icon: 'none' });
                    return;
                } else{
					
					obj.follow_place = this.meeting_placeArr[this.meeting_placeIndex].ext_value
					console.log(obj.follow_place, '进入体制内')
					console.log(this.meeting_placeIndex, '进入体制内')
					console.log(this.meeting_placeArr, '进入体制内')
				}
            }
			
			
            // const token = "Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM";
			const token = uni.getStorageSync('token');
			
			this.$api.request('customerFollowup/add', obj, (res) => {
				console.log(res)
				if (res.status == 'ok') {
					uni.showToast({
					    title: '提交成功',
					    icon: 'success'
					});
					const timer = setTimeout(() => {
						uni.navigateBack({
							delta: 1,
							animationType: 'pop-in',
							animationDuration: 500
						});
						clearTimeout(timer)
					}, 500)
				} else {
					uni.showToast({
						icon: 'none',
						title: res.info
					})
				}
			})
        },
        onSortList(files) {
            this.fileList = files;
        },
        beforeUpload(file) {
            // const token = "Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM";
			const token = uni.getStorageSync('token');
			uni.uploadFile({
                url: this.action,
                filePath: file[file.length - 1].url,
                name: 'file',
                formData: {
                   dir: 'profile',
                   type: 'image',
                },
                header: {
                    'token': token,
					'Content-Type': 'application/json'
                },
                success: (res) => {
                    if (res.statusCode === 200) {
						
                        const response = JSON.parse(res.data);
						this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].onLine = response.url
						this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].postLine = response.path
                    }
                }
            });
            return false;
        }
    }
};
</script>

<style lang="scss" scoped>
/* 胶囊*/
.tn-custom-nav-bar__back {
    width: 60%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    box-sizing: border-box;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 1000rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.5);
    color: #FFFFFF;
    font-size: 18px;
}

.tn-custom-nav-bar__back .icon {
    display: block;
    flex: 1;
    margin: auto;
    text-align: center;
}

.oa-content {
    max-width: 640px;
    margin: 0 auto;
    background-color: #E7EDF7;
    min-height: 100vh;
    padding-bottom: 60rpx;
    padding-bottom: calc(80rpx + env(safe-area-inset-bottom) / 2);
    padding-bottom: calc(80rpx + constant(safe-area-inset-bottom));
}

/* 间隔线 start*/
.tn-strip-bottom-min {
    width: 100%;
    border-bottom: 1rpx solid #F8F9FB;
}

.tn-strip-bottom {
    width: 100%;
    border-bottom: 20rpx solid rgba(241, 241, 241, 0.8);
}

/* 间隔线 end*/

/* 用户头像 start */
.logo-image {
    width: 80rpx;
    height: 80rpx;
    position: relative;
}

.logo-pic {
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    border: 2rpx solid rgba(255, 255, 255, 0.05);
    box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
    border-radius: 50%;
    overflow: hidden;
}

/* 底部悬浮按钮 start*/
.tn-tabbar-height {
    min-height: 160rpx;
    height: calc(180rpx + env(safe-area-inset-bottom) / 2);
    height: calc(180rpx + constant(safe-area-inset-bottom));
}

.tn-footerfixed {
    max-width: 640px;
    margin: 0 auto;
    position: fixed;
    width: 100%;
    bottom: calc(40rpx + env(safe-area-inset-bottom));
    z-index: 1024;
    box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);
}

/* 底部悬浮按钮 end*/

/* 自定义上传事件 start */
.image-upload__wrap {
    display: flex;
    align-items: center;
}

.image-upload__item {
    flex: 1;
    width: calc(100% - 60rpx);
    max-width: 50%;
    height: 260rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.image-upload__item .image {
    width: 100%;
    height: 100%;
    border: 2rpx dashed #E6E6E6;
    border-radius: 15rpx;
    overflow: hidden;
}

.image-upload__image,
.image-upload__empty {
    width: 100%;
    height: 90%;
}

.image-upload__title {
    padding-top: 30rpx;
    line-height: 1;
}

.image-upload__item + .image-upload__item {
    margin-left: 20rpx;
}

/* 新增右对齐样式 */
.tn-justify-end {
    justify-content: flex-end !important;
}

.tn-radio-button {
    padding: 8rpx 30rpx;
    border-radius: 4rpx;
    border: 1rpx solid #e6e6e6;
    background: #f8f8f8;
    transition: all 0.3s;
    color: #666;
}

.tn-radio-button.active {
    background: #6699ff;
    color: #fff;
    border-color: #6699ff;
    box-shadow: 0 2rpx 4rpx rgba(102, 153, 255, 0.2);
}

.tn-padding-vertical {
    padding-top: 20rpx;
    padding-bottom: 20rpx;
}
</style>    