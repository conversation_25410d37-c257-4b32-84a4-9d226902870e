<template>
	<view class="container">
		main
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: getApp().globalData.userInfo // 用户信息
			}
		},
		onLoad() {
			// 设置tabbar
			this.setTabbar()
			// 监听登录事件
			uni.$on('login', this.userLogin)
		},
		// 小程序点击分享
		onShareAppMessage() {
			return {
				title: '博迪智家',
				path: '/pages/main/main'
			}
		},
		onShow() {
			// #ifdef MP-WEIXIN
			const curPages = getCurrentPages()[0]
			if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
				curPages.getTabBar().setData({
					selected: 0 // custom-tab-bar中index.js文件list的数组对应数据下标
				})
			}
			// #endif
		},
		onUnload() {
			uni.$off('login', this.userLogin)
		},
		methods: {
			// 用户登录和退出事件
			userLogin() {
				// 更新用户信息
				this.userInfo = getApp().globalData.userInfo
				// 设置tabbar
				this.setTabbar()
			},
			// 设置tabbar
			setTabbar() {
				// #ifdef MP-WEIXIN
				const curPages = getCurrentPages()[0]
				let mode = 1 // 自己定义mode的参数，在custom-tab-bar中index.js中写逻辑
				// a权限
				// if (this.userInfo.role === 1) {
				// 	mode = 1
				// }
				// 更新tabbar
				if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
					curPages.getTabBar().updateTabbar(mode)
				}
				// #endif
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		// 适配自定义tabbar
		/* #ifdef MP-WEIXIN */
		padding-bottom: 112rpx;
		padding-bottom: calc(112rpx + env(safe-area-inset-bottom));
		/* #endif */
	}
</style>