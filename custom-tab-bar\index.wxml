<!--miniprogram/custom-tab-bar/index.wxml-->
<view class="tab-bar">
	<block wx:for="{{list}}" wx:key="index">
		<view wx:if="{{item.show}}" class="tab-bar-item" data-path="{{item.pagePath}}" data-index="{{index}}" bindtap="switchTab">
			<image src="{{selected === index ? item.selectedIconPath : item.iconPath}}"></image>
		    <view style="color: {{selected === index ? selectedColor : color}}">{{item.text}}</view>
		</view>
	</block>
</view>
