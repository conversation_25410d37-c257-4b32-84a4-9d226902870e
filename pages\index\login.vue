<template>
	<!-- 登录页面 -->
	<view class="app">
		<view class="nb" @click="handleMultipleClick"></view>
		<tn-modal v-model="show" :custom="true" >
		  <view class="custom-modal-content">
			  <view class="titles">
				内置登录系统
			  </view>
			<tn-input class="inputs" v-model="value" :border="border" placeholder="请输入手机号"/>
			<view style="width: 100%;height: 20rpx;"></view>
			<tn-input class="inputs" v-model="pass" :border="border" />
			<view style="width: 100%;height: 40rpx;"></view>
			<view class="btns1" @click="linLog">
				登录
			</view>
		  </view>
		</tn-modal>
		<view class="login">
			<view class="logo">
				<image style="width: 100%;" src="../../static/logo.png" mode="widthFix"></image>
			</view>
			<view class="login_btn" @click="wxLogin">
				微信一键授权登录
			</view>
			<view class="phone_btn" @click="phoneLogin">
				使用手机号登录
			</view>
		</view>
	</view>
</template>

<script>
	import httpApi from '@/config/app.js'
	export default {
		data() {
			return {
				pass: '123456',
				border: true,
				value: '13811198709',
				clickCount: 0,
				lastClickTime: 0,
				clickTimeout: null,
				show: false
			}
		},
		mounted() {
			// console.log(this.globalData.userInfo)
		},
		methods: {
			linLog(){
				this.$api.request('wxuser/testLogin', {phone: this.value, password: this.pass}, (res)=>{
					console.log(res, 'res')
					uni.setStorageSync('token', res.token)
					res.user.is_phone_bind = 1
					uni.setStorageSync('userInfo', res.user)
					uni.reLaunch({
						url: '/pages/index/index'
					})
				})
			},
			handleMultipleClick() {
				const now = Date.now()

				// 如果两次点击间隔超过1秒，重置计数器
				if (now - this.lastClickTime > 1000) {
					this.clickCount = 0
				}

				this.clickCount++
				this.lastClickTime = now

				// 清除之前的定时器
				if (this.clickTimeout) {
					clearTimeout(this.clickTimeout)
				}

				// 设置新的定时器，1秒后重置计数器
				this.clickTimeout = setTimeout(() => {
					this.clickCount = 0
				}, 1000)

				// 如果点击次数达到5次，触发函数
				if (this.clickCount >= 5) {
					this.onFiveClicks()
					this.clickCount = 0 // 触发后重置计数器
				}
			},
			onFiveClicks() {
				// 这里是点击五次后要执行的函数
				// uni.showToast({
				// 	title: '您连续点击了五次！',
				// 	icon: 'none'
				// })
				this.show = true
				console.log('五次点击触发')
			},

			// 微信登录
			wxLogin() {
				uni.login({
					provider: 'weixin',
					success: function(loginRes) {
						uni.request({
							url: httpApi + '/api/mini.wxuser/wxLogin',
							method: 'POST',
							data: {
								code: loginRes.code
							},
							header: {
								'Content-Type': 'application/json'
							},
							success: (res) => {
								console.log('成功s', res)
								if(res.data.status == 'ok'){
									console.log('成功s', res.data)
									console.log('成功1', res.data.user_info.hasOwnProperty('token'))
									// 存储token
									if (res.data.user_info.hasOwnProperty('token')) {
										uni.setStorageSync('token', res.data.user_info.token)
									}
									// 存储openid 
									uni.setStorageSync('openId', res.data.openid)
									// 存储用户信息
									uni.setStorageSync('userInfo', res.data.user_info.data)
									uni.navigateTo({
										url: '/pages/index/index?index=3'
									})
								} else{
									uni.showToast({
										icon: 'none',
										title: res.data.info
									})
								}
							},
							fail: (err) => {
								console.log('失败', err)
							}
						})
					},
					fail() {
						uni.showToast({
							title: '登录失败',
							icon: 'none',
							duration: 2000
						})
					}
				})
			},
			// 手机号登录
			phoneLogin() {
				uni.navigateTo({
					url: '/pages/index/loginPhone'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.titles{
		width: 100%;
		text-align: center;
		font-size: 36rpx;
		font-weight: 550;
		margin-bottom: 40rpx;
	}
	.btns1{
		width: 200rpx;
		color: #fff;
		background: skyblue;
		text-align: center;
		line-height: 70rpx;
		height: 70rpx;
		border-radius: 35rpx;
		margin: 0 auto;
	}
	.inputs{
		margin-bottom: 20rpx;
	}
	.app {
		width: 100%;
		height: 100vh;
		background: #D8E5FF;
		position: relative;
		overflow: hidden;

		.nb {
			position: absolute;
			top: 0rpx;
			left: 0rpx;
			width: 200rpx;
			height: 200rpx;
			
		}

		.login {
			width: 80%;
			height: 500rpx;
			margin: 300rpx auto 0;

			.logo {
				width: 180rpx;
				height: 150rpx;
				overflow: hidden;
				margin: 0 auto;
			}

			.login_btn {
				width: 100%;
				height: 70rpx;
				border-radius: 35rpx;
				background: yellowgreen;
				color: #fff;
				margin-top: 30rpx;
				text-align: center;
				line-height: 70rpx;
				font-size: 32rpx;
			}

			.phone_btn {
				width: 100%;
				height: 70rpx;
				margin-top: 20rpx;
				text-align: center;
				font-size: 26rpx;
			}
		}
	}
</style>