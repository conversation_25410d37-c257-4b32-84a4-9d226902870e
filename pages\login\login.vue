<template>
	<view class="container">login</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		onLoad() {
		},
		methods: {
			login() {
				// 登录成功，保存用户数据
				// 保存用户信息
				const data = {}
				getApp().globalData.userInfo = data
				this.$util.setStorageSync('userInfo', data)
				// 发送登录成功事件
				uni.$emit('login')
				// 关闭当前页面
				this.$util.switchTab('main')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {}
</style>