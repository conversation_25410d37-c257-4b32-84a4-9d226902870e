<script>
	export default {
		// globalData: {
		// 	userInfo: {} // 用户信息
		// },
		onLaunch: function() {
			// 获取用户信息
			// const userInfo = uni.getStorageSync('userInfo')
			// if (userInfo) {
			// 	this.globalData.userInfo = userInfo
			// }
			// // 开发测试代码
			// this.globalData.userInfo = {
			// 	token: 'Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM'
			// }
			// console.log(this.globalData)
			// uni.onStorageChanged(({ key }) => {
			// 	if (key === 'token') {
			// 		this.updateTabBar();
			// 	}
			// });
		},
		onShow: function() {
			// #ifdef MP-WEIXIN
			// 小程序升级功能模块
			if (wx.canIUse('getUpdateManager')) {
				const updateManager = wx.getUpdateManager()
				updateManager.onCheckForUpdate(function(res) {
					// 请求完新版本信息的回调
					if (res.hasUpdate) {
						updateManager.onUpdateReady(function() {
							wx.showModal({
								title: '更新提示',
								content: '新版本已经准备好，是否重启应用？',
								success: function(res) {
									if (res.confirm) {
										// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
										updateManager.applyUpdate()
									}
								}
							})
						})
						updateManager.onUpdateFailed(function() {})
					}
				})
			}
			// #endif
		},
		onHide: function() {},
		methods: {
			// updateTabBar() {
			// 	const pages = getCurrentPages();
			// 	const currentPage = pages[pages.length - 1];
			// 	if (currentPage.$vm?.updateSelected) {
			// 		currentPage.$vm.updateSelected();
			// 	}
			// }
		}
	}
</script>

<style lang="scss">
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import "@/uni_modules/uview-ui/index.scss";
	
	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	@import './tuniao-ui/index.scss';
	@import './tuniao-ui/iconfont.css';
	@import './common/common.scss';

	page {
		background: #E8ECF1;
	}
	
	// 超出行数，自动显示行尾省略号，最多5行
	@for $i from 1 through 5 {
	  .line-#{$i} {
	    // vue下，单行和多行显示省略号需要单独处理
	    @if $i == '1' {
	      overflow: hidden;
	      white-space: nowrap;
	      text-overflow: ellipsis;
	    } @else {
	      display: -webkit-box!important;
	      overflow: hidden;
	      text-overflow: ellipsis;
	      word-break: break-all;
	      -webkit-line-clamp: $i;
	      -webkit-box-orient: vertical!important;
	    }
	  }
	}
</style>