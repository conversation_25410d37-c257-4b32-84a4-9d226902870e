<template>
	<view class="app">
		<view class="tops">
			<view class="top_info" @click="jumpLogin" v-if="!userInfo">
				点击登录
			</view>
			<view class="userXx" v-else>
				<image style="width: 100%;" class="imgBg" src="/static/backs.png" mode="widthFix"></image>
				<view class="flexsClass" v-if="">
					<view class="imgs">
						<image v-if="setUser.avatar" style="width: 100%;height: 100%;" :src="setUser.avatar" alt=""
							srcset="" />
						<image v-else style="width: 100%;height: 100%;" src="/static/moren.png" mode=""></image>
					</view>
					<view class="rights">
						<view class="names">
							{{ setUser.real_name ? setUser.real_name : '-- '}}
						</view>
						<view class="bumen">
							部门：{{ setUser.dept_name ? setUser.dept_name : '--' }}
						</view>
						<!-- <view class="phones">
							{{ userInfo.phone }}
						</view> -->
					</view>
				</view>

			</view>
			<view class="conBtns">
				<view class="gneng" @click="jumpsAbout">
					<image class="gong_img" src="/static/guan.png" mode=""></image>
					<view class="text">
						关于我们
					</view>
				</view>
				<view class="gneng" @click="handleScan">
					<view class="tn-icon-scan gong_img" style="font-size: 35rpx;"></view>
					<view class="text">
						扫一扫
					</view>
				</view>
				<view class="gneng" @click="openCode">
					<view class="tn-icon-qr-code gong_img" style="font-size: 35rpx;"></view>
					<view class="text">
						推广码
					</view>
				</view>
				<view class="gneng" style="border-bottom: none;" @click="setInt">
					<image class="gong_img" src="/static/setup.png" mode=""></image>
					<view class="text">
						设置
					</view>
				</view>
			</view>
			<view class="btns" v-if="userInfo" @click="clearInput">
				退出登录
			</view>
		</view>
		<!-- 完善日志信息 -->
		<tn-popup v-model="codeShow" borderRadius="10" mode="center" :maskCloseable="false">
			<view class="codeView">
				<view class="close" style="position: absolute;top: 7rpx;right: 7rpx;" @click="closePopup">
					<text class="tn-icon-close-fill" style="color: red;font-size: 40rpx;"></text>
				</view>
				<picker @change="(e) => bindCode(e)" :value="indexChannel" :range="channelSourceArr" range-key="title">
					<view class="item_list">
						<view class="lable">
							活动名称
						</view>
						<view class="value">
							<view class="tn-color-gray" v-if="indexChannel === -1">
								请选择
								<text class="tn-icon-right tn-padding-left-xs"></text>
							</view>
							<view class="" v-else>
								{{ channelSourceArr[indexChannel].title }}
								<text class="tn-icon-right tn-padding-left-xs"></text>
							</view>
						</view>
					</view>
				</picker>
				<view class="imgScode" v-if="imgUrl">
					<image :src="imgUrl" mode="" style="width: 100%;height: 100%;" @tap="saveImageToLocal"></image>
				</view>
			</view>
		</tn-popup>
		<view class="bd_phone" v-if="bdP">
			<!-- <view class="bd_phone"> -->
			<view class="boxs">
				<view class="titles">
					完善个人信息
				</view>
				<view class="phones">
					<input class="inputs" placeholder="请选择输入您的姓名" name="input" placeholder-style="color:#AAAAAA"
						v-model="real_name"></input>
				</view>
				<view class="news1">
					<!-- <input class="inputs" placeholder="请选择您的身份" name="input" placeholder-style="color:#AAAAAA" v-model="phone"></input> -->
					<picker @change="(e) => bindPickerChange(e, 'shenfen')" :value="indexIncome_source"
						:range="income_sourceArr" range-key="name">
						<view class="xing" style="color: #AAA;" v-if="indexIncome_source === -1">
							请选择您的身份

							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="xing" style="color: #000;" v-else>
							{{income_sourceArr[indexIncome_source].name}}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</picker>
				</view>
				<view class="news1" v-if="income_sourceArr[indexIncome_source].name == '员工'">
					<picker @change="(e) => bindPickerChange(e, 'mendian')" :value="indexShop" :range="shopList"
						range-key="name">
						<view class="xing" style="color: #AAA;" v-if="indexShop === -1">
							请选择您的门店
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="xing" style="color: #000;" v-else>
							{{shopList[indexShop].name}}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</picker>
				</view>
				<view class="news1" v-if="income_sourceArr[indexIncome_source].name == '员工'">
					<picker @change="(e) => bindPickerChange(e, 'zhiwei')" :value="indexDet" :range="detList"
						range-key="name">
						<view class="xing" style="color: #AAA;" v-if="indexDet === -1">
							请选择您的部门
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="xing" style="color: #000;" v-else>
							{{detList[indexDet].name}}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</picker>
				</view>
				<view class="news1">
					<input class="inputs" placeholder="请输入您的手机号" name="input" placeholder-style="color:#AAAAAA"
						v-model="phone"></input>
				</view>
				<view class="yzms">
					<input class="inputs" placeholder="请输入验证码" name="input" placeholder-style="color:#AAAAAA"
						v-model="yzm"></input>
					<view class="scode" @click="code">
						{{ text }}
					</view>
				</view>
				<view class="sumbit" @click="queryPhone">
					确定绑定
				</view>
				<view class="sumbit" @click="phoneLogin">
					切换手机验证码登录
				</view>
			</view>
		</view>
	</view>
</template>



<script>
	export default {
		data() {
			return {
				indexChannel: -1,
				imgUrl: '',
				channelSourceArr: [],
				codeShow: false,
				phone: '',
				userInfo: {},
				bdP: false,
				yzm: '',
				real_name: '',
				text: '发送验证码',
				disabled: false,
				shopList: [],
				detList: [],
				is_staff: '',
				store_id: '',
				duty_id: '',
				indexShop: -1,
				indexDet: -1,
				indexIncome_source: -1,
				setUser: null,
				income_sourceArr: [{
						name: '员工',
						value: 1
					},
					{
						name: '非员工',
						value: 2
					}
				]
			}
		},
		mounted() {
			
			this.getListHd()
			this.getListInput()
			this.getUserInfo()
			this.userInfo = uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo') : ''
			console.log('我是跳转页面1', this.userInfo)
			if (this.userInfo) {
				console.log('我是跳转页面1', this.userInfo.is_phone_bind)
				console.log(this.userInfo, '我是当前数据1')
				console.log(uni.getStorageSync('userInfo'), '我是存储数据2')
				this.bdP = this.userInfo.is_phone_bind == 1 ? false : true
			} else {
				console.log('我是跳转页面2')
				this.bdP = false
				this.jumpLogin()
			}
			console.log(this.userInfo)
		},
		// 小程序点击分享
		onShareAppMessage() {
			return {
				title: '博迪智家',
				path: '/pages/index/index'
			}
		},
		onShow() {},
		destroyed() {
			uni.$off('login', this.userLogin)
		},
		methods: {
			phoneLogin() {
				uni.navigateTo({
					url: '/pages/index/loginPhone'
				})
			},
			saveImageToLocal() {
				// 1. 下载图片到临时路径
				uni.downloadFile({
					url: this.imgUrl,
					success: (res) => {
						if (res.statusCode === 200) {
							// 2. 保存到相册
							uni.saveImageToPhotosAlbum({
								filePath: res.tempFilePath,
								success: () => {
									uni.showToast({
										title: '保存成功',
										icon: 'success'
									});
								},
								fail: (err) => {
									console.error('保存失败:', err);
									this.handleSaveError(err);
								}
							});
						}
					},
					fail: (err) => {
						console.error('下载失败:', err);
						uni.showToast({
							title: '图片下载失败',
							icon: 'none'
						});
					}
				});
			},
			// 处理保存失败（尤其是权限问题）
			handleSaveError(err) {
				if (err.errMsg.includes('auth deny')) {
					uni.showModal({
						title: '权限提示',
						content: '需要相册权限才能保存图片，是否去设置？',
						success: (res) => {
							if (res.confirm) {
								// 打开设置页
								uni.openSetting();
							}
						}
					});
				} else {
					uni.showToast({
						title: '保存失败，请重试',
						icon: 'none'
					});
				}
			},
			closePopup() {
				this.codeShow = false
			},
			getListHd() {
				this.$api.request('activity/listing', {}, (res) => {
					if (res.status == 'ok') {
						this.channelSourceArr = res.list
					}
				})
			},
			openCode() {
				this.codeShow = true
			},
			bindCode(e) {
				const index = e.detail.value
				if (this.indexChannel == index) {
					return
				}
				this.indexChannel = index
				this.getCodeSc(index)
			},
			getCodeSc(i) {
				this.$util.showLoading('正在生成二维码')
				this.$api.request('activity/salecodeqr', {
					id: this.channelSourceArr[i].id
				}, (res) => {
					if (res.status == 'ok') {
						this.imgUrl = res.user_info.img
					} else {
						this.showToast({
							icon: 'none',
							title: res.info
						})
					}
					this.$util.hideLoading()
				})
			},
			handleScan() {
				uni.scanCode({
					onlyFromCamera: true,
					success: res => {
						if (res.errMsg == 'scanCode:ok') {
							console.log(res)
							if(res.path.includes('%')){
								uni.navigateTo({
									url: '/' + res.path
								})
							}else {
								let str = res.path.split('scene=')[0] + res.path.split('scene=')[1]
								console.log(str, '我是跳转地址')
								uni.navigateTo({
									url: '/' + str
								})
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: '识别失败，请联系管理员!'
							})
						}
					},
					fail() {
						console.log('取消识别')
					}
				});
			},
			getUserInfo() {
				this.$api.request('user/getIndex', {}, (res) => {
					this.setUser = res.data
					console.log(this.setUser)
				})
			},
			bindPickerChange(e, type) {
				const index = e.detail.value
				if (type == 'shenfen') {
					this.indexIncome_source = index
					this.is_staff = this.income_sourceArr[index].value
					this.indexShop = -1
					this.indexDet = -1
					this.store_id = ''
					this.duty_id = ''
				}
				if (type == 'mendian') {
					console.log(e)
					this.indexShop = index
					this.store_id = this.shopList[index].id
				}
				if (type == 'zhiwei') {
					console.log(e)
					this.indexDet = index
					this.duty_id = this.detList[index].id
				}
			},
			jumpsAbout() {
				uni.navigateTo({
					url: '/setup/aboutUs/index'
				})
			},
			setInt() {
				uni.navigateTo({
					url: '/setup/setUser/index'
				})
			},
			clearInput() {
				// 存储token
				uni.setStorageSync('token', '')
				// 存储openid 
				uni.setStorageSync('openId', '')
				// 存储用户信息
				uni.setStorageSync('userInfo', '')

				this.userInfo = null

				uni.showToast({
					icon: 'none',
					title: '退出登录'
				})
				uni.reLaunch({
					url: '/pages/index/index'
				})
			},
			jumpLogin() {
				uni.reLaunch({
					url: '/pages/index/login'
				})
			},
			jumpNav() {
				uni.navigateTo({
					url: '/crm/customer/index',
				})
			},
			getListInput() {
				this.$api.request('common/getShopList', {}, (res) => {
					this.shopList = res.list
					console.log(this.shopList, 'shop')
				})
				this.$api.request('common/getDeptList', {}, (res) => {
					this.detList = res.list
					console.log(this.detList, 'det')
				})
			},
			queryPhone() {
				let that = this
				if (!that.yzm) return uni.showToast({
					icon: 'none',
					title: '请输入验证码'
				})
				let obj = {
					openid: uni.getStorageSync('openId'),
					phone: this.phone,
					sms_code: this.yzm,
					is_staff: this.is_staff,
					store_id: this.store_id,
					duty_id: this.duty_id,
					real_name: this.real_name
				}
				this.$api.request('wxuser/setUserPhone', obj, (res) => {
					if (res.status == 'ok') {
						uni.setStorageSync('token', res.token)
						this.userInfo.is_phone_bind = 1
						uni.setStorageSync('userInfo', this.userInfo)
						console.log(this.userInfo, '我是当前数据')
						console.log(this.userInfo, '我是存储数据')
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						uni.reLaunch({
							url: '/pages/index/index'
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.bdP = true
					}
				})
			},
			code() {
				let that = this;
				if (!that.phone) return uni.showToast({
					icon: 'none',
					title: '请输入手机号码'
				})
				if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone)) return uni.showToast({
					icon: 'none',
					title: '请输入正确的手机号码'
				})
				let obj = {
					phone: that.phone,
					type: 2
				}
				this.$api.request('common/sms', obj, (res) => {
					uni.showToast({
						icon: 'none',
						title: '验证码已发送，请注意短信消息'
					})
					this.sendCode()
				})
			},
			sendCode() {
				if (this.disabled) {
					uni.showToast({
						icon: 'none',
						title: '请等待倒计时结束再点击'
					})
					return
				}
				this.disabled = true;
				let n = 60;
				this.text = "剩余 " + n + "s";
				const run = setInterval(() => {
					n = n - 1;
					if (n < 0) {
						clearInterval(run);
					}
					this.text = "剩余 " + n + "s";
					if (this.text < "剩余 " + 0 + "s") {
						this.disabled = false;
						this.text = "重新获取";
					}
				}, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.imgScode {
		width: 300rpx;
		height: 300rpx;
		margin: 40rpx auto 0;
	}

	.codeView {
		padding: 32rpx;
		width: 500rpx;
		position: relative;

		.item_list {
			margin-top: 20rpx;
			display: flex;
			margin-top: 20rpx;
			justify-content: space-between;
		}
	}

	.tops {
		width: 100%;
		height: 90rpx;

		.top_info {
			width: 78%;
			height: 70rpx;
			position: relative;
			margin: 10rpx auto;
			padding: 0 20rpx;
			background: plum;
			border-radius: 40rpx;
			line-height: 70rpx;
			color: #fff;
		}

		.userXx {
			width: 100%;
			height: 300rpx;
			position: relative;
			overflow: hidden;

			.imgBg {
				position: absolute;
				top: 0;
				left: 0;
				z-index: 10;
				width: 100%;
				height: 100%;
			}

			.flexsClass {
				position: relative;
				z-index: 20;
				display: flex;
				align-items: center;
				height: 200rpx;

				.imgs {
					width: 120rpx;
					height: 120rpx;
					border-radius: 120rpx;
					overflow: hidden;
					margin-left: 32rpx;
				}

				.rights {
					// width: calc(100% - 64rpx - 20rpx);
					margin-left: 20rpx;

					.names {
						font-size: 30rpx;
					}

					.phones {
						font-size: 24rpx;
						margin-top: 10rpx;
					}

					.bumen {
						font-size: 24rpx;
						margin-top: 5rpx;
					}
				}
			}

		}

		.conBtns {
			width: calc(100% - 64rpx);
			// height: 300rpx;
			background: #fff;
			border-radius: 20rpx;
			margin: 0 auto;
			position: relative;
			z-index: 25;
			top: -90rpx;

			.gneng {
				height: 80rpx;
				display: flex;
				width: calc(100% - 56rpx);
				margin: 0 auto;
				align-items: center;
				gap: 0 15rpx;
				border-bottom: 1rpx solid #ccc;

				.gong_img {
					width: 35rpx;
					height: 35rpx;
				}

				.text {
					position: relative;
					top: -3rpx;
					font-size: 28rpx;
				}
			}
		}

		.btns {
			width: 90%;
			position: fixed;
			bottom: 200rpx;
			left: 5%;
			background: #e72f8c;
			text-align: center;
			font-size: 28rpx;
			color: #fff;
			border-radius: 35rpx;
			line-height: 70rpx;
		}
	}

	.bd_phone {
		width: 100%;
		height: 100vh;
		position: fixed;
		z-index: 999;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.3);

		.boxs {
			width: 90%;
			padding: 40rpx;
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			margin: 180rpx auto 0;

			.titles {
				font-weight: 550;
				text-align: center;
				margin-top: 30rpx;
			}

			.phones {
				width: 90%;
				margin: 50rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #AAA;

				.inputs {
					width: 90%;
					margin-left: 5%;
					height: 70rpx;

				}
			}

			.news1 {
				width: 90%;
				margin: 16rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #AAA;

				.xing {
					width: 90%;
					margin-left: 5%;
					height: 70rpx;
					line-height: 70rpx;
				}

				.inputs {
					width: 90%;
					margin-left: 5%;
					height: 70rpx;

				}
			}

			.yzms {
				width: 90%;
				margin: 16rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #AAA;
				display: flex;
				align-items: center;

				.inputs {
					width: 60%;
					margin-left: 5%;
					height: 70rpx;

				}

				.scode {
					width: 35%;
					color: #AAA;
					text-align: center;
					line-height: 68rpx;
					font-size: 26rpx;
					height: 68rpx;
					border-left: 1rpx solid #AAA;
					box-sizing: border-box;
				}
			}

			.sumbit {
				width: 90%;
				margin: 40rpx auto;
				height: 70rpx;
				border-radius: 35rpx;
				color: #fff;
				background: skyblue;
				text-align: center;
				line-height: 70rpx;
			}
		}
	}

</style>