<template>
	<view class="container">

		<!-- 选项卡 -->
		<view class="tabs">
			<view class="tab-item" :class="{ active: activeTab === 0 }" @click="changeTab(0)">
				<text>全部</text>
				<view class="badge">{{count}}</view>
				<view v-if="activeTab === 0" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 1 }" @click="changeTab(1)">
				<text>今日需跟进</text>
				<view class="badge">{{ count2 }}</view>
				<view v-if="activeTab === 1" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 2 }" @click="changeTab(2)">
				<text>今日已跟进</text>
				<view class="badge">{{ count1 }}</view>
				<view v-if="activeTab === 2" class="active-line"></view>
			</view>
		</view>

		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<text class="tn-icon-filter justify-content-item tn-padding-right-xs tn-text-lg" style="color: #3467C2;"
					@click="openSelectModal()"></text>
				<input type="text" placeholder="请输入搜索关键词" placeholder-class="placeholder" v-model="searchKey" />
			</view>
			<view class="action-buttons">
				<tn-button class="btn-item" shape="icon" backgroundColor="#9ebeff" @click="search()">
					<i class="tn-icon-search" aria-hidden="true"></i>
				</tn-button>
				<tn-button class="btn-item" shape="icon" backgroundColor="#9ebeff" @click="AddCustomer()">
					<i class="tn-icon-add" aria-hidden="true"></i>
				</tn-button>
			</view>
		</view>

		<!-- 列表内容 -->

		<view class="list-container">
			<view class="list-item" v-for="(item, index) in filteredListData" :key="index">
				<view class="item-header">
					<text class="item-title">{{ item.clue_name }}</text>
					<view class="item-tag">{{ item.state }}</view>
				</view>

				<view class="item-info">
					<view class="info-grid">
						<view class="info-grid-item">
							<text class="info-label">电话：</text>
							<text class="info-value">{{ item.phone }}</text>
						</view>
						<view class="info-grid-item">
							<text class="info-label">来源：</text>
							<text class="info-value">{{ item.channel }}</text>
						</view>
						<view class="info-grid-item">
							<text class="info-label">区域：</text>
							<text class="info-value">{{ item.area }}</text>
						</view>
						<view class="info-grid-item">
							<text class="info-label">所属人：</text>
							<text class="info-value">{{ item.belongUserName }}</text>
						</view>
					</view>
					<view class="info-grid-items">
						<text class="info-label">最新跟进记录：</text>
						<text class="info-value">{{ item.last_log_center ? item.last_log_center : '暂无跟新记录' }}</text>
					</view>
				</view>

				<view class="item-divider"></view>
				<view class="item-footer">
					<view class="action-btns">
						<view class="action-btn" v-for="(btnItem, btnIndex) in item.btn" :key="btnIndex">
							<!-- 电话类型按钮 -->
							<view v-if="btnItem.type === 'tel'" class="action-btn view-btn"
								@click="handleTel(btnItem.param.tel)">
								{{ btnItem.title }}
							</view>
							<!-- 跳转页面类型按钮 -->
							<view v-if="['navigate', 'redirect', 'reLaunch', 'switch'].includes(btnItem.type)"
								class="action-btn call-btn" @click="handleNavigate(btnItem.path, btnItem.param, item.id)">
								{{ btnItem.title }}
							</view>
							<!-- 提交 -->
							<!-- <view v-if="btnItem.type === 'navigate'"
								class="action-btn call-btn" @click="handleNavigate(btnItem.path, btnItem.param, item.id)">
								{{ btnItem.title }}
							</view> -->
							<!-- 执行功能类型按钮 -->
							<view v-if="btnItem.type === 'func'" class="action-btn edit-btn"
								@click="handleFunc(btnItem.api, btnItem.path, btnItem.param, item.id)">
								{{ btnItem.title }}
							</view>
							<!-- 执行功能类型按钮 -->
							<!-- <view v-if="btnItem.type === 'bta'" class="action-btn edit-btn"
								@click="modify(btnItem.path, item.id, item)	">
								{{ btnItem.title }}
							</view> -->
							<!-- 输入执行类型（假设后续有prompt类型补充） -->
							<view v-if="btnItem.type === 'prompt'" class="action-btn edit-btn" @click="handlePrompt(btnItem)">
								{{ btnItem.title }}
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>


		<!-- 分配模态框 -->
		<view class="assign-modal" v-if="showAssignModal">
			<view class="modal-mask" @click="closeAssignModal"></view>
			<view class="modal-content">
				<view class="modal-header">
					<text class="modal-title">选择分配用户</text>
					<text class="modal-close" @click="closeAssignModal">×</text>
				</view>
				<view class="modal-body">
					<view class="user-btn-group">
						<view class="user-btn" v-for="(user, index) in assignUsers" :key="index"
							:class="{ 'active-btn': selectedUserId === user.value }"
							@click="selectUser(user.value, user.label)">
							{{ user.label }}
						</view>
					</view>
				</view>
				<view class="modal-footer">
					<button class="btn-cancel" @click="closeAssignModal">取消</button>
					<button class="btn-confirm" @click="confirmAssign">
						确认分配
					</button>
				</view>
			</view>
		</view>
		<!-- 在列表容器末尾添加 -->
		<view class="loading-status">
			<text v-if="isLoading">正在加载...</text>
			<text v-if="noMoreData">没有更多数据了</text>
		</view>

		<!-- <select-modal v-model="showSelectModal"></select-modal> -->
		<SelectModal :value="showSelectModal" @input="toggleFilter" @filter="applyFilters" :listArrs="listArrs" :userInfoList="userInfoList" :key="listKey" v-if="listKey" />
	</view>


</template>

<script>
	import SelectModal from '@/components/select.vue'
	export default {
		components: {
			SelectModal
		},
		data() {
			return {
				
				activeTab: 0,
				showSelectModal: false,
				allData: [],
				filteredListData: [],
				count: '',
				todayneedcount: '',
				todaycount: '',
				searchKey: '',
				currentPage: 1, // 当前页码
				pageSize: 10, // 每页条数
				total: 100, // 总数据量
				isLoading: false, // 加载状态
				noMoreData: false, // 是否没有更多数据
				followupAt: null, // 筛选参数
				count1: 0,
				count2: 1,
				listArrs: {},
				listKey: 0,
				userInfoList: [],
				follow_status: '',
				customer_source: '',
				belong_user_ids: '',
				regions: ''
			}
		},
		mounted() {
			this.fetchAssignUsers()
			this.fetchData();
		},
		onShow() {
			this.fetchAssignUsers()
			this.fetchData();
		},
		onReachBottom() {
			console.log('触发滚动到底部事件', this.total < this.pageSize) // 调试日志
			// if (!this.noMoreData) {
				
			// }
			if(this.total > this.pageSize){
				this.pageSize += 10
				this.isLoading = true
				this.fetchData();
			} else{
				this.noMoreData = false
			}
		},
		computed: {
			// 今日需跟进数量
			// todayNeedCount() {
			// 	return this.allData.filter(item => item.todayneedfollo === 1).length;
			// },
			// // 今日已跟进数量
			// todayCount() {
			// 	return this.allData.filter(item => item.todayfollow === 1).length;
			// }
		},
		methods: {
			fetchOptions() {
				this.$api.request('common/searchCustomerTermList', {}, (res)=>{
					if (res.status === 'ok') {
						this.listArrs = res.list
						this.listKey = this.listKey + 1
						console.log(this.listArrs, 'this.listArrs')
					}
				})
			},
			toggleFilter(value) {
				this.showSelectModal = value;
			},
			applyFilters(filters) {
				// 根据筛选条件进行筛选操作
				console.log('筛选条件:', filters);
				this.regions = filters.area instanceof Array ? filters.area.join(',') : ''
				this.follow_status = filters.channel instanceof Array ? filters.channel.join(',') : ''
				this.customer_source = filters.status
				this.belong_user_ids = filters.user instanceof Array ? filters.user.join(',') : ''
				this.pageSize = 10
				console.log(this.regions, this.follow_status, this.customer_source, this.belong_user_ids )
				this.fetchData();
				this.showSelectModal = false;
			},
			// 打开选择弹框
			openSelectModal() {
				this.showSelectModal = true
			},

			// 打开分配模态框
			openfenpeiModal(clueId) {
				this.clueId = clueId;
				this.fetchAssignUsers();
				this.showAssignModal = true;
			},
			
			// 选择用户
			selectUser(userId, userLabel) {
				this.selectedUserId = userId;
				this.selectedUserLabel = userLabel;
			},

			// 获取可分配用户列表
			fetchAssignUsers() {
				const token = uni.getStorageSync('token');
				this.assignLoading = true;
				uni.request({
					url: 'https://bodhis.kaikungroup.com/api/mini.user/getUserList',
					method: 'GET',
					header: {
						// 'token': 'Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM'
						token: token
					},
					success: (res) => {
						if (res.statusCode === 200) {
							this.userInfoList = res.data.user_info
							this.fetchOptions();
						}
					},
					complete: () => {
						this.assignLoading = false;
					}
				});
			},


			// 确认分配
			confirmAssign() {
				if (!this.selectedUserId) {
					return uni.showToast({
						title: '请选择分配用户',
						icon: 'none'
					});
				}

				uni.showModal({
					title: '确认分配',
					content: `确认将该线索分配给 ${this.selectedUserLabel}？`,
					success: (res) => {
						if (res.confirm) {
							this.assignClue();
						}
					}
				});
			},

			// 关闭分配模态框（重置选中状态）
			closeAssignModal() {
				this.showAssignModal = false;
				this.selectedUserId = null; // 清除选中状态
			},



			// 执行分配操作
			assignClue() {
				const token = uni.getStorageSync('token');
				uni.request({
					url: 'https://bodhis.kaikungroup.com/api/mini.customer/assign',
					method: 'POST',
					header: {
						// 'token': 'Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM'
						token: token
					},
					data: {
						clueId: this.clueId,
						userId: this.selectedUserId
					},
					success: (res) => {
						if (res.statusCode === 200) {
							uni.showToast({
								title: '分配成功'
							});
							this.showAssignModal = false;
							this.fetchData(); // 重新获取数据
						} else {
							uni.showToast({
								title: '分配失败',
								icon: 'none'
							});
						}
					}
				});
			},
			search() {
				this.currentPage = 1; // 重置页码
				this.pageSize = 10
				// this.noMoreData = false;
				this.fetchData(); // 重新获取数据
			},
			// 从接口获取数据
			fetchData() {
				let that = this
				// const token =
				// 	"Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM";
				const token = uni.getStorageSync('token');
				uni.request({
					url: 'https://bodhis.kaikungroup.com/api/mini.customer/listing', // 替换为实际的接口地址
					method: 'POST',
					data: {
						pageNo: this.currentPage,
						pageSize: this.pageSize,
						type: this.activeTab,
						follow_status: this.follow_status,
						customer_source: this.customer_source,
						belong_user_ids: this.belong_user_ids,
						regions: this.regions,
						search: this.searchKey
					},
					header: {
						'token': token,
					},
					success: (res) => {
						if (res.statusCode === 200) {
							that.isLoading = false
							that.allData = res.data.list;
							that.count = res.data.total
							that.total = res.data.count
							that.todayneedcount = res.data.todayneedcount;
							that.todaycount = res.data.todaycount;
							that.count2 = res.data.todayneedfolloCount	
							that.count1 = res.data.todayfollowCount
							this.filteredListData = res.data.list;
						} else {
							console.error('请求失败:', res.errMsg);
						}
					},
					fail: (err) => {
						console.error('请求出错:', err);
					}
				});
			},
			
			// 修改过滤方法
			filterData() {
				let filtered = this.allData;

				// 选项卡过滤
				if (this.activeTab === 1) {
					filtered = filtered.filter(item => item.todayneedfollo == 1);
				} else if (this.activeTab === 2) {
					filtered = filtered.filter(item => item.todayfollow === 1);
				}

				// 搜索过滤（支持多字段匹配）
				if (this.searchKey.trim()) {
					const keyword = this.searchKey.trim().toLowerCase();
					filtered = filtered.filter(item =>
						item.clue_name.toLowerCase().includes(keyword) ||
						item.phone.includes(keyword) ||
						item.user_name.toLowerCase().includes(keyword)
					);
				}

				this.filteredListData = filtered;
			},


			changeTab(index) {
				if (this.activeTab === index) return;

				this.activeTab = index;
				this.currentPage = 1; // 重置页码
				this.pageSize = 10; // 重置页码
				this.searchKey = '';
				this.noMoreData = false; // 重置数据状态
				this.allData = []; // 清空旧数据
				this.fetchData();
				
			},

			//添加客户
			AddCustomer() {
				uni.navigateTo({
					url: '/crm/customer/addCustomer',
				})
			},

			// 拨打电话处理
			handleTel(tel) {
				uni.makePhoneCall({
					phoneNumber: tel
				});
			},

			// 跳转页面处理
			handleNavigate(path, param, id) {
				// const query = this.objectToQuery(param);
				uni.navigateTo({
					url: `${path}?id=${id}`
				});
			},
			// 修改
			modify(path, id) {
				uni.navigateTo({
					url: path + '?id=' + id
				});
				
			},
			// 执行功能处理
			handleFunc(api, path, param, id) {
				// 这里可以添加请求接口逻辑
				this.$api.request(api, {id: id}, (res)=>{
					uni.showToast({
						icon: 'none',
						title: res.info
					})
					// this.fetchData();
					uni.navigateTo({
						url: path
					})
				} )
			},

			// 对象转查询字符串
			objectToQuery(obj) {
				return Object.entries(obj).map(([k, v]) => `${k}=${v}`).join('&');
			},




		}
	}
</script>

<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		padding: 30rpx 0;
		text-align: center;
		background-color: #ffffff;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.tabs {
		display: flex;
		justify-content: space-around;
		background-color: #ffffff;
		padding-bottom: 10rpx;
	}

	.tab-item {
		position: relative;
		padding: 20rpx 0;
		flex: 1;
		text-align: center;
		font-size: 30rpx;
		color: #333;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.active {
		color: #00AAFF;
		font-weight: bold;
	}

	.active-line {
		position: absolute;
		bottom: 0;
		width: 60rpx;
		height: 6rpx;
		background-color: #00AAFF;
		border-radius: 3rpx;
	}

	.badge {
		background-color: #FF4D4F;
		color: white;
		font-size: 24rpx;
		height: 36rpx;
		min-width: 36rpx;
		padding: 0 6rpx;
		border-radius: 18rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 6rpx;
	}

	.search-container {
		padding: 20rpx;
		display: flex;
		align-items: center;
	}

	.search-box {
		flex: 1;
		height: 80rpx;
		background-color: #ffffff;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		border: 1rpx solid #EEEEEE;
	}

	.search-box input {
		flex: 1;
		height: 80rpx;
		margin-left: 10rpx;
		font-size: 28rpx;
	}

	.placeholder {
		color: #AAAAAA;
		font-size: 28rpx;
	}

	.action-buttons {
		display: flex;
		margin-left: 20rpx;
		gap: 20rpx;
		/* 使用Flex gap属性 */
	}

	.search-btn,
	.add-btn {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: #A5CAFF;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
	}

	.list-container {
		flex: 1;
		padding: 0 20rpx;
	}

	.list-item {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
		margin-bottom: 20rpx;
	}

	.item-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.item-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.item-tag {
		font-size: 24rpx;
		color: #A5CAFF;
		background-color: rgba(165, 202, 255, 0.1);
		padding: 4rpx 12rpx;
		border-radius: 4rpx;
	}

	.item-info {
		margin-bottom: 16rpx;
	}

	.info-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15rpx 30rpx;
	}

	.info-grid-item {
		display: flex;
		align-items: center;
		font-size: 26rpx;
	}
	.info-grid-items {
		display: flex;
		width: 100%;
		align-items: center;
		font-size: 26rpx;
		margin-top: 15rpx;
		.info-label {
			color: #999;
			min-width: 100rpx;
			flex-shrink: 0;
		}
		.info-value {
			color: #666;
			flex: 1;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
	.info-label {
		color: #999;
		min-width: 100rpx;
		flex-shrink: 0;
	}

	.info-value {
		color: #666;
		flex: 1;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.item-divider {
		height: 1rpx;
		background-color: #f0f0f0;
		margin: 10rpx 0 16rpx 0;
	}

	.item-footer {
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.action-btns {
		display: flex;
		gap: 10rpx;
		flex-wrap: wrap;
		/* 添加换行功能 */
		max-width: 100%;
		/* 可选：限制容器最大宽度 */
	}

	.action-btn {
		padding: 8rpx 11rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: 90rpx;
		flex-shrink: 0;
		/* 防止按钮被压缩 */
	}

	.view-btn {
		background-color: #f0f7ff;
		color: #3467C2;
		border: 1rpx solid #d6e6ff;
	}

	.call-btn {
		background-color: #e6f7f0;
		color: #27ae60;
		border: 1rpx solid #c8ebd7;
	}

	.edit-btn {
		background-color: #fff7e6;
		color: #f39c12;
		border: 1rpx solid #ffeac8;
	}

	.tab-bar {
		height: 100rpx;
		background-color: #ffffff;
		display: flex;
		justify-content: space-around;
		align-items: center;
		border-top: 1rpx solid #EEEEEE;
	}

	.tab-bar-item {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.tab-text {
		font-size: 24rpx;
		color: #999999;
		margin-top: 6rpx;
	}

	.active-text {
		color: #00AAFF;
	}

	.loading-status {
		text-align: center;
		padding: 60rpx;
		padding-bottom: 100rpx;
		color: #999;
		font-size: 28rpx;
	}

	// 点击态效果
	.action-btn {
		transition: all 0.2s;

		&--active {
			transform: scale(0.95);
			opacity: 0.8;
		}

		// 不同按钮的激活态颜色
		&.view-btn--active {
			background-color: #d6e6ff !important;
		}

		&.call-btn--active {
			background-color: #c8ebd7 !important;
		}

		&.edit-btn--active {
			background-color: #ffeac8 !important;
		}
	}


	//弹出层
	.assign-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 999;

		.modal-mask {
			position: absolute;
			width: 100%;
			height: 100%;
			background-color: rgba(0, 0, 0, 0.5);
		}

		.modal-content {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
			width: 90%;
			max-width: 600rpx;
			background-color: #fff;
			border-radius: 20rpx;
			padding: 40rpx;

			.modal-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 30rpx;

				.modal-title {
					font-size: 36rpx;
					font-weight: bold;
				}

				.modal-close {
					font-size: 40rpx;
					color: #999;
					cursor: pointer;
				}
			}

			.modal-body {
				max-height: 400rpx;
				overflow-y: auto;
				margin-bottom: 30rpx;
			}

			.user-btn-group {
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				gap: 15rpx;
			}

			.user-btn {
				padding: 12rpx 24rpx;
				border: 1rpx solid #e0e0e0;
				border-radius: 8rpx;
				font-size: 28rpx;
				transition: all 0.2s;
				min-width: auto;

				&.active-btn {
					background-color: #007AFF !important; // 添加 !important 强制应用样式
					color: #fff !important;
					border-color: #007AFF !important;
				}

				&:hover {
					background-color: #f5f5f5;
				}
			}

			.modal-footer {
				display: flex;
				justify-content: flex-end;
				gap: 20rpx;
				margin-top: 30rpx;

				.btn-cancel {
					padding: 15rpx 30rpx;
					border: 1rpx solid #ddd;
					border-radius: 10rpx;
					background-color: #fff;
					font-size: 30rpx;
					cursor: pointer;
				}

				.btn-confirm {
					padding: 15rpx 30rpx;
					background-color: #007AFF; // 确认分配按钮颜色
					color: #fff;
					border-radius: 10rpx;
					font-size: 30rpx;
					cursor: pointer;
				}
			}
		}
	}
</style>