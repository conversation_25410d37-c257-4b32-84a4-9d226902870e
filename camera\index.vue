<template>
  <view class="camera-page">
    <button @click="takePhoto">拍照</button>
    <canvas v-if="showCanvas" :canvas-id="'watermarkCanvas'" :style="canvasStyle" />
    <image v-if="finalImage" :src="finalImage" mode="widthFix" :style="imageStyle" />
    <button v-if="finalImage" @click="saveImage">保存到相册</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      showCanvas: false,
      finalImage: '',
      photoPath: '',
      latitude: '',
      longitude: '',
      imgWidth: 0,
      imgHeight: 0,
      locationName: '',
    }
  },
  computed: {
    canvasStyle() {
      if (!this.imgWidth || !this.imgHeight) return '';
      return `width:${this.imgWidth}px;height:${this.imgHeight}px;position:absolute;left:0;top:0;z-index:10;`;
    },
    imageStyle() {
      if (!this.imgWidth || !this.imgHeight) return '';
      return `width:${this.imgWidth}px;max-width:100vw;display:block;margin-top:40rpx;`;
    }
  },
  methods: {
    async takePhoto() {
      await this.getLocation();
      uni.chooseImage({
        count: 1,
        sizeType: ['original'],
        sourceType: ['camera'],
        success: (res) => {
          this.photoPath = res.tempFilePaths[0];
          uni.getImageInfo({
            src: this.photoPath,
            success: (imgInfo) => {
              this.imgWidth = imgInfo.width;
              this.imgHeight = imgInfo.height;
              this.drawWatermark();
            }
          });
        }
      });
    },
    async getLocation() {
      return new Promise((resolve) => {
        uni.getLocation({
          type: 'wgs84',
          success: (res) => {
            this.latitude = res.latitude;
            this.longitude = res.longitude;
            // 逆地理编码获取地名
            this.getLocationName(res.latitude, res.longitude).then(() => {
              resolve();
            });
          },
          fail: () => {
            this.latitude = '';
            this.longitude = '';
            this.locationName = '';
            resolve();
          }
        });
      });
    },
    // 逆地理编码获取地名（腾讯位置服务示例）
    getLocationName(lat, lng) {
      return new Promise((resolve) => {
        uni.request({
          url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${lat},${lng}&key=VAUBZ-TE2CU-MCLV4-GQQ6S-MS4XE-B7BQK&get_poi=0`,
          success: (res) => {
            if (res.data && res.data.result && res.data.result.address) {
              this.locationName = res.data.result.address;
            } else {
              this.locationName = '';
            }
            resolve();
          },
          fail: () => {
            this.locationName = '';
            resolve();
          }
        });
      });
    },
    drawArcText(ctx, text, centerX, centerY, radius, startAngle, endAngle, fontSize, color, clockwise = true) {
      // 计算每个字的弧度
      const len = text.length;
      const angleRange = endAngle - startAngle;
      const angleStep = angleRange / (len - 1);
      ctx.save && ctx.save();
      ctx.setFontSize(fontSize);
      ctx.setFillStyle(color);
      for (let i = 0; i < len; i++) {
        const char = text[i];
        const angle = clockwise
          ? startAngle + i * angleStep
          : endAngle - i * angleStep;
        ctx.save && ctx.save();
        ctx.translate(centerX + radius * Math.cos(angle), centerY + radius * Math.sin(angle));
        ctx.rotate(angle + (clockwise ? Math.PI / 2 : -Math.PI / 2));
        ctx.fillText(char, 0, 0);
        ctx.restore && ctx.restore();
      }
      ctx.restore && ctx.restore();
    },
    drawWatermark() {
      this.showCanvas = true;
      const ctx = uni.createCanvasContext('watermarkCanvas', this);
      ctx.clearRect && ctx.clearRect(0, 0, this.imgWidth, this.imgHeight);
      ctx.drawImage(this.photoPath, 0, 0, this.imgWidth, this.imgHeight);
      const margin = 30;
      // 圆形尺寸和位置
      const base = Math.min(this.imgWidth, this.imgHeight);
      const circleRadius = Math.max(70, Math.floor(base * 0.13));
      const circleX = this.imgWidth - margin - circleRadius;
      const circleY = this.imgHeight - margin - circleRadius;
      // 画圆形背景
      ctx.save && ctx.save();
      ctx.beginPath && ctx.beginPath();
      ctx.arc(circleX, circleY, circleRadius, 0, 2 * Math.PI);
      ctx.setFillStyle('rgba(255,169,64,0.18)');
      ctx.fill && ctx.fill();
      ctx.closePath && ctx.closePath();
      ctx.restore && ctx.restore();
      // 印章弧形文字
      const fontSize = Math.floor(circleRadius * 0.18);
      // 上半弧：地区名称（合并为一行，逐字贴合）
      let areaText = '';
      if (this.locationName) {
        if (this.locationName.length > 8) {
          const mid = Math.ceil(this.locationName.length / 2);
          areaText = this.locationName.slice(0, mid) + ' ' + this.locationName.slice(mid);
        } else {
          areaText = this.locationName;
        }
        // 上半弧：Math.PI*1.1 ~ Math.PI*1.9
        this.drawArcText(ctx, areaText, circleX, circleY, circleRadius - fontSize, Math.PI * 1.1, Math.PI * 1.9, fontSize, 'rgba(255,169,64,0.98)', true);
      }
      // 下半弧：时间
      const timeText = this.formatTime(new Date());
      if (timeText) {
        // 下半弧：Math.PI*0.1 ~ Math.PI*0.9
        this.drawArcText(ctx, timeText, circleX, circleY, circleRadius - fontSize, Math.PI * 0.1, Math.PI * 0.9, fontSize, 'rgba(255,215,0,0.92)', true);
      }
      // 圆心：博笛智家
      ctx.setFontSize(Math.floor(circleRadius * 0.22));
      ctx.setFontWeight && ctx.setFontWeight('bold');
      ctx.setFillStyle('rgba(255,169,64,0.98)');
      ctx.setTextAlign('center');
      ctx.setTextBaseline && ctx.setTextBaseline('middle');
      ctx.setShadow(4, 4, 12, 'rgba(0,0,0,0.35)');
      ctx.fillText('博笛智家', circleX, circleY);
      ctx.setShadow(0,0,0,'rgba(0,0,0,0)');
      ctx.draw(false, () => {
        uni.canvasToTempFilePath({
          canvasId: 'watermarkCanvas',
          width: this.imgWidth,
          height: this.imgHeight,
          success: (res) => {
            this.finalImage = res.tempFilePath;
            this.showCanvas = false;
          },
          fail: () => {
            this.showCanvas = false;
          }
        }, this);
      });
    },
    formatTime(date) {
      const y = date.getFullYear();
      const m = (date.getMonth() + 1).toString().padStart(2, '0');
      const d = date.getDate().toString().padStart(2, '0');
      const h = date.getHours().toString().padStart(2, '0');
      const min = date.getMinutes().toString().padStart(2, '0');
      const s = date.getSeconds().toString().padStart(2, '0');
      return `${y}-${m}-${d} ${h}:${min}:${s}`;
    },
    saveImage() {
      uni.saveImageToPhotosAlbum({
        filePath: this.finalImage,
        success: () => {
          uni.showToast({ title: '保存成功', icon: 'success' });
        },
        fail: () => {
          uni.showToast({ title: '保存失败', icon: 'none' });
        }
      });
    }
  }
}
</script>

<style scoped>
.camera-page {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
button {
  margin: 20rpx 0;
  width: 80vw;
  font-size: 36rpx;
  background: #2563eb;
  color: #fff;
  border-radius: 12rpx;
}
</style>
