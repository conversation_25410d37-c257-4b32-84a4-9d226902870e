<template>
	<view v-if="openModal" class="wx-modal">
		<view class="wam__mask" @touchmove.prevent="" @tap.stop="closeModal"></view>
		<view class="wam__wrapper">
			<view class="wam__close-btn" @tap.stop="closeModal">
				<tn-button size="lg" shape="icon" backgroundColor="#00000000" fontColor="#AAAAAA">
					<text class="tn-icon-close"></text>
				</tn-button>
			</view>
			<view class="tn-margin-left tn-flex tn-flex-col-center">
				<view class="tn-text-bold tn-text-lg">综合筛选</view>
			</view>

			<view class="tn-margin-top-sm" style="border-top: 1rpx solid #F8F7F8;">

				<scroll-view scroll-y="true" style="max-height: 55vh;">
					<view class="tn-padding-top-sm" style="margin: 30rpx;">

                        <!-- 是否为公海 -->
                        <view class="tn-margin-top-xl" v-if="names == 'crm'">
							公海筛选
						</view>

						<view class="justify-content-item" v-if="names == 'crm'">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in highSeasArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="highClick(index)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 区域选择改为单选按钮 -->
						<view class="tn-margin-top-xl">
							区域选择
						</view>

						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in areas" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleAreaClick(index, item.name)">
									{{ item.title }}
								</view>
							</view>
						</view>

						<!-- 渠道单选 -->
						<view class="tn-margin-top-xl">
							渠道类型
						</view>

						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in channels" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleChannelClick(index, item.ext_value)">
									{{ item.title }}
								</view>
							</view>
						</view>

						<!-- 状态单选 -->
						<view class="tn-margin-top-xl">
							状态筛选
						</view>

						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in statuses" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleStatusClick(index)">
									{{ item.title }}
								</view>
							</view>
						</view>
						
						<view class="tn-margin-top-xl" v-if="infoList.length">
							负责人
						</view>
						<view class="justify-content-item" v-if="infoList.length">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in infoList" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLastFollowUpClick(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 新增最后跟进时间 -->
						<view class="tn-margin-top-xl">
							最后跟进时间
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in lastFollowUp" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLastFollowTimer(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 意向 -->
						<view class="tn-margin-top-xl">
							意向
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in intention" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas5(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 层数 -->
						<view class="tn-margin-top-xl">
							层数
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in layersArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas6(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 结构形式 --> 
						<view class="tn-margin-top-xl">
							结构形式
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in structureArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas7(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 建房时效 --> 
						<view class="tn-margin-top-xl">
							建房时效
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in housingArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas8(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 宅基地面积 -->
						<view class="tn-margin-top-xl"> 
							宅基地面积
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in homesteadArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas9(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 预算 -->
						<view class="tn-margin-top-xl">
							预算	
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in budgetArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas0(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 建房风格 -->
						<view class="tn-margin-top-xl">
							建房风格
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in housingStyle" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas11(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 审批情况 -->
						<view class="tn-margin-top-xl">
							审批情况
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in approvalStatus" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas12(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 收入来源 -->
						<view class="tn-margin-top-xl">
							收入来源
						</view>
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in incomeSource" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas13(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						<!-- 性别 -->
						<view class="tn-margin-top-xl">
							性别
						</view>
						
						<view class="justify-content-item">
							<view class="tn-tag-content tn-text-justify">
								<view v-for="(item, index) in sexArr" :key="index"
									class="tn-tag-content__item tn-margin-right tn-text-sm"
									:class="[item.select ? 'bg-select' : 'bg-select-no']"
									@click="handleLas14(index, item.id)">
									{{ item.title }}
								</view>
							</view>
						</view>
						
						
							
						

					</view>
				</scroll-view>

				<view class="tn-flex tn-footerfixed" style="border-top: 1rpx solid #F8F7F8;">
					<view class="tn-flex-1 justify-content-item tn-text-center tn-margin-right-sm">
						<tn-button :plain="true" backgroundColor="#AAAAAA " padding="39rpx 0" width="100%"
							:fontSize="28" fontColor="#AAAAAA" shape="round" @click="resetFilters">
							<text class="">重 置</text>
						</tn-button>
					</view>
					<view class="tn-flex-1 justify-content-item tn-text-center tn-margin-left-sm">
						<tn-button backgroundColor="#9ebeff " padding="40rpx 0" width="100%" :fontSize="28"
							fontColor="#FFFFFF" shape="round" @click="confirmFilters">
							<text class="">确 定</text>
						</tn-button>
					</view>
				</view>

				<view class="tn-tabbar-height"></view>

			</view>

		</view>
	</view>
</template>

<script>
	export default {
		options: {
			virtualHost: true
		},
		props: {
			value: {
				type: Boolean,
				default: false
			},
			listArrs: {
				type: Object,
				default: {}
			},
			userInfoList: {
				type: Array,
				default: []
			},
            names:  {
				type: String,
				default: ''
			},
		},
		data() {
			return {
				openModal: false,
				// 区域选择改为单选按钮
				areas: [],
				channels: [],
                highSeasArr: [],
                highSeasValue: '',
				statuses: [],
				intention: [],
				layersArr: [],
				structureArr: [],
				housingArr: [],
				homesteadArr: [],
				housingStyle: [],
				incomeSource: [],
				sexArr: [],
				// 新增最后跟进时间
				lastFollowUp: [],
				selectArr1: [],
				selectArr2: [],
                selectArrName: [],
				selectArr3: [],	
				selectArr4: [],	
				selectArr14: [],
				selectArr5: [],
				selectArr6: [],
				selectArr7: [],
				selectArr8: [],
				selectArr9: [],
				selectArr11: [],
				selectArr12: [],
				selectArr13: [],
				infoList: [],
				approvalStatus: [],
				budgetArr: [],
				// 新增创建时间
				startDate: '',
				endDate: ''
			}
		},
		watch: {
			value: {
				handler(val) {
					this.openModal = val
				},
				immediate: true
			}
		},
		mounted() {
			console.log('打开')
			console.log(this.$props.listArrs)
			console.log(this.names)
            // this.isTrueName = this.names
            this.highSeasArr =  [{title: '是否为公海', id: '1', select: false }]
			this.lastFollowUp = this.listArrs.create_at.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.areas = this.listArrs.region.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.statuses = this.listArrs.follow_status.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.channels = this.listArrs.customer_source.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			// 意向 intention
			this.intention = this.listArrs.intention.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.layersArr = this.listArrs.layers.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.housingArr = this.listArrs.housing_validity.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			
			this.structureArr = this.listArrs.structure.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.homesteadArr = this.listArrs.homestead.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.housingStyle = this.listArrs.housing_style.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			
			this.approvalStatus = this.listArrs.approval_status.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.budgetArr = this.listArrs.budget.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			
			this.incomeSource = this.listArrs.income_source.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			this.sexArr = this.listArrs.sex.map(item => ({
				...item,
				title: item.name,
				select: false
			}));
			// 筛选
			// this.channels = this.listArrs.customer_source.map(item => ({
			// 	...item, 
			// 	title: item.name,
			// 	select: false
			// }));
			

			
	
			this.infoList = this.userInfoList.map(item => ({
				...item,	
				title: item.real_name,
				select: false
			}));

		},


		methods: {
			tn(e) {
				uni.navigateTo({
					url: e,
				});
			},
			closeModal() {
				this.$emit('input', false);
				this.openModal = false;
			},
			// 区域单选处理
			handleAreaClick(index, name) {
				this.areas[index].select = !this.areas[index].select
				this.selectArr1 = this.selectArr1.includes(name) ? this.selectArr1.filter(item => item !== name) : [...this
					.selectArr1, name
				];
				console.log(this.areas, this.selectArr1)
			},
			// 渠道单选处理
			handleChannelClick(index, name) {
				this.channels[index].select = !this.channels[index].select
				this.selectArr2 = this.selectArr2.includes(name) ? this.selectArr2.filter(item => item !== name) : [...this.selectArr2, name];
				this.selectArrName = this.selectArrName.includes(this.channels[index].name) ? this.selectArrName.filter(item => item !== this.channels[index].name) : [...this.selectArrName,  this.channels[index].name];
				console.log('selectArr2', this.selectArr2)
				console.log('selectArr2', name)
				console.log('selectArr2', this.channels[index].name)
				// this.channels.forEach((item, i) => item.select = (i === index));
			},
            highClick(index){
                this.highSeasArr.forEach((item, i) => item.select = (i === index));
            },
			// 状态单选处理
			handleStatusClick(index) {
				this.statuses.forEach((item, i) => item.select = (i === index));
			},
			handleLas5(index, name){
				this.intention[index].select = !this.intention[index].select
				this.selectArr5 = this.selectArr5.includes(name) ? this.selectArr5.filter(item => item !== name) : [...this.selectArr5, name];
			},
			handleLas6(index, name){
				this.layersArr[index].select = !this.layersArr[index].select
				this.selectArr6 = this.selectArr6.includes(name) ? this.selectArr6.filter(item => item !== name) : [...this.selectArr6, name];
			},
			handleLas7(index, name){		
				this.structureArr[index].select = !this.structureArr[index].select
				this.selectArr7 = this.selectArr7.includes(name) ? this.selectArr7.filter(item => item !== name) : [...this.selectArr7, name];
			},
			handleLas8(index, name){
				this.housingArr[index].select = !this.housingArr[index].select
				this.selectArr8 = this.selectArr8.includes(name) ? this.selectArr8.filter(item => item !== name) : [...this.selectArr8, name];
			},
			handleLas9(index, name){
				this.homesteadArr[index].select = !this.homesteadArr[index].select
				this.selectArr9 = this.selectArr9.includes(name) ? this.selectArr9.filter(item => item !== name) : [...this.selectArr9, name];
			},
			handleLas0(index, name){
				this.budgetArr[index].select = !this.budgetArr[index].select
				this.selectArr0 = this.selectArr0.includes(name) ? this.selectArr0.filter(item => item !== name) : [...this.selectArr0, name];
			},
			handleLas11(index, name){
				this.housingStyle[index].select = !this.housingStyle[index].select
				this.selectArr11 = this.selectArr11.includes(name) ? this.selectArr11.filter(item => item !== name) : [...this.selectArr11, name];
			},
			handleLas12(index, name){
				this.approvalStatus[index].select = !this.approvalStatus[index].select
				this.selectArr12 = this.selectArr12.includes(name) ? this.selectArr12.filter(item => item !== name) : [...this.selectArr12, name];
			},
			handleLas13(index, name){
				this.incomeSource[index].select = !this.incomeSource[index].select
				this.selectArr13 = this.selectArr13.includes(name) ? this.selectArr13.filter(item => item !== name) : [...this.selectArr13, name];
			},	
			handleLas14(index, name){
				// this.sexArr[index].select = !this.sexArr[index].select
				// this.selectArr14 = this.selectArr14.includes(name) ? this.selectArr14.filter(item => item !== name) : [...this.selectArr14, name];
				this.sexArr.forEach((item, i) => item.select = (i === index));
			},
			// 最后跟进时间处理
			handleLastFollowUpClick(index, name) {
				this.infoList[index].select = !this.infoList[index].select
				this.selectArr3 = this.selectArr3.includes(name) ? this.selectArr3.filter(item => item !== name) : [...this.selectArr3, name];
				console.log('selectArr3', this.selectArr3)
			},
			// 最后跟进时间处理
			handleLastFollowTimer(index, name) {
				// this.lastFollowUp.forEach((item, i) => item.select = (i === index));
				// this.lastFollowUp[index].select = !this.lastFollowUp[index].select
				this.lastFollowUp.forEach((item, i) => item.select = (i === index));
				// this.selectArr4 = this.selectArr4.includes(name) ? this.selectArr4.filter(item => item !== name) : [...this.selectArr4, name];
			},
			// 重置筛选条件
			resetFilters() {
				this.areas.forEach(item => item.select = false);
				this.channels.forEach(item => item.select = false);
				this.statuses.forEach(item => item.select = false);
                this.highSeasArr.forEach(item => item.select = false);
				this.lastFollowUp.forEach(item => item.select = false);
				this.infoList.forEach(item => item.select = false)
				this.intention.forEach(item => item.select = false)
				this.layersArr.forEach(item => item.select = false)
				this.structureArr.forEach(item => item.select = false)
				this.housingArr.forEach(item => item.select = false)
				this.homesteadArr.forEach(item => item.select = false)
				this.budgetArr.forEach(item => item.select = false)
				this.housingStyle.forEach(item => item.select = false)
				this.approvalStatus.forEach(item => item.select = false)
				this.incomeSource.forEach(item => item.select = false)
				this.sexArr.forEach(item => item.select = false)
				this.startDate = '';
				this.endDate = '';
				this.selectArr1 = ""
				this.selectArr2 = ""
				this.selectArr3 = ""
				this.selectArr4 = ""
                this.selectArrName = ""
				this.selectArr5 = ""
				this.selectArr6 = ""
				this.selectArr7 = ""
				this.selectArr8 = ""
				this.selectArr9 = ""
				this.selectArr0 = ""
				this.selectArr11 = ""
				this.selectArr12 = ""
				this.selectArr13 = ""
				this.selectArr14 = ""
			},
			// 确定筛选条件
			confirmFilters() {
				const selectedStatus = this.statuses.find(item => item.select)?.id;
				const highSeasValue = this.highSeasArr.find(item => item.select)?.id;
                
				const timer = this.lastFollowUp.find(item => item.select)?.id;
				const sexId = this.sexArr.find(item => item.select)?.name;
				
				console.log(this.selectArr5)
				// return
				// 结构形式
				const filters = {
					area: this.selectArr1,
					channel: this.selectArr2,
                    channelname: this.selectArrName,
					status: selectedStatus,
					user: this.selectArr3,
                    high_seas: highSeasValue,
					structure: this.selectArr7,
					sex: sexId,
					income_source: this.selectArr13,
					housing_style: this.selectArr11,
					homestead: this.selectArr9,
					housing_validity: this.selectArr8,
					approval_status: this.selectArr12,
					layers: this.selectArr6,
					budget: this.selectArr0,
					intention: this.selectArr5,
					next_followup_at: timer
				};
				this.$emit('filter', filters);
				this.closeModal();
			}
		}
	}
</script>
<style lang="scss" scoped>
	/* 文字截取*/
	.clamp-text-1 {
		-webkit-line-clamp: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	.clamp-text-2 {
		-webkit-line-clamp: 2;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		text-overflow: ellipsis;
		overflow: hidden;
	}


	.wx-modal {
		position: fixed;
		left: 0;
		top: 0;
		width: 100vw;
		height: 100vh;
		z-index: 99998 !important;

		view {
			box-sizing: border-box;
		}

		.image {
			width: 100%;
			height: 100%;
			border-radius: inherit;
		}

		.wam {
			z-index: 9999 !important;

			/* mask */
			&__mask {
				position: absolute;
				left: 0;
				top: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.5);
				opacity: 0;
				animation: showMask 0.25s linear 0s forwards;
			}

			/* close-btn */
			&__close-btn {
				position: absolute;
				top: 20rpx;
				right: 10rpx;
				z-index: 99999;
				font-size: 40rpx;
			}

			/* wrapper */
			&__wrapper {
				position: absolute;
				left: 0;
				bottom: 0;
				width: 100%;
				background-color: #FFFFFF;
				border-radius: 40rpx 40rpx 0rpx 0rpx;
				padding-top: 40rpx;
				// padding-bottom: 40rpx;
				// padding-bottom: calc(constant(safe-area-inset-bottom) + 40rpx);
				// padding-bottom: calc(env(safe-area-inset-bottom) + 40rpx);
				transform-origin: center bottom;
				transform: scaleY(0);
				animation: showWrapper 0.25s linear 0s forwards;
				z-index: 99999;
			}

		}
	}

	@keyframes showMask {
		0% {
			opacity: 0;
		}

		100% {
			opacity: 1;
		}
	}

	@keyframes showWrapper {
		0% {
			transform: scaleY(0);
			opacity: 0;
		}

		100% {
			transform: scaleY(1);
			opacity: 1;
		}
	}

	/* 底部悬浮按钮 start*/
	.tn-tabbar-height {
		min-height: 20rpx;
		height: calc(40rpx + env(safe-area-inset-bottom) / 2);
		height: calc(40rpx + constant(safe-area-inset-bottom));
	}

	.tn-footerfixed {
		width: 100%;
		// margin-bottom: calc(env(safe-area-inset-bottom)- 20rpx );
		padding: 30rpx;
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);

	}

	/* 底部悬浮按钮 end*/

	/* 标签内容 start*/
	.tn-tag-content {
		&__item {
			display: inline-block;
			line-height: 45rpx;
			padding: 8rpx 24rpx;
			margin: 24rpx 30rpx 5rpx 0rpx;
			border-radius: 100rpx;
			min-width: 140rpx;
			text-align: center;

			&--prefix {
				padding-right: 10rpx;
			}
		}
	}

	/* 标签内容 end*/

	.bg-select-no {
		background-color: #F5F5F5;
		border: 1rpx solid #F5F5F5;
	}

	.bg-select {
		background-color: #9ebeff20;
		border: 1rpx solid #9ebeff;
		color: #9ebeff;
	}
</style>