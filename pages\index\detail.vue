<template>
	<view class="container">
		<view class="custom">
			<view class="tn-flex tn-flex-row-between tn-flex-col-center" style="padding: 30rpx 30rpx 0 30rpx;">
				<view class="justify-content-item">
					<view class="tn-text-bold">
						<text class="tn-text-xxl">{{detail.RealName}}</text>
					</view>
				</view>
			</view>
			<view class="tn-text-md" style="padding: 30rpx 30rpx 15rpx 30rpx;">
				<view class="tn-flex  tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-phone"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">{{detail.Mobile}}</view>
				</view>
				<view class="tn-flex tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-location"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">{{detail.Province}} {{detail.City}}
						{{detail.County}} {{detail.Town}}
					</view>
				</view>
				<view class="tn-flex tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-up-circle"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">{{detail.Village}} {{detail.AddressBuild}}
					</view>
				</view>
				<view v-if="detail.Distance" class="tn-flex tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-signpost"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">距离 {{detail.Distance || "-"}}</view>
				</view>
				<view class="tn-flex tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-warning"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">级别 {{detail.Classify}} </view>
				</view>
				<view class="tn-flex tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-more-circle"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">{{detail.Describe}}</view>
				</view>
				<view class="tn-flex tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-time"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">{{detail.CreateTime}}</view>
				</view>
			</view>
		</view>

		<view class="tn-flex api-box-90 tn-padding-top-sm">
			<view class="tn-flex-1 justify-content-item tn-margin-right-xs tn-text-center tn-bg-white"
				style="border-radius: 10rpx;">
				<tn-action-sheet v-model="showContactType" :list="contactList" @click="contact"></tn-action-sheet>
				<tn-button backgroundColor="#00B9FE " padding="39rpx 0" width="100%" :fontSize="28" :plain="true"
					@click="contactShow" fontColor="#00B9FE" open-type="share">
					<text class="tn-icon-tel tn-padding-right-xs"></text>
					<text class="">联系客户</text>
				</tn-button>
			</view>
			<view class="tn-flex-1 justify-content-item tn-margin-left-xs tn-text-center">
				<tn-button backgroundColor="#00B9FE " padding="40rpx 0" width="100%" :fontSize="28" fontColor="#FFFFFF"
					@click="work">
					<text class="tn-icon-share tn-padding-right-xs"></text>
					<text class="">{{workText}}</text>
				</tn-button>
			</view>
		</view>

		<view class="custom">
			<view class="tn-flex tn-flex-row-between tn-flex-col-center" style="padding: 30rpx 30rpx 0 30rpx;">
				<view class="justify-content-item">
					<view class="tn-text-bold">
						<text class="tn-text-xxl">电站信息</text>
					</view>
				</view>
				<view class="justify-content-item">
					<text @click="$api.navigateTo('/pages/custom/change?id='+detail.CustomId)"
						class="tn-text-md tn-color-red--dark">变更</text>
				</view>
			</view>
			<view class="tn-text-md" style="padding: 30rpx 30rpx 15rpx 30rpx;">
				<view class="tn-flex  tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-star"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">所属项目：{{detail.ProjectName}}</view>
				</view>
				<view class="tn-flex  tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-star"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">电站编号：{{detail.Code}}</view>
				</view>
				<view class="tn-flex  tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-star"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">安装块数：{{detail.InstallNum || "-"}} 块</view>
				</view>
				<view class="tn-flex  tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-star"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">安装容量：{{detail.Capacity || "-"}} kw</view>
				</view>
				<view class="tn-flex  tn-flex-col-center tn-padding-bottom-sm">
					<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
						<view class="tn-icon-star"></view>
					</view>
					<view class="tn-margin-left-sm tn-flex-1 tn-color-666">安装时间：{{detail.InstallTime}}</view>
				</view>


				<view class="tn-flex tn-flex-col-center tn-padding-bottom-sm tn-flex-wrap" v-if="tags.length>0">
					<block v-for="(tagItem, tagIndex) in tags" :key="tagIndex">
						<tn-tag :index="tagIndex" :backgroundColor="`tn-bg-${tagItem.Color}--light`"
							fontColor="tn-color-333" size="lg" margin="0 16rpx 16rpx 0rpx" shape="radius">
							{{ tagItem.Label }}
						</tn-tag>
					</block>
				</view>


			</view>
		</view>

	</view>
</template>
<script>
	export default {
		data() {
			return {
				// 详情页必备参数，id，data，detail；data为原始数据，detail为处理后数据
				id: 0,
				data: {
					Id: 0,
					Mobile: '加载中...',
					RealName: "加载中...",
					Province: "加载中...",
					Village: "加载中...",
					Distance: "加载中...",
					Classify: "加载中...",
					InstallNum: "-",
					Capacity: "-",
					Describe: "-",
					CreateTime: "加载中...",
					Advice: "-"
				},
				detail: {
					Id: 0,
					Mobile: '加载中...',
					RealName: "加载中...",
					Province: "加载中...",
					Village: "加载中...",
					Distance: "加载中...",
					Classify: "加载中...",
					InstallNum: "-",
					Capacity: "-",
					Describe: "-",
					CreateTime: "加载中...",
					Advice: "-"
				},
				// 必备数据 end

				workBtn: true,
				workText: '开始维护',
				//选择联系方式
				showContactType: false,
				contactList: [{
						text: '电话联系',
						fontSize: 30,
						color: '#333333',
						name: 'mobile'
					},
					{
						text: '导航前往',
						fontSize: 30,
						color: '#333333',
						index: 'navi'
					},
				],
				tags: []
			}
		},
		onLoad(option) {
			if (option.id) {
				this.id = option.id;
				this.getDetail();
			}
		},
		onShow() {
			this.getDetail();
		},
		methods: {
			//弹出选择联系客户的方式
			contactShow() {
				let that = this;
				that.showContactType = true;
			},
			contact(index) {
				let that = this;
				if (index === 0) {
					//电话联系
					that.$api.call(that.detail.Mobile);
				} else if (index === 1) {
					//导航前往
					if (that.detail.Lat > 0) {
						uni.openLocation({
							longitude: that.detail.lng,
							latitude: that.detail.lat,
							name: that.detail.name,
							address: that.detail.address,
							success() {
								console.log('地图打开成功');
							},
							fail(err) {
								that.$api.toast('地图打开失败');
							}
						})
					} else {
						that.$api.toast('系统无该客户定位，无法导航');
					}
				}
			},
			work() {
				let that = this;
				if (that.data.Status == 2) {
					//已分配，可以开始
					that.$api.navigateTo('/pages/maintain/start?id=' + that.id);
				} else if (that.data.Status == 3) {
					//已开始，可以完成
					//是否必须完善电站信息
					if (that.data.Improve == 1) {
						that.$api.toast("该电站需要完善信息资料后才可以完成维护");
					} else {
						that.$api.navigateTo('/pages/maintain/end?id=' + that.id);
					}
				} else if (that.data.Status == 4) {
					//更换完成
					that.$api.navigateTo('/pages/maintain/end?id=' + that.id);
				}
			},
			//获取详情
			getDetail() {
				let that = this;
				let data = {};
				data.Id = that.id;
				
				// 请求接口
				that.$api.request('Maintain/MaintainDetail', data, function(res) {
					if (res.status == 'ok') {
						// 数据赋值给data和detail
						that.data = res.data;
						that.detail = res.detail;
					} else {
						that.$api.toast(res.info);
					}
				});
			}
		}
	}
</script>

<style scoped lang="scss">
	.custom {
		width: 90%;
		margin: 40rpx auto 20rpx auto;
		box-shadow: 5rpx 5rpx 6rpx 5rpx rgba(0, 0, 0, 0.1);
		border-radius: 10rpx;
	}
</style>