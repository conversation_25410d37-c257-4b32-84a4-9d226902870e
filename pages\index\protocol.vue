<template>
	<view class="container">
		<view class="box">
			<view class="content">
				{{ company }}（以下简称{{ abbrName }}）在此提醒您，您在注册成为用户之前，请认真阅读本《用户协议》（以下简称“协议”），以确保您充分理解本协议中各条款。您的注册、登录、使用等行为将视为对本协议的接受，并同意接受本协议各项条款的约束。
			</view>
			<view class="content">
				本协议约定{{ abbrName }}与用户之间关于“{{ appName }}”软件服务的权利义务。“用户”是指注册、登录、使用本服务的个人。本协议可由{{ abbrName }}随时更新，更新后的协议条款一旦公布即代替原来的协议条款，恕不再另行通知，用户可在{{ appName }}中查阅最新版协议条款。在{{ abbrName }}修改协议条款后，如果用户不接受修改后的条款，请立即停止使用{{ abbrName }}提供的服务，用户继续使用{{ abbrName }}提供的服务将被视为接受修改后的协议。
			</view>
			
			<view class="title">
				一、账号注册
			</view>
			<view class="content">
				1.1.
				用户在使用本服务前需要注册一个“{{ appName }}”帐号。“{{ appName }}”帐号需要使用手机号码绑定注册，请用户使用尚未与“{{ appName }}”帐号绑定的手机号码，以及未被{{ abbrName }}根据本协议封禁的手机号码注册“{{ appName }}”帐号。{{ abbrName }}可以根据用户需求或产品需要对帐号注册和绑定的方式进行变更，而无须事先通知用户。
			</view>
			<view class="content">
				1.2. 鉴于“{{ appName }}”帐号的绑定注册方式，您同意{{ abbrName }}在注册时将使用您提供的手机号码及自动提取您的手机设备识别码等信息用于注册。
			</view>
			<view class="title">
				二、服务内容
			</view>
			<view class="content">
				2.1.
				本服务的具体内容由{{ abbrName }}根据实际情况提供，包括但不限于授权用户通过其帐号进行邀请注册、试用申请、推广拉新、购买商品。{{ abbrName }}可以对其提供的服务予以变更，且{{ abbrName }}提供的服务内容可能随时变更；用户将会收到{{ abbrName }}关于服务变更的通知。
			</view>
			<view class="content">
				2.2. {{ abbrName }}提供的服务均为免费服务，若后期增加收费服务功能将另行通知。
			</view>
			
			
			
			<view class="title">
				三、用户个人隐私信息保护
			</view>
			<view class="content">
				3.1.
				用户在注册帐号或使用本服务的过程中，需要填写或提交一些必要的信息，如法律法规、规章规范性文件（以下称“法律法规”）规定的需要填写的身份信息。如用户提交的信息不完整或不符合法律法规的规定，则用户可能无法使用本服务或在使用本服务的过程中受到限制。
			</view>
			<view class="content">
				3.2.
				个人隐私信息是指涉及用户个人身份或个人隐私的信息，比如，用户真实姓名、身份证号、手机号码、手机设备识别码、IP地址。非个人隐私信息是指用户对本服务的操作状态以及使用习惯等明确且客观反映在{{ abbrName }}服务器端的基本记录信息、个人隐私信息范围外的其它普通信息，以及用户同意公开的上述隐私信息。
			</view>
			<view class="content">
				3.3.
				尊重用户个人隐私信息的私有性是{{ abbrName }}的一贯制度，{{ abbrName }}将采取技术措施和其他必要措施，确保用户个人隐私信息安全，防止在本服务中收集的用户个人隐私信息泄露、毁损或丢失。在发生前述情形或者{{ abbrName }}发现存在发生前述情形的可能时，将及时采取补救措施。
			</view>
			<view class="content">
				3.4. {{ abbrName }}未经用户同意不向任何第三方公开、透露用户个人隐私信息。但以下特定情形除外：
			</view>
			<view class="content">
				3.4.1. {{ abbrName }}根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露；
			</view>
			<view class="content">
				3.4.2. 由于用户将其用户密码告知他人或与他人共享注册帐户与密码，由此导致的任何个人信息的泄漏，或其他非因{{ abbrName }}原因导致的个人隐私信息的泄露；
			</view>
			<view class="content">
				3.4.3. 用户自行向第三方公开其个人隐私信息；
			</view>
			<view class="content">
				3.4.4. 任何由于黑客攻击、电脑病毒侵入及其他不可抗力事件导致用户个人隐私信息的泄露。
			</view>
			<view class="content">
				3.5. 用户同意{{ abbrName }}可在以下事项中使用用户的个人隐私信息：
			</view>
			<view class="content">
				3.5.1. {{ abbrName }}向用户及时发送重要通知，如软件更新、本协议条款的变更；
			</view>
			<view class="content">
				3.5.2. {{ abbrName }}内部进行数据分析和研究等，以改进迪{{ abbrName }}的产品、服务和与用户之间的沟通；
			</view>
			<view class="content">
				3.5.3. 依本协议约定，{{ abbrName }}管理、审查用户信息及进行处理措施；
			</view>
			<view class="content">
				3.5.4. 适用法律法规规定的其他事项。
			</view>
			<view class="content">
				除上述事项外，如未取得用户事先同意，{{ abbrName }}不会将用户个人隐私信息使用于任何其他用途。
			</view>
			<view class="content">
				3.6.
				{{ abbrName }}重视对未成年人个人隐私信息的保护。{{ abbrName }}将依赖用户提供的个人信息判断用户是否为未成年人。任何18岁以下的未成年人注册帐号或使用本服务应事先取得家长或其法定监护人（以下简称"监护人"）的书面同意。除根据法律法规的规定及有权机关的指示披露外，{{ abbrName }}不会使用或向任何第三方透露未成年人的个人隐私信息。除本协议约定的例外情形外，未经监护人事先同意，{{ abbrName }}不会使用或向任何第三方透露未成年人的个人隐私信息。任何18岁以下的用户不得下载和使用{{ abbrName }}所提供的软件服务。
			</view>
			<view class="content">
				3.7.
				用户确认，其地理位置信息为非个人隐私信息，用户成功注册“{{ appName }}”帐号视为确认授权{{ abbrName }}提取及使用用户的地理位置信息。如用户需要终止提供地理位置信息，可随时退出软件服务。
			</view>
			
			
			<view class="title">
				四、使用规则
			</view>
			<view class="content">
				4.1. 用户不得利用“{{ appName }}”帐号进行如下行为：
			</view>
			<view class="content">
				4.1.1. 虚构事实、隐瞒真相以误导、欺骗他人的；
			</view>
			<view class="content">
				4.1.2. 利用技术手段批量建立虚假帐号的；
			</view>
			<view class="content">
				4.1.3. 利用“{{ appName }}”帐号或本服务从事任何违法犯罪活动的；
			</view>
			<view class="content">
				4.1.4. 其他违反法律法规规定行为。
			</view>
			<view class="content">
				4.2.
				用户须对利用“{{ appName }}”帐号或本服务传送信息的真实性、合法性、无害性、准确性、有效性等全权负责，与用户所传播的信息相关的任何法律责任由用户自行承担，与{{ abbrName }}无关。如因此给{{ abbrName }}或第三方造成损害的，用户应当依法予以赔偿。
			</view>
			<view class="content">
				4.3.
				{{ abbrName }}提供的服务中可能包括广告，用户同意在使用过程中显示{{ abbrName }}和第三方供应商、合作伙伴提供的广告。除法律法规明确规定外，用户应自行对依该广告信息进行的交易负责，对用户因依该广告信息进行的交易或前述广告商提供的内容而遭受的损失或损害，{{ abbrName }}不承担任何责任。
			</view>
			
			
			<view class="title">
				五、账户管理
			</view>
			<view class="content">
				5.1.
				 “{{ appName }}”帐号的所有权归{{ abbrName }}所有，用户完成申请注册手续后，获得“{{ appName }}”帐号的使用权，该使用权仅属于初始申请注册人，禁止赠与、借用、租用、转让或售卖。
			</view>
			<view class="content">
				5.2. 用户可以更改、删除“{{ appName }}”帐户上的个人资料、注册信息等，但需注意，删除有关信息用户需承担该风险。
			</view>
			<view class="content">
				5.3.
				用户有责任妥善保管注册帐号信息及帐号密码的安全，因用户保管不善可能导致遭受盗号或密码失窃，责任由用户自行承担。用户同意在任何情况下不使用其他用户的帐号或密码。在用户怀疑他人使用其帐号或密码时，用户同意立即通知{{ abbrName }}。
			</view>
			<view class="content">
				5.4.
				用户应遵守本协议的各项条款，正确、适当地使用本服务，如因用户违反本协议中的任何条款，{{ abbrName }}在通知用户后有权依据协议中断或终止对违约用户“{{ appName }}”帐号提供服务。同时，{{ abbrName }}保留在任何时候收回“{{ appName }}”帐号、用户名的权利。
			</view>
			<view class="content">
				5.5. 如用户注册“{{ appName }}”帐号后一年不登录，通知用户后，{{ abbrName }}可以收回该帐号，以免造成资源浪费，由此造成的不利后果由用户自行承担。
			</view>
			
			<view class="title">
				六、数据储存
			</view>
			<view class="content">
				6.1. {{ abbrName }}不对用户在本服务中相关数据的删除或储存失败负责。
			</view>
			<view class="content">
				6.2. {{ abbrName }}可以根据实际情况自行决定用户在本服务中数据的最长储存期限，并在服务器上为其分配数据最大存储空间等。用户可根据自己的需要自行备份本服务中的相关数据。
			</view>
			<view class="content">
				6.3. 如用户停止使用本服务或本服务终止，{{ abbrName }}可以从服务器上永久地删除用户的数据。本服务停止、终止后，{{ abbrName }}没有义务向用户返还任何数据。
			</view>
			
			
			<view class="title">
				七、风险承担
			</view>
			<view class="content">
				7.1.
				用户理解并同意，“{{ appName }}”仅为用户提供信息分享、传送及获取的平台，用户必须为自己注册帐号下的一切行为负责，包括用户所传送的任何内容以及由此产生的任何后果。用户应对“{{ appName }}”及本服务中的内容自行加以判断，并承担因使用内容而引起的所有风险，包括因对内容的正确性、完整性或实用性的依赖而产生的风险。{{ abbrName }}无法且不会对因用户行为而导致的任何损失或损害承担责任。如果用户发现任何人违反本协议约定或以其他不当的方式使用本服务，请立即向{{ abbrName }}举报或投诉，{{ abbrName }}将依本协议约定进行处理。
			</view>
			<view class="content">
				7.2. 用户理解并同意，因业务发展需要，{{ abbrName }}保留单方面对本服务的全部或部分服务内容变更、暂停、终止或撤销的权利，用户需承担此风险。
			</view>
			
			<view class="title">
				八、知识产权声明
			</view>
			<view class="content">
				8.1.
				除本服务中涉及广告的知识产权由相应广告商享有外，{{ abbrName }}在本服务中提供的内容（包括但不限于网页、文字、图片、音频、视频、图表等）的知识产权均归{{ abbrName }}所有，但用户在使用本服务前对自己发布的内容已合法取得知识产权的除外。
			</view>
			<view class="content">
				8.2. 除另有特别声明外，{{ abbrName }}提供本服务时所依托软件的著作权、专利权及其他知识产权均归{{ abbrName }}所有。
			</view>
			<view class="content">
				8.3.
				{{ abbrName }}在本服务中所涉及的图形、文字或其组成，以及其他{{ abbrName }}标志及产品、服务名称（以下统称“{{ abbrName }}标识”），其著作权或商标权归{{ abbrName }}所有。未经{{ abbrName }}事先书面同意，用户不得将{{ abbrName }}标识以任何方式展示或使用或作其他处理，也不得向他人表明用户有权展示、使用、或其他有权处理{{ abbrName }}标识的行为。
			</view>
			<view class="content">
				8.4. 上述及其他任何{{ abbrName }}或相关广告商依法拥有的知识产权均受到法律保护，未经{{ abbrName }}或相关广告商书面许可，用户不得以任何形式进行使用或创造相关衍生作品。
			</view>
			
			<view class="title">
				九、法律责任
			</view>
			<view class="content">
				9.1.
				如果{{ abbrName }}发现或收到他人举报或投诉用户违反本协议约定的，{{ abbrName }}有权不经通知随时对相关内容，包括但不限于用户资料进行审查、删除，并视情节轻重对违规帐号处以包括但不限于警告、帐号封禁、设备封禁、功能封禁的处罚，且通知用户处理结果。
			</view>
			<view class="content">
				9.2.
				用户理解并同意，{{ abbrName }}有权依合理判断对违反有关法律法规或本协议规定的行为进行处罚，对违法违规的任何用户采取适当的法律行动，并依据法律法规保存有关信息向有关部门报告等，用户应承担由此而产生的一切法律责任。
			</view>
			<view class="content">
				9.3. 用户理解并同意，因用户违反本协议约定，导致或产生的任何第三方主张的任何索赔、要求或损失，包括合理的律师费，用户应当赔偿{{ abbrName }}与合作公司、关联公司，并使之免受损害。
			</view>
			
			<view class="title">
				十、不可抗力及其他免责事由
			</view>
			<view class="content">
				10.1.
				用户理解并确认，在使用本服务的过程中，可能会遇到不可抗力等风险因素，使本服务发生中断。不可抗力是指不能预见、不能克服并不能避免且对一方或双方造成重大影响的客观事件，包括但不限于自然灾害如洪水、地震、瘟疫流行和风暴等以及社会事件如战争、动乱、政府行为等。出现上述情况时，{{ abbrName }}将努力在第一时间与相关单位配合，及时进行修复，但是由此给用户或第三方造成的损失，{{ abbrName }}及合作单位在法律允许的范围内免责。
			</view>
			<view class="content">
				10.2.
				本服务同大多数互联网服务一样，受包括但不限于用户原因、网络服务质量、社会环境等因素的差异影响，可能受到各种安全问题的侵扰，如他人利用用户的资料，造成现实生活中的骚扰；用户下载安装的其它软件或访问的其他网站中含有病毒，威胁到用户移动设备的信息和数据的安全，继而影响本服务的正常使用等等。用户应加强信息安全及使用者资料的保护意识，要注意加强密码保护，以免遭致损失和骚扰。
			</view>
			<view class="content">
				10.3.
				用户理解并确认，本服务存在因不可抗力、病毒或黑客攻击、系统不稳定、用户所在位置、用户关机以及其他任何技术、互联网络、通信线路原因等造成的服务中断或不能满足用户要求的风险，因此导致的用户或第三方任何损失，{{ abbrName }}不承担任何责任。
			</view>
			<view class="content">
				10.4.
				用户理解并确认，在使用本服务过程中存在来自任何他人的包括误导性的、欺骗性的、威胁性的、诽谤性的、令人反感的或非法的信息，或侵犯他人权利的匿名或冒名的信息，以及伴随该等信息的行为，因此导致的用户或第三方的任何损失，{{ abbrName }}不承担任何责任。
			</view>
			<view class="content">
				10.5.
				用户理解并确认，{{ abbrName }}需要定期或不定期地对“{{ appName }}”平台或相关的设备进行检修或者维护，如因此类情况而造成服务在合理时间内的中断，{{ abbrName }}无需为此承担任何责任，但{{ abbrName }}应事先进行通告。
			</view>
			<view class="content">
				10.6.
				{{ abbrName }}依据法律法规、本协议约定获得处理违法违规或违约内容的权利，该权利不构成{{ abbrName }}的义务或承诺，{{ abbrName }}不能保证及时发现违法违规或违约行为或进行相应处理。
			</view>
			<view class="content">
				10.7. 用户理解并确认，对于{{ abbrName }}向用户提供的下列产品或者服务的质量缺陷及其引发的任何损失，{{ abbrName }}无需承担任何责任：
			</view>
			<view class="content">
				10.7.1. {{ abbrName }}向用户免费提供的服务；
			</view>
			<view class="content">
				10.7.2. {{ abbrName }}向用户赠送的任何产品或者服务。
			</view>
			<view class="content">
				10.8.
				在任何情况下，{{ abbrName }}均不对任何间接性、后果性、惩罚性、偶然性、特殊性或刑罚性的损害，包括因用户使用“{{ appName }}”而遭受的利润损失，承担责任（即使{{ abbrName }}已被告知该等损失的可能性亦然）。尽管本协议中可能含有相悖的规定，{{ abbrName }}对用户承担的全部责任，无论因何原因或何种行为方式，始终不超过用户因使用{{ abbrName }}提供的服务而支付给{{ abbrName }}的费用。
			</view>
			
			<view class="title">
				十一、服务的变更、中断、终止
			</view>
			<view class="content">
				11.1.
				鉴于网络服务的特殊性，用户同意{{ abbrName }}有权随时变更、中断或终止部分或全部的服务。{{ abbrName }}变更、中断或终止的服务，{{ abbrName }}应当在变更、中断或终止之前通知用户。
			</view>
			<view class="content">
				11.2. 如发生下列任何一种情形，{{ abbrName }}有权变更、中断或终止向用户提供的免费服务或收费服务，而无需对用户或任何第三方承担任何责任：
			</view>
			<view class="content">
				11.2.1. 根据法律规定用户应提交真实信息，而用户提供的个人资料不真实、或与注册时信息不一致又未能提供合理证明；
			</view>
			<view class="content">
				11.2.2. 用户违反相关法律法规或本协议的约定；
			</view>
			<view class="content">
				11.2.3. 按照法律规定或有权机关的要求；
			</view>
			<view class="content">
				11.2.4. 出于安全的原因或其他必要的情形。
			</view>
			
			<view class="title">
				十二、其他
			</view>
			<view class="content">
				12.1. {{ abbrName }}郑重提醒用户注意本协议中免除{{ abbrName }}责任和限制用户权利的条款，请用户仔细阅读，自主考虑风险。未成年人应在法定监护人的陪同下阅读本协议。
			</view>
			<view class="content">
				12.2.
				本协议的效力、解释及纠纷的解决，适用于中华人民共和国法律。若用户和{{ abbrName }}之间发生任何纠纷或争议，首先应友好协商解决，协商不成的，用户同意将纠纷或争议提交{{ abbrName }}住所地有管辖权的人民法院管辖。
			</view>
			<view class="content">
				12.2. 本协议的任何条款无论因何种原因无效或不具可执行性，其余条款仍有效，对双方具有约束力。
			</view>
			<view class="content tn-text-right">
				{{ company }}
			</view>
			<view class="content tn-text-right">
				2024年05月01日
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				company: '--',
				abbrName: '--',
				appName: '--',
			}
		},
		onLoad() {
			this.company = this.$api.config.company;
			this.abbrName = this.$api.config.companyAbbr;
			this.appName = this.$api.config.appName;
		}
	}
</script>

<style>
	.box{
		width: 95%;
		margin-left: auto;
		margin-right: auto;
		padding-bottom: 30rpx;
	}
	.content {
		margin-top: 20rpx;
		text-indent: 2rem;
		line-height: 45rpx;
	}

	.title {
		margin-top: 30rpx;
		font-weight: bold;
	}
</style>