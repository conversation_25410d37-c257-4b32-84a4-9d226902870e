
<template>
	<view class="app">
		<view class="tops">
			<view class="top_info" @click="jumpLogin" v-if="!userInfo">
				点击登录
			</view>
			<view class="userXx" v-else>
				<image style="width: 100%;" class="imgBg" src="/static/backs.png" mode="widthFix"></image>
				<view class="flexsClass">
					<view class="imgs">
						<image style="width: 100%;height: 100%;" :src="userInfo.avatar" alt="" srcset="" />
					</view>
					<view class="rights">
						<view class="names">
							{{ userInfo.name ? userInfo.name : '-- '}}
						</view>
						<view class="bumen">
							部门：{{ userInfo.remark ? userInfo.remark : '--' }}
						</view>
						<view class="phones">
							{{ userInfo.phone }}
						</view>
					</view>
				</view>

			</view>
			<view class="conBtns">
				<view class="gneng">
					<image class="gong_img" src="/static/guan.png" mode=""></image>
					<view class="text">
						关于我们
					</view>
				</view>
				<view class="gneng" @click="jumpNav">
					<image class="gong_img" src="/static/guanlis.png" mode=""></image>
					<view class="text">
						CRM管理
					</view>
				</view>
				<view class="gneng">
					<image class="gong_img" src="/static/setup.png" mode=""></image>
					<view class="text">
						设置
					</view>
				</view>
			</view>
			<view class="btns"  v-if="userInfo" @click="clearInput">
				退出登录
			</view>
		</view>
		<view class="bd_phone" v-if="bdP">
			<view class="boxs">
				<view class="titles">
					绑定手机号
				</view>
				<view class="phones">
					<input class="inputs" placeholder="请输入您的手机号" name="input" placeholder-style="color:#AAAAAA" v-model="phone"></input>
				</view>
				<view class="yzms">
					<input class="inputs" placeholder="请输入验证码" name="input" placeholder-style="color:#AAAAAA" v-model="yzm"></input>
					<view class="scode" @click="code">
						{{ text }}
					</view>
					<!-- <button class="scode" :disabled="disabled" :class="disabled === true ? 'on' : ''" @click="code">
						{{ text }}
					</button> -->
				</view>
				<view class="sumbit" @click="queryPhone">
					确定绑定
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return {
				phone: '',
				userInfo: {},
				bdP: false,
				yzm: '',
				text: '发送验证码',
				disabled: false,
				
			}
		},
		mounted() {
			this.userInfo = uni.getStorageSync('userInfo') ? uni.getStorageSync('userInfo') : ''
			console.log('我是跳转页面1', this.userInfo)	
			if(this.userInfo){
				console.log('我是跳转页面1')	
				this.bdP = this.userInfo.is_phone_bind == 1 ? false : true
			} else{
				console.log('我是跳转页面2')	
				this.bdP = false
				this.jumpLogin()
			}
			console.log(this.userInfo)
		},
		// 小程序点击分享
		onShareAppMessage() {
			return {
				title: '博迪智家',
				path: '/pages/main/main'
			}
		},
		onShow() {
			this.isInit = true
			// #ifdef MP-WEIXIN
			const curPages = getCurrentPages()[0];
			if (typeof curPages.getTabBar === 'function' && curPages.getTabBar()) {
				curPages.getTabBar().setData({
					selected: 3 // custom-tab-bar中index.js文件list的数组对应数据下标
				})
			}
			// #endif
		},
		onUnload() {
			uni.$off('login', this.userLogin)
		},
		methods: {
			clearInput(){
				// 存储token
				uni.setStorageSync('token', '')
				// 存储openid 
				uni.setStorageSync('openId', '')
				// 存储用户信息
				uni.setStorageSync('userInfo', '')
				
				this.userInfo = null
				
				uni.showToast({ icon: 'none', title: '退出登录' })
			},
			jumpLogin(){
				uni.navigateTo({
					url: '/pages/index/login'
				})
			},
			jumpNav(){
				uni.navigateTo({
					url: '/crm/customer/index',
				})
			},
			queryPhone(){
				let that = this
				if (!that.yzm) return uni.showToast({ icon: 'none', title: '请输入验证码' })
				let obj = {
					openid: uni.getStorageSync('openId'),
					phone: this.phone,
					sms_code: this.yzm
				}
				this.$api.request('wxuser/setUserPhone', obj, (res)=>{
					if(res.status != 'ok'){
						uni.showToast({ icon: 'none', title: res.info })
						this.userInfo.is_phone_bind = 1
						uni.setStorageSync('userInfo', this.userInfo)
					} else{
						this.bdP = false
					}
				})
			},
			code(){
				let that = this;
				if (!that.phone) return uni.showToast({ icon: 'none', title: '请输入手机号码' })
				if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone)) return uni.showToast({ icon: 'none', title: '请输入正确的手机号码' })
				let obj = {
					phone: that.phone,
					type: 2
				}
				this.$api.request('common/sms', obj, (res)=>{
					uni.showToast({ icon: 'none', title: '验证码已发送，请注意短信消息' })
					this.sendCode()
				})
			},
			sendCode() {
			  if (this.disabled) {
				  uni.showToast({
				  	icon: 'none',
					title: '请等待倒计时结束再点击'
				  })
				return
			  }
			  this.disabled = true;
			  let n = 60;
			  this.text = "剩余 " + n + "s";
			  const run = setInterval(() => {
			    n = n - 1;
			    if (n < 0) {
			      clearInterval(run);
			    }
			    this.text = "剩余 " + n + "s";
			    if (this.text < "剩余 " + 0 + "s") {
			      this.disabled = false;
			      this.text = "重新获取";
			    }
			  }, 1000);
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tops{
		width: 100%;
		height: 90rpx;
		.top_info{
			width: 78%;
			height: 70rpx;
			position: relative;
			margin: 10rpx auto;
			padding: 0 20rpx;
			background: plum;
			border-radius: 40rpx;
			line-height: 70rpx;
			color: #fff;
		}
		.userXx{
			width: 100%;
			height: 300rpx;
			position: relative;
			overflow: hidden;
			
			.imgBg{
				position: absolute;
				top: 0;
				left: 0;
				z-index: 10;
				width: 100%;
				height: 100%;
			}
			.flexsClass{
				position: relative;
				z-index: 20;
				display: flex;
				align-items: center;
				height: 200rpx;
				.imgs{
					width: 120rpx;
					height: 120rpx;
					border-radius: 120rpx;
					overflow: hidden;
					margin-left: 32rpx;
					background: #ccc;
				}
				.rights{
					width: calc(100% - 64rpx - 20rpx);
					margin-left: 170rpx;
					.names{
						font-size: 30rpx;
					}
					.phones{
						font-size: 24rpx;
						margin-top: 10rpx;
					}
					.bumen{
						font-size: 24rpx;
						margin-top: 15rpx;
					}
				}
			}
			
		}
		.conBtns{
			width: calc(100% - 64rpx);
			// height: 300rpx;
			background: #fff;
			border-radius: 20rpx;
			margin: 0 auto;
			position: relative;
			z-index: 25;
			top: -80rpx;
			.gneng{
				height: 80rpx;
				display: flex;
				width: calc(100% - 56rpx);
				margin: 0 auto;
				align-items: center;
				gap: 0 15rpx;
				border-bottom: 1rpx solid #ccc;
				.gong_img{
					width: 35rpx;
					height: 35rpx;
				}
				.text{
					position: relative;
					top: -3rpx;
					font-size: 28rpx;
				}
			}
		}
		
		.btns{
			width: 90%;
			position: fixed;
			bottom: 200rpx;
			left: 5%;
			background: #e72f8c;
			text-align: center;
			font-size: 28rpx;
			color: #fff;
			border-radius: 35rpx;
			line-height: 70rpx;
		}
	}
	.bd_phone{
		width: 100%;
		height: 100vh;
		position: fixed;
		z-index: 999;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.3);
		.boxs{
			width: 80%;
			height: 420rpx;
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			margin: 300rpx auto 0;
			.titles{
				font-weight: 550;
				text-align: center;
				margin-top: 30rpx;
			}
			.phones{
				width: 90%;
				margin: 50rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #000;
				.inputs{
					width: 90%;
					margin-left: 5%;
					height: 70rpx;
					
				}
			}
			.yzms{
				width: 90%;
				margin: 20rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #000;
				display: flex;
				align-items: center;
				.inputs{
					width: 60%;
					margin-left: 5%;
					height: 70rpx;
					
				}
				.scode{
					width: 35%;
					color: #000;
					text-align: center;
					line-height: 68rpx;
					font-size: 26rpx;
					height: 68rpx;
					border-left: 1rpx solid #000;
					box-sizing: border-box;
				}
			}
			.sumbit{
				width: 90%;
				margin: 40rpx auto;
				height: 70rpx;
				border-radius: 35rpx;
				color: #fff;
				background: skyblue;
				text-align: center;
				line-height: 70rpx;
			}
		}
	}
	
</style>