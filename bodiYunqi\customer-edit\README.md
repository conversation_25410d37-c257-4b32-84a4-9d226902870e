# 客户资料编辑页面

## 页面功能
- 编辑客户基本信息
- 表单验证
- 提交修改数据

## 可编辑字段
1. **客户姓名** (必填)
   - 文本输入框
   - 最大长度：20字符
   - 必填验证

2. **客户地址** (必填)
   - 多行文本输入框
   - 最大长度：200字符
   - 自动调整高度
   - 必填验证

3. **联系电话** (必填)
   - 数字输入框
   - 最大长度：11位
   - 手机号格式验证
   - 必填验证

## 页面参数
支持通过URL参数传递数据：

### 方式一：传递客户ID
```javascript
uni.navigateTo({
  url: '/bodiYunqi/customer-edit/index?id=123'
})
```
页面会根据ID调用API获取客户详情数据

### 方式二：直接传递数据
```javascript
const customerName = encodeURIComponent('张三')
const customerAddress = encodeURIComponent('北京市朝阳区xxx街道')
const customerPhone = '13800138000'

uni.navigateTo({
  url: `/bodiYunqi/customer-edit/index?customerName=${customerName}&customerAddress=${customerAddress}&customerPhone=${customerPhone}`
})
```

## 表单验证规则
- **客户姓名**: 不能为空
- **客户地址**: 不能为空
- **联系电话**: 不能为空，且必须是有效的手机号格式（1开头的11位数字）

## API接口（待实现）
### 获取客户详情
- **接口**: `Customer/detail`
- **参数**: `{ id: customerId }`
- **用途**: 根据客户ID获取现有数据

### 更新客户资料
- **接口**: `Customer/update`
- **参数**: 
  ```javascript
  {
    id: customerId,
    customerName: '客户姓名',
    customerAddress: '客户地址', 
    customerPhone: '联系电话'
  }
  ```

## 页面特点
- **响应式设计**: 适配不同屏幕尺寸
- **表单验证**: 实时验证用户输入
- **加载状态**: 提交时显示加载状态
- **用户体验**: 成功后自动返回上一页

## 样式特点
- 卡片式表单布局
- 清晰的标签和必填标识
- 聚焦时的边框高亮效果
- 统一的按钮样式

## 使用示例

### 从列表页跳转编辑
```javascript
// 在列表页面中
handleEdit(customer) {
  uni.navigateTo({
    url: `/bodiYunqi/customer-edit/index?id=${customer.id}`
  })
}
```

### 新增客户（预填部分信息）
```javascript
uni.navigateTo({
  url: `/bodiYunqi/customer-edit/index?customerPhone=13800138000`
})
```

## 待完善功能
1. 实际的API接口调用
2. 更多字段的支持（如客户类型、备注等）
3. 图片上传功能（客户头像）
4. 地址选择器集成
