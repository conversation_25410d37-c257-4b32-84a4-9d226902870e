<template>
	<!-- 核销 -->
	<view class="app">
		<view class="title">
			核销码
		</view>
		<view class="ewms">
			<image :src="dataObj.img" mode="widthFix" style="width: 100%;"> </image>
		</view>
		<view class="tisp">
			<view> 1.本活动数量有限先到先得 </view>
			<view> 2.本活动最终解释权归公司所有 </view>
			<view> 3.核销码只在当天生效，请尽快核销 </view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return {
				dataObj: {}
			}
		},
		onLoad() {
			this.dataObj = uni.getStorageSync('hex')
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
	.app{
		width: 100%;
		.title{
			font-size: 32rpx;
			font-weight: 550;
			padding: 50rpx 0;
			text-align: center;
		}
		.ewms{
			width: 350rpx;
			height: 350rpx;
			margin: 20rpx auto;
		}
		.tisp{
			width: calc(100% - 100rpx);
			margin: 20rpx auto 0;
			position: relative;
			top: 50rpx;
		}
	}
</style>