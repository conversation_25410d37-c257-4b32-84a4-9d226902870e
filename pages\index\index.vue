<template>
	<view class="index">

		<view class="tn-page">
			<home v-if="tabbarList[pagesIndex].title == '首页'"></home>
			<flow v-if="tabbarList[pagesIndex].title == '流程'" :nameKey="nameKey" :showTrue="showTrue" ref="process">
			</flow>
			<!-- <flow v-if="tabbarList[pagesIndex].title == '工作台'"></flow> -->
			<crm v-if="tabbarList[pagesIndex].title == 'CRM'" :nameKey="nameKey" :showTrue="showTrue"></crm>
			<work v-if="tabbarList[pagesIndex].title == '工作台'"></work>
			<my v-if="tabbarList[pagesIndex].title == '我的'"></my>
			<!-- <flow v-if="pagesIndex==4"></flow> -->
			<!-- <finance v-if="pagesIndex>5"></finance> -->
		</view>
		<tn-tabbar v-if="tabbarList.length>0" v-model="tabbarIndex" :list="tabbarList" @change="switchTabs" :key="tabbarIndex"></tn-tabbar>
		<view class="yes_no" v-if="yesterday == 2" :key="yesterday">
			<view class="tops1">
				<view class="titles">
					昨天数据为空，请选择昨天是否下村宣传？
				</view>
				<view class="infoSize">
					注:下村则需要补充昨日数据;未下村则昨日数据为空，无法修改!
				</view>
				<view class="btns">
					<view class="ann colorBtn1" @click="noTwon">
						不下村
					</view>
					<view class="ann colorBtn2" @click="yesTwon">
						下村
					</view>
				</view>
				<view style="height: 50rpx;">
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Home from '../tabs/index.vue'
	import Flow from '../process/process.vue'
	import Crm from '../tabs/workbenches.vue'
	import Work from '../tabs/work.vue'
	import My from '../tabs/my.vue'

	export default {
		components: {
			Home,
			Flow,
			Crm,
			My,
			Work
		},
		data() {
			return {
				nameKey: 0,
				yesterday: 1,
				showTrue: 0,
				// 底部tabbar菜单数据
				tabbarList: [],
				// tabbar当前被选中的序号
				tabbarIndex: 0,
				pagesIndex: 0,
				baseIndex: 0,
				tabsName: ''
			}
		},
		onShareAppMessage(res) {
			console.log(res.target.dataset.id, '123')
			console.log(res.target.dataset, '123')
			console.log(res.target.dataset.list.join(','))
			const promise = new Promise((resolve, reject) => {
				// 调用封装后的接口方法
				this.$api.request('work/flowshare', { flow_custom_id: res.target.dataset.id, work_id: res.target.dataset.list.join(',') }, (res) => { // 回调函数
					// 成功回调处理
					if (res.status === 'ok') {	
						resolve({
							path: `linkWeb/index?id=${res.id}&key=${res.key}` || 'pages/index/index',
							title: res.title || '博笛智家',
							imageUrl: res.cover || 'https://bodhi.kaikungroup.com/images/share_cover.png',	
							scene: 0 // 固定参数	
						})
					}
				})
			})
			return {
				path: 'pages/index/index',
				title: '标题',
				imageUrl: '',
				scene: 0,
				promise
			}
		},
		onShow() {
			console.log('我是首页隐藏出发')
			this.showTrue += 1
			this.getSubmit()
		},
		onHide() {
			this.showTrue += 1
			this.getSubmit()
		},
		onReachBottom() {
			console.log('触发滚动到底部事件')
			this.nameKey += 1
		},
		onLoad(e) {
			if (uni.getStorageSync('token')) {
				this.getMenuList()
			} else {
				this.tabbarList = [{
						title: '首页',
						activeIcon: 'count-fill',
						inactiveIcon: 'menu'
					},
					{
						title: '我的',
						activeIcon: 'vip-fill',
						inactiveIcon: 'vip'
					}
				]
			}
			if (e.index == 3) {
				this.tabbarList.map((item, index) => {
					if (item.title == '我的') {
						this.tabbarIndex = index
						this.pagesIndex = index
						this.baseIndex == index
					}
				})
			}
		},
		methods: {
			httpAxios(){
				uni.request({
					url: 'http://**************/v1/chat-messages',
					// app-WRTuhMaT6uZKhFHtVqHGGz2p
				})
			},
			
			
			noTwon(){
				uni.showModal({
					title: '请输入备注',
					editable: true,
					placeholderText: '请输入不下村的原因或备注',
					success: (res) => {
						if (res.confirm) {
							const remark = res.content || '';
							this.$api.request('village/submit', { 
								type: 2, 
								class: 2,
								remark: remark
							}, (res)=>{
								if(res.status == 'ok'){
									uni.showToast({
										icon: 'none',
										title: res.info
									})
									this.yesterday = 1
								} else{
									uni.showToast({
										icon: 'none',
										title: res.info
									})
								}
							})
						}
					}
				})
			},
			yesTwon(){
				uni.navigateTo({
					url: '/signIn/detail/index?id=0&status=add&class=2'
				})
			},
			// village/yesterdaySubmit
			getSubmit(){
				this.$api.request('village/yesterdaySubmit', {}, (res) => {
					this.yesterday = res.data.yesterday
				})
			},
			// common/getMenuList
			getMenuList() {
				let that = this
				this.$api.request('common/getMenuList', {}, (res) => {
					console.log(res.list)
					let arr = res.list
					for (let i = 0; i < arr.length; i++) {
						arr[i].activeIcon = arr[i].icon_selected,
							arr[i].inactiveIcon = arr[i].icon_no_selected
						arr[i].title = arr[i].name
					}
					that.tabbarList = arr
					if (this.tabsName == '我的') {
						that.tabbarList.map((item, index) => {
							if (item.title == '我的') {
								this.tabbarIndex = index
								this.pagesIndex = index
								this.baseIndex == index
							}
						})
					}
					console.log('res', that.tabbarList)
				})
			},
			switchTabs(e) {
				console.log(e, '点击e');
				console.log(this.baseIndex, 'this.baseIndex');
				this.pagesIndex = this.baseIndex + e;
				this.tabsName = this.tabbarList[this.pagesIndex].title
				console.log(this.pagesIndex, 'this.pagesIndex');
			}
		},
		onPullDownRefresh() {
			uni.stopPullDownRefresh();
		}
	}
</script>

<style lang="scss" scoped>
	.yes_no{
		position: fixed;
		top: 0;
		width: 100vw;
		left: 0;
		z-index: 999;
		height: 100vh;
		background: rgba(0, 0, 0, 0.3);
		.tops1{
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			margin: auto;
			width: 650rpx;
			height: 240rpx;
			padding: 30rpx 32rpx;
			background: #fff;
			border-radius: 15rpx;
			.titles{
				font-size: 32rpx;
				text-align: center;
			}
			.infoSize{
				color: red;
				margin-top: 10rpx;
				font-size: 20rpx;
				text-align: center;
			}
			.btns{
				display: flex;
				text-align: center;
				gap: 40rpx;
				justify-content: center;
				.ann{
					width: 200rpx;
					height: 60rpx;
					line-height: 60rpx;
					text-align: center;
					color: #fff;
					margin-top: 40rpx;
					border-radius: 60rpx;
				}
				.colorBtn2{
					background: #01BEFF;
				}
				.colorBtn1{
					background: #82B2FF;
				}
			}
		}
	}
</style>