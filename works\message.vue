<template>
	<!-- 消息 -->
	<view class="box">
		<!-- #ifdef H5 -->
		<view style="height: 44px;width: 100%;"></view>
		<!-- #endif -->
		<view class="tabs">
			<view class="tab-item" :class="{ active: activeTab === 1 }" @click="changeTab(1)">
				<text>未读</text>
				<view class="badge">{{count1}}</view>
				<view v-if="activeTab === 1" class="active-line"></view>
			</view>
			<view class="tab-item" :class="{ active: activeTab === 2 }" @click="changeTab(2)">
				<text>已读</text>
				<view class="badge">{{ count2 }}</view>
				<view v-if="activeTab === 2" class="active-line"></view>
			</view>
		</view>
		<view class="list" v-if="allData.length">
			<view class="list_item" v-for="(item, index) in allData" :key="index">
				<view class="item_top">
					<view class="item_top_title">
						{{ item.flow_custom_name }}
					</view>
					<view @click="headZk(item)">
						<text class="tn-icon-right" v-if="!item.currentStatus"></text>
						<text class="tn-icon-down" v-else></text>
					</view>
				</view>
				<view class="item_mian">
					<view class="timer" v-if="item.create_time">
						{{item.create_time}}
					</view>
					<view class="types" v-if="item.type">
						类型：{{item.type}}
					</view>
				</view>
				<view class="item_content">
					{{ item.title }}
				</view>
				<view class="item_xiangxi" v-show="item.currentStatus"> 
					<u-divider text="详细消息" textColor="#2979ff" lineColor="#2979ff" textSize="14"></u-divider>
					{{ item.contents }}
				</view>
			</view>
		</view>
		<view class="miann_app" v-if="!loging && !allData.length">
			<tn-empty mode="data"></tn-empty>
		</view>
		<view class="loading-status">
			<text v-if="isLoading">正在加载...</text>
			<text v-if="noMoreData">没有更多数据了</text>
		</view>
		<view class="" style="position: fixed;top: 40%;z-index: 999;left: 40%;">
			<u-loading-icon :show="loging" size="60" color="#9EBEFF" mode="semicircle"></u-loading-icon>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return {
				pageNo: 1,
				pageSize:10,
				list: [{disabled: false, content: '123455'}],
				activeTab: 1,
				allData: [],
				count1: 0,
				count2: 0,
				isLoading: false,
				noMoreData: false,
				loging: false
			}
		},
		onLoad() {
			this.getList()
			this.getCount()
		},
		onShow() {
			
		},
		onReachBottom() {
			console.log('触发滚动到底部事件')
			if(this.activeTab == 1){
				if(this.count1 > this.pageSize){
					this.pageSize += 10
					this.isLoading = true
					this.getList();
				} else{
					this.noMoreData = false
				}
			}else{
				if(this.count2 > this.pageSize){
					this.pageSize += 10
					this.isLoading = true
					this.getList();
				} else{
					this.noMoreData = false
				}
			}
			
		},
		methods: {
			// 获取角标
			getCount(){
				this.$api.request('notice/listing', {pageNo: 1, pageSize: 1, read: 1}, (res)=>{
					if(res.status== 'ok'){
						this.count1 = res.count
					}
				})
				this.$api.request('notice/listing', {pageNo: 1, pageSize: 1, read: 2}, (res)=>{
					if(res.status== 'ok'){
						this.count2 = res.count
					}
				})
			},
			headZk(item){
				if(item.contents) return item.currentStatus = !item.currentStatus
				this.$api.request('notice/detail', { id: item.id }, (res)=>{
					if(res.status == 'ok'){
						item.contents = res.detail.content
						item.currentStatus = !item.currentStatus
					}else{
						uni.showToast({title: res.info, icon: 'none'})
					}
				})
			},
			changeTab(index) {
				if (this.activeTab === index) return;
				this.activeTab = index;
				this.pageSize = 10; // 重置页码
				this.allData = []; // 清空旧数据
				this.getList()
				this.getCount()
			},
			getList(){
				this.loging = true
				this.$api.request('notice/listing', {pageNo: this.pageNo, pageSize: this.pageSize, read: this.activeTab}, (res)=>{
					this.loging = false
					if(res.status== 'ok'){
						console.log(res)
						res.list.map(item=>item.currentStatus = false)		
						res.list.map(item=>item.contents = '')
						this.allData = res.list
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.miann_app{
		width: 100%;
		height: 100vh;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.loading-status {
		text-align: center;
		padding: 60rpx;
		padding-bottom: 100rpx;
		color: #999;
		font-size: 28rpx;
	}
	.box{
		display: flex;
		flex-direction: column;
		min-height: 100vh;
		background-color: #f5f5f5;
		.tabs {
			display: flex;
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			justify-content: space-around;
			background-color: #ffffff;
			padding-bottom: 10rpx;
			.tab-item {
				position: relative;
				padding: 20rpx 0;
				flex: 1;
				text-align: center;
				font-size: 30rpx;
				color: #333;
				display: flex;
				justify-content: center;
				align-items: center;
				.active-line {
					position: absolute;
					bottom: 0;
					width: 60rpx;
					height: 6rpx;
					background-color: #00AAFF;
					border-radius: 3rpx;
				}
				
				.badge {
					background-color: #FF4D4F;
					color: white;
					font-size: 24rpx;
					height: 36rpx;
					min-width: 36rpx;
					padding: 0 6rpx;
					border-radius: 18rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-left: 6rpx;
				}
			}
		}
		.active {
			color: #00AAFF;
			font-weight: bold;
		}
		.list{
			width: 100%;
			position: relative;
			top: 45px;
			// margin-top: 10rpx;
			.list_item{
				width: calc(100% - 64rpx);
				margin: 20rpx auto;
				padding: 20rpx;
				background: #fff;
				border-radius: 10rpx;
				.item_top{
					display: flex;
					align-items: center;
					justify-content: space-between;
					.item_top_title{
						font-size: 30rpx;
						font-weight: 550;
					}
				}
				.item_mian{
					display: flex;
					align-items: center;
					margin-top: 10rpx;
					font-size: 26rpx;
					gap: 20rpx;
				}
				.item_content{
					margin-top: 10rpx;
					font-size: 26rpx;
				}
				.item_xiangxi{
					font-size: 28rpx;
					color: #00AAFF;
				}
			}
		}
		
		
		
	}
</style>