<template>
	<view class="container">
		<canvas canvas-id="myCanvas" style="width: 308px; height: 542px;" />
		<button @tap="saveImage">保存喜报</button>
	</view>
</template>

<script>
	export default {
		onReady() {
			this.createPoster();
		},
		methods: {
			async createPoster() {
				const ctx = uni.createCanvasContext('myCanvas', this);

				function downloadFile(url) {
					return new Promise((resolve, reject) => {
						uni.downloadFile({
							url: url,
							success: (res) => {
								if (res.statusCode === 200) {
									resolve(res.tempFilePath)
								} else {
									reject(new Error('下载失败'))
								}
							},
							fail: (err) => {
								reject(err)
							}
						})
					})
				}

				const res1 = await downloadFile('https://bodhi.kaikungroup.com/mini/xb.png')
				const res2 = await downloadFile('https://bodhi.kaikungroup.com/mini/xb.png')

				// 绘制背景
				ctx.drawImage(res1, 0, 0, 308, 542);

				// 设置通用样式
				ctx.setFillStyle('#FFD700'); // 金色
				ctx.setTextAlign('center');

				// 顶部居中文字
				ctx.setFontSize(30);
				ctx.fillText('大兴区  阿夏', 308 / 2, 65);
				
				// 底部两行文字
				ctx.setFontSize(24);
				const bottomBase = 542 - 87;
				ctx.fillText('定金200000元', 308 / 2, bottomBase - 35);

				ctx.setFontSize(18);
				ctx.fillText('砖混245平米，1280元/平米', 308 / 2, bottomBase);

				// 右下角编码
				ctx.setTextAlign('right');
				ctx.setFillStyle('#FFFFFF');
				ctx.setFontSize(10);
				ctx.fillText('编码：300000', 308 - 5, 542 - 5);

				ctx.draw(false, () => {
					console.log('海报绘制完成');
				});
			},

			async saveImage() {
				const [tempFile] = await Promise.all([
					uni.canvasToTempFilePath({
						canvasId: 'myCanvas',
						fileType: 'png'
					})
				]);

				await uni.saveImageToPhotosAlbum({
					filePath: tempFile.tempFilePath
				});

				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
			}
		}
	}
</script>

<style>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	button {
		margin-top: 20px;
		width: 200px;
	}
</style>
<!-- <template>
  <view class="container">
    <canvas canvas-id="myCanvas" :style="canvasStyle"/>
    <button @tap="saveImage">保存海报</button>
  </view>
</template>

<script>
export default {
  data() {
    return {
      canvasStyle: {
        width: '750px',
        height: '1334px'
      }
    }
  },
  onReady() {
    this.createPoster();
  },
  methods: {
    async createPoster() {
      try {
        const ctx = uni.createCanvasContext('myCanvas', this);
        
        // 下载背景图（添加超时和重试机制）
        const bgRes = await uni.downloadFile({
          url: 'https://bodhi.kaikungroup.com/mini/xb.png',
          timeout: 10000
        });
        
        // 增加资源有效性检查
        if (!bgRes.tempFilePath) {
          throw new Error('背景图下载失败');
        }
        
        // 预加载图片
        await new Promise((resolve, reject) => {
          ctx.drawImage(bgRes.tempFilePath, 0, 0, 750, 1334);
          ctx.draw(true, resolve);
          setTimeout(() => reject(new Error('图片加载超时')), 5000);
        });

        // 绘制文字内容
        this.drawTextElements(ctx);
        
      } catch (error) {
        console.error('海报生成失败:', error);
        uni.showToast({
          title: '生成失败，请重试',
          icon: 'none'
        });
      }
    },

    drawTextElements(ctx) {
      // 设置通用样式
      ctx.setFillStyle('#FFD700');
      ctx.setTextAlign('center');

      // 顶部文字
      ctx.setFontSize(36);
      ctx.fillText('大兴区   阿夏', 375, 200);

      // 底部文字
      ctx.setFontSize(32);
      const bottomY = 1334 - 100;
      ctx.fillText('定金200000元', 375, bottomY - 40);
      ctx.fillText('砖混245平米，1280元/平米', 375, bottomY);

      // 右下角编码
      ctx.setTextAlign('right');
      ctx.setFillStyle('#FFFFFF');
      ctx.setFontSize(24);
      ctx.fillText('300000', 730, 1314);

      ctx.draw();
    },

    async saveImage() {
      try {
        const res = await uni.canvasToTempFilePath({
          canvasId: 'myCanvas',
          fileType: 'png',
          quality: 1
        });
        
        await uni.saveImageToPhotosAlbum({
          filePath: res.tempFilePath
        });
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: '保存失败，请检查权限',
          icon: 'none'
        });
      }
    }
  }
}
</script> -->