<template>
	<view class="container">
		<view class="customer-information">
			<view class="main-info">{{customerInformation.clue_name ? customerInformation.clue_name : '--' }}</view>
			<view class="secondary-info">结构：{{customerInformation.structure || '未设置'}}</view>
			<view class="secondary-info">已支付：{{customerInformation.paid ? customerInformation.paid : '--'}}</view>
			<view class="secondary-info">未支付：{{customerInformation.surplus ? customerInformation.surplus : '--'}}</view>
			<view class="secondary-info">总金额：{{customerInformation.total ? customerInformation.total : '--'}}</view>
			<view class="secondary-info">开工日期：{{customerInformation.start_open_date || '--'}}</view>
		</view>
		<view class="customer-information newClass" v-if="circulatelog.length">
			<view class="titles_new">回访内容</view>
			<view v-for="(cItem, cIndex) in circulatelog">
				<view v-if="['text', 'radio'].indexOf(cItem.form_type) !== -1" class="textStyle">
					<view class="label view1">{{ cIndex + 1 }}. {{ cItem.form_name }}：</view>
					<view class="text selected view2" :style="{ color: cItem.val == '满意' ? '#31E749' : '#CC5A36' }" >{{ cItem.val }}</view>
				</view>
				<!-- textarea -->
				<view v-if="cItem.form_type === 'areatext'" class="areaStyle">
					<view class="label">{{ cIndex + 1 }}. {{ cItem.form_name }}：</view>
					<view class="areaVal" style="color: #31C9E8;margin-top: 5rpx;">{{ cItem.val }}</view>
				</view>
				<!-- 图片 -->
				<view v-if="cItem.form_type === 'image'" class="imageStyle">
					<view class="label">{{ cIndex + 1 }}. {{ cItem.form_name }}：</view>
					<view v-if="Array.isArray(JSON.parse(cItem.val))" class="image-wrap">
						<image v-for="(sItem, sIndex) in JSON.parse(cItem.val)" class="image-item" :src="sItem" mode="aspectFill" @tap="previewMedia(JSON.parse(cItem.val), sIndex, 'image')"></image>
					</view>
				</view>
				<!-- 视频 -->
				<view v-if="cItem.form_type === 'video'" class="imageStyle">
					<view class="label">{{ cIndex + 1 }}. {{ cItem.form_name }}：</view>
					<view v-if="Array.isArray(JSON.parse(cItem.val))" class="video-wrap">
						<view v-for="(sItem, sIndex) in JSON.parse(cItem.val)" class="video-item" @tap="previewMedia(JSON.parse(cItem.val), sIndex, 'video')">视频{{ sIndex + 1 }}</view>
					</view>
				</view>
			</view>
		</view>
		<template v-for="(item, index) in flowList">
			<view v-if="item.children" class="flow-item">
				<view class="flow-name">{{ item.work_name }}</view>
				<template v-for="(cItem, cIndex) in item.children" v-if="item.children.length">
					<!-- text -->
					<view v-if="cItem.form_type === 'text'" class="flow-form-item form-input">
						<view class="label">{{ cItem.work_name }}</view>
						<input v-model="cItem.val" class="input" type="text" maxlength="64" placeholder="请输入"
							:disabled="cItem.state === '已提交' ? true : false" />
					</view>
					<view v-if="cItem.form_type === 'disable'" class="flow-form-item form-input">
						<view class="label">{{ cItem.work_name }}</view>
						<view>
							{{ cItem.val_file }}
						</view>
					</view>
					<!-- number -->
					<view v-if="['number', 'finance', 'duration'].indexOf(cItem.form_type) !== -1"
						class="flow-form-item form-input">
						<view class="label">{{ cItem.work_name }}</view>
						<input v-model="cItem.val" class="input" type="digit" maxlength="64" placeholder="请输入"
							:disabled="cItem.state === '已提交' ? true : false" />
					</view>
					<!-- textarea -->
					<view v-if="cItem.form_type === 'areatext'" class="flow-form-item form-textarea">
						<view class="label">{{ cItem.work_name }}</view>
						<textarea v-model="cItem.val" class="textarea" maxlength="512" placeholder="请输入"
							:disabled="cItem.state === '已提交' ? true : false" />
					</view>
					<!-- 年月日 -->
					<view v-if="cItem.form_type === 'date'" class="flow-form-item form-select"
						@tap="selectDate(index, cIndex)">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="text line-1" :class="{'selected': cItem.val}">{{ cItem.val || '请选择' }}</view>
						<image v-if="cItem.state === '待提交'" class="icon"
							src="../../static/icon/<EMAIL>" mode=""></image>
					</view>
					<!-- 年月日时分秒 -->
					<view v-if="cItem.form_type === 'datetime'" class="flow-form-item form-select"
						@tap="selectTime(index, cIndex)">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="text line-1" :class="{'selected': cItem.val}">{{ cItem.val || '请选择' }}</view>
						<image v-if="cItem.state === '待提交'" class="icon"
							src="../../static/icon/<EMAIL>" mode=""></image>
					</view>
					<!-- select选择 -->
					<view v-if="['template', 'radio'].indexOf(cItem.form_type) !== -1"
						class="flow-form-item form-select" @tap="selectRadio(index, cIndex)">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="text line-1" :class="{'selected': cItem.val}">
							{{ cItem.val || '请选择' }}
						</view>
						<image v-if="cItem.state === '待提交'" class="icon" src="/static/icon/<EMAIL>"
							mode=""></image>
					</view>
					<!-- 多选 -->
					<view v-if="cItem.form_type === 'checkbox'" class="flow-form-item form-select"
						@tap="selectCheckbox(index, cIndex)">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="text line-1" :class="{'selected': cItem.val}">
							{{ cItem.val ? cItem.val.join(', ') : '请选择' }}
						</view>
						<image v-if="cItem.state === '待提交'" class="icon" src="/static/icon/<EMAIL>" mode=""></image>
					</view>
					<!-- 图片 -->
					<view v-if="cItem.form_type === 'image'" class="flow-form-item form-image">
						<view class="label">{{ cItem.work_name }}</view>
						<template v-if="cItem.state === '待提交'">
							<u-upload accept="media" previewFullImage :maxCount="9" multiple
								:fileList="uploadConfig[`fileList${cItem.id}`]" previewImage
								@afterRead="(file, lists) => {handleFileAfterRead(file, 'all', index, cIndex)}"
								@delete="(file) => {handleDeleteMedia(file, index, cIndex)}"></u-upload>
								<view v-if="Array.isArray(cItem.val_file)" class="image-wrap">
									<image v-for="(sItem, sIndex) in cItem.val_file" class="image-item" :src="sItem" mode="aspectFill" @tap="previewMedia(cItem.val_file, sIndex, 'image')"></image>
								</view>
						</template>
						<template v-else>
							<view v-if="Array.isArray(cItem.val_file)" class="image-wrap">
								<image v-for="(sItem, sIndex) in cItem.val_file" class="image-item" :src="sItem"
									mode="aspectFill" @tap="previewMedia(cItem.val_file, sIndex, 'image')"></image>
							</view>
						</template>
					</view>
					<!-- 视频 -->
					<view v-if="cItem.form_type === 'video'" class="flow-form-item form-image">
						<view class="label">{{ cItem.work_name }}</view>
						<template v-if="cItem.state === '待提交'">
							<u-upload accept="video" previewFullImage :maxCount="9" multiple :fileList="uploadConfig[`fileList${cItem.id}`]" previewImage @afterRead="(file) => {handleFileAfterRead(file, 'video', index, cIndex)}" @delete="(file) => {handleDeleteMedia(file, index, cIndex)}"></u-upload>
							<view v-if="Array.isArray(cItem.val_file)" class="video-wrap">
								<view v-for="(sItem, sIndex) in cItem.val_file" class="video-item" @tap="previewMedia(cItem.val_file, sIndex, 'video')">视频{{ sIndex + 1 }}</view>
							</view>
						</template>
						<template v-else>
							<view v-if="Array.isArray(cItem.val_file)" class="video-wrap">
								<view v-for="(sItem, sIndex) in cItem.val_file" class="video-item"
									@tap="previewMedia(cItem.val_file, sIndex, 'video')">视频{{ sIndex + 1 }}</view>
							</view>
						</template>
					</view>
					<!-- 文件 -->
					<view v-if="['file', 'cad'].indexOf(cItem.form_type) !== -1" class="flow-form-item form-file">
						<view class="label">{{ cItem.work_name }}</view>
						<template v-if="cItem.state === '待提交'">
							<!-- #ifdef MP -->
							<view class="file-box">
								<view class="button" @click="chooseFile(index, cIndex)">上传文件</view>
								<view v-for="(sItem, sIndex) in uploadConfig[`fileList${cItem.id}`]" class="file-item">
									<view class="file-name">{{ sItem.name }}</view>
									<image class="delete-icon" src="/static/icon/<EMAIL>" mode=""
										@tap="handleDeleteFile({path: sItem.path}, index, cIndex)"></image>
								</view>
							</view>
							<!-- #endif -->
							<!-- #ifdef H5 -->
							<view class="file-box">
								<view class="button" @click="imgUpload(index, cIndex)">上传文件</view>
								<view v-for="(sItem, sIndex) in uploadConfig[`fileList${cItem.id}`]" class="file-item">
									<view class="file-name">{{ sItem.name }}</view>
									<image class="delete-icon" src="/static/icon/<EMAIL>" mode="" @tap="handleDeleteFile({path: sItem.path}, index, cIndex)"></image>
								</view>
							</view>
							<!-- #endif -->
						</template>
						<template v-else>
							<view v-if="Array.isArray(cItem.val_file)" class="file-wrap">
								<view v-for="(sItem, sIndex) in cItem.val_file" class="file-item" @tap="previewFile(sItem)">
									文件{{ sIndex + 1 }}</view>
							</view>
						</template>
					</view>
					<!-- 经纬度 -->
					<view v-if="cItem.form_type === 'geo'" class="flow-form-item form-geo">
						<view class="label">{{ cItem.work_name }}</view>
						<view class="value">
							<view v-if="cItem.val" class="text">{{ cItem.val }}</view>
							<view v-else class="button" @tap="selectLocation(index, cIndex)">获取位置</view>
						</view>
					</view>
				</template>
				<view class="flow-remark" v-if="item.remark">备注：{{ item.remark }}</view>
			</view>

			<view v-else-if="item.remark" class="flow-item">
				<view class="flow-name">{{ item.work_name }}</view>
				<view class="flow-remark" v-if="item.remark">备注：{{ item.remark }}</view>
			</view>

		</template>

		<view class="flow-item">
			<view class="flow-name">备注信息</view>
			<textarea v-model="remark" class="flow-textarea" placeholder="请输入备注" maxlength="1024"></textarea>
		</view>

		<view v-if="flag" class="button-box">
			<view v-if="flag !== 2" class="button reject" @tap="rejectConfig.show = true">
				<text class="text">驳回</text>
			</view>
			<!-- 两个按钮 -->
			<view class="button" @tap="cancelProcess()" v-if="flag == 2 && cancel" style="background: #FF7043;">
				<text class="text">取消流程</text>
			</view>
			<!-- 单独按钮 -->
			<view class="button" @tap="submit()">
				<text class="text">{{ flag === 2 ? '提交流程' : '审核通过' }}</text>
			</view>
			
		</view>

		<!-- selectRadio -->
		<tn-picker v-model="pickerConfig.show" :title="pickerConfig.title" mode="selector" :range="pickerConfig.range"
			:defaultSelector="pickerConfig.defaultSelector" rangeKey="text" @confirm="handlePickerConfirm"></tn-picker>

		<!-- selectDateTime -->
		<tn-picker v-model="pickerConfig.timeShow" :title="pickerConfig.title" mode="time" :params="pickerConfig.params"
			:startYear="pickerConfig.startYear" :endYear="pickerConfig.endYear"
			@confirm="handleTimePickerConfirm"></tn-picker>

		<!-- selectCheckbox -->
		<tn-popup v-model="checkboxConfig.show" mode="center" width="600" height="800" :borderRadius="8" closeBtn
			closeIconSize="36">
			<view class="checkbox-box">
				<tn-checkbox-group v-model="checkboxConfig.defaultCheckValue" wrap @change="handleCheckboxGroupChange">
					<tn-checkbox v-for="(item, index) in checkboxConfig.list" :key="index" :name="item.text">{{item.text}}</tn-checkbox>
				</tn-checkbox-group>
			</view>
		</tn-popup>

		<!-- 驳回 -->
		<tn-popup v-model="rejectConfig.show" mode="center" :borderRadius="8" :maskCloseable="false" :negativeTop="360">
			<view class="reject-box">
				<view class="header">驳回信息</view>
				<view class="node" @tap="rejectConfig.nodeShow = true">
					<view class="label">驳回节点</view>
					<view class="text" :class="{'selected': rejectConfig.nodeText}">{{ rejectConfig.nodeText || '请选择' }}
					</view>
					<image class="icon" src="/static/icon/<EMAIL>" mode=""></image>
				</view>
				<view class="remark">
					<view class="label">驳回原因</view>
					<textarea v-model="rejectConfig.remark" class="textarea" maxlength="512" placeholder="请输入" />
				</view>
				<view class="button-box">
					<view class="button cancel" @tap="rejectConfig.show = false">取消</view>
					<view class="button" @tap="confirmReject()">确认驳回</view>
				</view>
			</view>
		</tn-popup>
		<tn-picker v-model="rejectConfig.nodeShow" title="请选择驳回原因" mode="selector" :range="rejectConfig.nodeRange" rangeKey="text" @confirm="handleRejectNodeConfirm"></tn-picker>
	</view>
</template>

<script>
	import {
		getWorkDetails,
		uploadFile,
		rejectWork,
		submitWork
	} from '@/api/process.js'

	export default {
		data() {
			return {
				cancel: '',
				id: '', // 数据id
				customerInformation: {}, // 客户数据
				flowList: [], // 工作流列表
				circulatelog: [],
				flag: 0, // 类型
				remark: '', // 备注
				locationAuth: false, // 位置权限
				// picker选择器配置
				pickerConfig: {
					index: 0, // 数组下标
					inputIndex: 0, // 选择的表单数组下标
					show: false, // 显示控制
					timeShow: false, // 时间模式显示控制
					title: '', // 标题
					range: [], // 选择数据
					defaultSelector: [], // 默认选中数据
					params: {}, // 时间选择器配置
					startYear: 2000, // 时间模式起始时间
					endYear: new Date().getFullYear() + 1, // 时间模式结束时间
					defaultTime: '' // 时间模式默认时间
				},
				// 多选配置
				checkboxConfig: {
					index: 0, // 数组下标
					inputIndex: 0, // 选择的表单数组下标
					show: false,
					title: '', // 标题
					list: [], // 数据
					defaultCheckValue: [] // 选中的数据
				},
				// 驳回配置
				rejectConfig: {
					show: false,
					nodeId: '',
					nodeText: '',
					remark: '',
					nodeShow: false,
					nodeRange: [] // 选择数据
				},
				uploadConfig: {} // 文件上传配置
			}
		},
		onLoad(options) {
			this.id = options.id
			// 获取数据
			this.getData()
			// 获取位置权限
			uni.getSetting({
				success: (res) => {
					if (!res.authSetting['scope.userLocation']) {
						// 无权限，申请权限
						uni.authorize({
							scope: 'scope.userLocation',
							success: (ret) => {
								// 申请权限成功，获取位置
								this.locationAuth = true
							}
						})
					} else {
						// 有权限，直接获取位置
						this.locationAuth = true
					}
				}
			})
		},
		methods: {
			// 取消流程
			cancelProcess(){
				// this.$api.request('work/workcancel')
				uni.showModal({
					title: '请填写取消原因',
					cancelText: '取消',
					cancelColor: '#999',
					confirmText: '确定',
					confirmColor: '#6F87F8',
					editable: true,
					placeholderText: '请输入原因',
					success: (res) => {
						if (res.confirm) {
							if (!res.content) {
								this.$util.showToast('请输入原因')
							}
							// 执行操作
							this.$api.request('work/workcancel', {id: this.id, remark: res.content}, (res)=>{
								if(res.status == 'ok'){
									uni.navigateBack(1)
								}
							})
						}
					}
				})
			},
			// 获取数据
			getData() {
				getWorkDetails({
					id: this.id
				}).then(res => {
					if (res) {
						// 生成文件选择数据
						if (res.flow[res.flow.length - 1]?.children) {
							res.flow[res.flow.length - 1]?.children.forEach(item => {
								if (['image', 'video', 'file', 'cad'].indexOf(item.form_type) != -1) {
									this.$set(this.uploadConfig, `fileList${item.id}`, [])
								}
							})
						}
						this.cancel = res.cancel
						this.$nextTick(() => {
							this.customerInformation = res.profile
							this.flowList = res.flow
							this.circulatelog = res.circulatelog
							this.flag = res.flow[res.flow.length - 1]?.flag
							this.rejectConfig.nodeRange = res.abort
						})
					}
				})
			},
			// 提交流程
			async submit() {
				const flowData = this.flowList[this.flowList.length - 1]
				let formData = []
				if (flowData.children && flowData.children.length) {
					formData = await Promise.all(flowData.children.map(async item => {
						let value = item.val
						if (['checkbox', 'image', 'video', 'file', 'cad'].indexOf(item.form_type) !== -
							1) {
							value = JSON.stringify(Array.isArray(item.val) ? item.val : [])
						}
						if (item.form_type === 'geo_auto') {
							// 获取经纬度
							// #ifdef H5
								value = '111111,222222'
							// #endif
							// #ifdef MP
								value = await this.getLocation()
							// #endif
							
						}
						return {
							id: item.id,
							val: value
						}
					}))
				}
				const params = {
					id: flowData.id,
					data: JSON.stringify(formData),
					remark: this.remark
				}
				submitWork(params).then(res => {
					if (res) {
						// 发送刷新事件
						uni.$emit('refreshProcess')
						this.$util.redirectTo('success', {
							type: 'submit',
							info: res.info
						})
					}
				})
			},
			// 驳回
			confirmReject() {
				rejectWork({
					id: this.flowList[this.flowList.length - 1].id,
					'to_id': this.rejectConfig.nodeId,
					remark: this.rejectConfig.remark
				}).then(res => {
					if (res) {
						// 发送刷新事件
						uni.$emit('refreshProcess')
						this.$util.redirectTo('success', {
							type: 'reject',
							info: res.info
						})
					}
				})
			},
			// 单选
			selectRadio(index, inputIndex) {
				const {
					work_name,
					options,
					state
				} = this.flowList[index].children[inputIndex]
				if (state !== '待提交') {
					return false
				}
				this.pickerConfig.index = index
				this.pickerConfig.inputIndex = inputIndex
				this.pickerConfig.title = `请选择${work_name}`
				this.pickerConfig.range = options
				setTimeout(() => {
					this.pickerConfig.show = true
				}, 50)
			},
			// 多选
			selectCheckbox(index, inputIndex) {
				const {
					work_name,
					options,
					state
				} = this.flowList[index].children[inputIndex]
				if (state !== '待提交') {
					return false
				}
				// 显示
				this.checkboxConfig.index = index
				this.checkboxConfig.inputIndex = inputIndex
				this.checkboxConfig.list = options
				this.checkboxConfig.defaultCheckValue = this.flowList[index].children[inputIndex].val || []
				setTimeout(() => {
					this.checkboxConfig.show = true
				}, 50)
			},
			// 选择年月日
			selectDate(index, inputIndex) {
				const {
					work_name,
					options,
					state
				} = this.flowList[index].children[inputIndex]
				if (state !== '待提交') {
					return false
				}
				this.pickerConfig.index = index
				this.pickerConfig.inputIndex = inputIndex
				this.pickerConfig.title = `请选择${work_name}`
				this.pickerConfig.params = {
					year: true,
					month: true,
					day: true,
					hour: false,
					minute: false,
					second: false
				}
				setTimeout(() => {
					this.pickerConfig.timeShow = true
				}, 50)
			},
			// 时间选择
			selectTime(index, inputIndex) {
				const {
					work_name,
					options,
					state
				} = this.flowList[index].children[inputIndex]
				if (state !== '待提交') {
					return false
				}
				this.pickerConfig.index = index
				this.pickerConfig.inputIndex = inputIndex
				this.pickerConfig.title = `请选择${work_name}`
				this.pickerConfig.params = {
					year: true,
					month: true,
					day: true,
					hour: true,
					minute: true,
					second: true
				}
				setTimeout(() => {
					this.pickerConfig.timeShow = true
				}, 50)
			},
			// #ifdef H5
			deepParseJSON(data){
				const parser = (target) => {
				    if (typeof target === 'string') {
				      try {
				        return parser(JSON.parse(target));
				      } catch {
				        return target;
				      }
				    }
				    
				    if (Array.isArray(target)) {
				      return target.map(item => parser(item));
				    }
				    
				    if (typeof target === 'object' && target !== null) {
				      return Object.keys(target).reduce((acc, key) => {
				        acc[key] = parser(target[key]);
				        return acc;
				      }, {});
				    }
				    
				    return target;
				  };
				  
				  return parser(data);
			},
			imgUpload(index, inputIndex){
				console.log(this.flowList)
				const { form_type } = this.flowList[index].children[inputIndex]
				const extension = form_type === 'file' ? ['txt', 'md', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'pdf'] : ['dwg', 'dxf']
				// 选择多个文件并上传
				uni.chooseFile({
				  count: 9, // 最多选9个文件（浏览器限制）
				  accept: 'file', // 接受所有文件类型
				  success: async (res) => {
				    const files = res.tempFiles[0].type; // 获取文件数组
					console.log(res.tempFiles[0], '123')
				    const { name, path, type} = res.tempFiles[0]
				    this.handleFileAfterRead({ file: [{ name, type, url: path}] }, form_type, index, inputIndex)
					console.log(name, type, path)
					console.log('form_type', form_type, 'index', index, "inputIndex：", inputIndex)
				    // 循环上传所有文件
				  //   const uploadTasks = files.map(file => {
						// console.log(file.path)
				  //     return new Promise((resolve, reject) => {
				  //       uni.uploadFile({
				  //         filePath: file.path,
				  //         name: 'file', // 对应接口的 file 字段
				  //         formData: {
				  //           type: file.type.split('/')[0], // 文件类型自动获取
				  //           dir: 'work'     // 固定参数
				  //         },
				  //         header: {
				  //           'Authorization': 'Bearer ...' // 按需添加
				  //         },
				  //         success: (uploadRes) => {
						// 		resolve(uploadRes.data) // 上传成功
						// 		// const { name, path, type } = uploadRes.tempFiles[0]
								
						// 		// this.handleFileAfterRead({
						// 		// 	file: [{
						// 		// 		name,
						// 		// 		type,
						// 		// 		url: path
						// 		// 	}]
						// 		// }, form_type, index, inputIndex)
				  //         },
				  //         fail: (error) => {
				  //           reject(error) // 上传失败
				  //         }
				  //       })
				  //     })
				  //   })
				    // 等待所有上传结果
				  //   try {
				  //     const results = await Promise.all(uploadTasks)
				  //     uni.showToast({ title: `成功上传 ${results.length} 个文件`, icon: 'none' })
				  //     console.log('所有结果:', results)
					  
						// console.log('所有结果2:', this.deepParseJSON(results))
				  //   } catch (error) {
				  //     uni.showToast({ title: '部分文件上传失败', icon: 'none' })
				  //     console.error('上传错误:', error)
				  //   }
				  },
				  fail: (error) => {
				    console.error('选择文件失败', error)
				    uni.showToast({ title: '选择文件失败', icon: 'none' })
				  }
				})
			},
			// #endif
			// 选择文件
			// #ifdef MP
			chooseFile(index, inputIndex) {
				console.log(this.flowList)
				const {
					form_type,
				} = this.flowList[index].children[inputIndex]
					const extension = form_type === 'file' ? ['txt', 'md', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
						'pdf'
					] : ['dwg', 'dxf']
				wx.chooseMessageFile({
					count: 9,
					type: 'file',
					extension: extension,
					success: (res) => {
						const {
							name,
							path,
							type
						} = res.tempFiles[0]
						this.handleFileAfterRead({
							file: [{
								name,
								type,
								url: path
							}]
						}, form_type, index, inputIndex)
						
						console.log({ file: [{
								name,
								type,
								url: path
							}]
						})
						console.log('form_type', form_type, 'index', index, "inputIndex：", inputIndex)
						
					}
				})
			},
			// #endif
			// 选择位置
			selectLocation(index, inputIndex) {
				// 获取位置
				// #ifdef MP
				const getLoc = async () => {
					
						this.flowList[index].children[inputIndex].val = await this.getLocation()
					
					
				}
				if (this.locationAuth) {
					getLoc()
				} else {
					uni.openSetting({
						success: (res) => {
							if (res.authSetting['scope.userLocation']) {
								this.locationAuth = true
								// 获取位置
								getLoc()
							}
						}
					})
				}
				// #endif
				// #ifdef H5
					this.flowList[index].children[inputIndex].val = '111111, 222222'
					return
				// #endif
			},
			// 处理picker选择器回调
			handlePickerConfirm(e) {
				const a = e[0] || 0
				const {
					index,
					inputIndex
				} = this.pickerConfig
				const {
					options
				} = this.flowList[index].children[inputIndex]
				const {
					form_type
				} = this.flowList[index].children[inputIndex]
				this.flowList[index].children[inputIndex].val = options[a].text
			},
			// 处理checkbox回调
			handleCheckboxGroupChange(e) {
				const {
					index,
					inputIndex
				} = this.checkboxConfig
				this.flowList[index].children[inputIndex].val = e
			},
			// 处理timePicker选择器回调
			handleTimePickerConfirm(e) {
				const {
					index,
					inputIndex
				} = this.pickerConfig
				if (e.second) {
					// 年月日时分秒
					this.flowList[index].children[inputIndex].val =
						`${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`
				} else {
					// 年月日
					this.flowList[index].children[inputIndex].val = `${e.year}-${e.month}-${e.day}`
				}
			},
			async upLoadImg(event, fileType, index, inputIndex, i){
				event.file[i].type = event.file[i].type.split('/')[0]
				console.log(event);
				const res = await uploadFile({
					formData: {
						type: fileType === 'all' ? event.file[i].type : fileType,
						dir: 'work'
					},
					filePath: event.file[i].url,
					name: 'file'
				})
				if (res) {
					console.log(res, 'res数据')
					let fileVal = uni.$u.deepClone(this.flowList[index].children[inputIndex].val)
					if (Array.isArray(fileVal)) {
						fileVal.push(res.path)
					} else {
						fileVal = [res.path]
					}
					this.flowList[index].children[inputIndex].val = fileVal
					// 组件数据
					const inputId = this.flowList[index].children[inputIndex].id
					const oldFileList = this.uploadConfig[`fileList${inputId}`]
					const newFileList = [...oldFileList, { ...event.file[i], path: res.path }]
					this.$set(this.uploadConfig, `fileList${inputId}`, newFileList)
					console.log(this.uploadConfig, 'this.uploadConfig之后')
				} else {
					this.$util.showToast('上传失败，请重试～')
				}
			},
			// 文件选择回调
			async handleFileAfterRead(event, fileType, index, inputIndex) {
				for(let i = 0; i < event.file.length; i++){
					this.upLoadImg(event, fileType, index, inputIndex, i)
				}
				
				
				// console.log(this.uploadConfig, 'this.uploadConfig之后')
				// console.log(index, inputIndex, '我是当前代码需求')
				// event.file[0].type = event.file[0].type.split('/')[0]
				// console.log(event);
				// const res = await uploadFile({
				// 	formData: {
				// 		type: fileType === 'all' ? event.file[0].type : fileType,
				// 		dir: 'work'
				// 	},
				// 	filePath: event.file[0].url,
				// 	name: 'file'
				// })
				// console.log(res);
				// if (res) {
				// 	// 表单数据
				// 	let fileVal = uni.$u.deepClone(this.flowList[index].children[inputIndex].val)
				// 	if (Array.isArray(fileVal)) {
				// 		fileVal.push(res.path)
				// 	} else {
				// 		fileVal = [res.path]
				// 	}
				// 	this.flowList[index].children[inputIndex].val = fileVal
				// 	// 组件数据
				// 	console.log(this.uploadConfig, 'this.uploadConfig之前')
				// 	const inputId = this.flowList[index].children[inputIndex].id
				// 	const oldFileList = this.uploadConfig[`fileList${inputId}`]
				// 	const newFileList = [...oldFileList, { ...event.file[0], path: res.path}]
				// 	this.$set(this.uploadConfig, `fileList${inputId}`, newFileList)
				// 	console.log(this.uploadConfig, 'this.uploadConfig之后')
				// } else {
				// 	this.$util.showToast('上传失败，请重试～')
				// }
			},
			// 图片删除回调
			handleDeleteMedia(file, index, inputIndex) {
				// 文件删除回调
				this.handleDeleteFile(file.file, index, inputIndex)
			},
			// 文件删除回调
			handleDeleteFile(file, index, inputIndex) {
				// 表单数据
				const fileVal = this.flowList[index].children[inputIndex].val
				const data = fileVal.filter(item => item !== file.path)
				this.flowList[index].children[inputIndex].val = data
				// 组件数据
				const inputId = this.flowList[index].children[inputIndex].id
				const oldFileList = this.uploadConfig[`fileList${inputId}`]
				const newFileList = oldFileList.filter(item => item.path !== file.path)
				this.$set(this.uploadConfig, `fileList${inputId}`, newFileList)
			},
			// 驳回节点选择回调
			handleRejectNodeConfirm(e) {
				const a = e[0] || 0
				const {
					id,
					text
				} = this.rejectConfig.nodeRange[a]
				this.rejectConfig.nodeId = id
				this.rejectConfig.nodeText = text
			},
			// 获取位置信息
			getLocation() {
				return new Promise(resolve => {
					this.$util.showLoading('正在获取位置')
					uni.getLocation({
						type: 'gcj02', // 坐标格式
						success: (res) => {
							resolve(`${res.longitude.toFixed(6)},${res.latitude.toFixed(6)}`)
						},
						fail: (err) => {
							this.$util.showToast('获取位置失败')
						},
						complete: () => {
							this.$util.hideLoading()
						}
					})
				})
			},
			// 预览多媒体
			previewMedia(list, index, type) {
				if (type === 'image') {
					uni.previewImage({
						current: index,
						urls: list
					})
				}
				if (type === 'video') {
					wx.previewMedia({
						sources: [{
							url: list[index], // 视频的 URL
							type: 'video', // 设置类型为视频
						}]
					})
				}
			},
			// 预览文件
			previewFile(url) {
				uni.downloadFile({
					url: url,
					success: function(res) {
						var filePath = res.tempFilePath
						uni.openDocument({
							filePath: filePath,
							fail: () => {
								this.$util.showToast('文件预览失败，请使用PC端查看');
							}
						})
					}
				})
			}
		},
		watch: {
			// 驳回
			'rejectConfig.show': function(newVal) {
				if (newVal === false) {
					// 重置参数
					this.rejectConfig.nodeId = ''
					this.rejectConfig.nodeText = ''
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	
	.newClass{
		.titles_new{
			font-weight: 700;
			font-size: 32rpx;
			color: #3A3B40;
			line-height: normal;
		}
		.textStyle{
			width: calc(100% - 20rpx);
			margin: 0 auto;
			background: #F4F4F4;
			border-radius: 10rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.view1{
				width: 83%;
			}
			.view2{
				width: 17%;
				text-align: right;
			}
		}
		.areaStyle{
			width: calc(100% - 20rpx);
			margin: 0 auto;
			background: #F4F4F4;
			border-radius:16rpx;
			padding: 20rpx;
			.areaVal{
				width: calc(100% - 36rpx);
				margin-left: 36rpx;
			}
		}
		.imageStyle{
			width: calc(100% - 20rpx);
			margin: 0 auto;
			background: #F4F4F4;
			border-radius: 18rpx;
			padding: 20rpx;
			.video-wrap {
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
			
				.video-item {
					width: 100rpx;
					margin: 0 0 8px 8px;
					padding: 4rpx 0;
					background: #6FB2E2;
					border-radius: 8rpx;
					font-size: 28rpx;
					font-weight: 400;
					color: #FFFFFF;
					line-height: normal;
					text-align: center;
				}
			}
			.image-wrap {
				flex: 1;
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				.image-item {
					width: 80px;
					height: 80px;
					margin: 0 0 8px 8px;
				}
			}
		}
		.label{
			font-weight: 700;
			font-size: 26rpx;
			color: #3A3B40;
			line-height: normal;
		}
		view{
			margin-top: 15rpx;
		}
	}
	.container {
		padding: 40rpx;
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));

		.customer-information {
			margin-bottom: 40rpx;
			padding: 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			.main-info {
				font-weight: 500;
				font-size: 30rpx;
				color: #3A3B40;
				line-height: 30rpx;
			}

			.secondary-info {
				display: block;
				margin-top: 16rpx;
				font-weight: 500;
				font-size: 26rpx;
				color: #74747B;
				line-height: 26rpx;
			}

			.main-info+.secondary-info {
				margin-top: 32rpx !important;
			}
		}

		.flow-item {
			position: relative;
			padding: 0 32rpx 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			&+.flow-item {
				margin-top: 38rpx;

				&::before {
					content: '';
					position: absolute;
					top: -50rpx;
					left: 50rpx;
					width: 28rpx;
					height: 68rpx;
					background: url(../../static/icon/<EMAIL>) center/100% repeat;
					z-index: -1;
				}

				&::after {
					content: '';
					position: absolute;
					top: -50rpx;
					right: 50rpx;
					width: 28rpx;
					height: 68rpx;
					background: url(../../static/icon/<EMAIL>) center/100% repeat;
					z-index: -1;
				}
			}

			.flow-name {
				padding: 48rpx 0 20rpx;
				font-weight: 700;
				font-size: 32rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.flow-remark {
				padding: 20rpx 0 0 0;
				font-size: 28rpx;
				color: #ff0000;
				font-weight: bold;
			}

			.flow-textarea {
				height: 156rpx;
				font-size: 28rpx;
				color: #555555;
				line-height: normal;
			}

			.flow-form-item {
				display: flex;
				padding: 20rpx 0;
				border-bottom: 2rpx solid #F0F0F0;

				.label {
					width: 200rpx;
					font-size: 28rpx;
					color: #3A3B40;
					line-height: normal;
				}

				.text {
					flex: 1;
					font-size: 28rpx;
					color: #BBBEC6;
					line-height: normal;
					text-align: right;

					&.selected {
						color: #555555;
					}
				}

				.icon {
					display: block;
					width: 28rpx;
					height: 16rpx;
					margin-left: 30rpx;
				}

				// input输入框
				&.form-input {
					display: flex;
					align-items: center;
					min-height: 96rpx;

					.input {
						flex: 1;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}
				}

				// textarea输入框
				&.form-textarea {
					display: flex;
					padding: 34rpx 0;

					.textarea {
						flex: 1;
						height: 96rpx;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}

					.textarea-text {
						flex: 1;
						min-height: 96rpx;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}
				}

				// select选择框
				&.form-select {
					display: flex;
					align-items: center;
					min-height: 96rpx;
				}

				// image类型
				&.form-image {
					display: flex;
					padding: 22rpx 0;

					::v-deep .u-upload__wrap {
						justify-content: flex-end;

						.u-upload__wrap__preview,
						.u-upload__button {
							margin: 0 0 8px 8px;
						}
					}

					.image-wrap {
						flex: 1;
						display: flex;
						flex-wrap: wrap;
						justify-content: flex-end;

						.image-item {
							width: 80px;
							height: 80px;
							margin: 0 0 8px 8px;
						}
					}

					.video-wrap {
						flex: 1;
						display: flex;
						flex-wrap: wrap;
						justify-content: flex-end;

						.video-item {
							width: 100rpx;
							margin: 0 0 8px 8px;
							padding: 4rpx 0;
							background: #6FB2E2;
							border-radius: 8rpx;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							line-height: normal;
							text-align: center;
						}
					}
				}

				// file类型
				&.form-file {
					display: flex;
					padding: 22rpx 0;

					.label {
						display: flex;
						align-items: center;
						height: 56rpx;
					}

					.file-box {
						flex: 1;
						display: flex;
						flex-direction: column;
						align-items: flex-end;

						.button {
							display: flex;
							justify-content: center;
							align-items: center;
							height: 56rpx;
							padding: 0 20rpx;
							background: $theme-color;
							border-radius: 8rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
							line-height: normal;
						}

						.file-item {
							width: 100%;
							display: flex;
							align-items: center;
							margin-top: 10rpx;

							.file-name {
								flex: 1;
								font-weight: 400;
								font-size: 28rpx;
								color: #555555;
								line-height: normal;
								text-align: right;
							}

							.delete-icon {
								width: 20rpx;
								height: 20rpx;
								margin-left: 16rpx;
							}
						}
					}

					.file-wrap {
						flex: 1;
						display: flex;
						flex-wrap: wrap;
						justify-content: flex-end;

						.file-item {
							width: 100rpx;
							margin: 0 0 8px 8px;
							padding: 4rpx 0;
							background: #6FB2E2;
							border-radius: 8rpx;
							font-size: 28rpx;
							font-weight: 400;
							color: #FFFFFF;
							line-height: normal;
							text-align: center;
						}
					}
				}

				&.form-geo {
					display: flex;
					align-items: center;
					min-height: 96rpx;

					.value {
						flex: 1;
						display: flex;
						justify-content: flex-end;

						.text {
							flex: 1;
							font-size: 28rpx;
							color: #555555;
							line-height: normal;
							text-align: right;
						}

						.button {
							display: flex;
							justify-content: center;
							align-items: center;
							height: 56rpx;
							padding: 0 20rpx;
							background: $theme-color;
							border-radius: 8rpx;
							font-weight: 500;
							font-size: 28rpx;
							color: #FFFFFF;
							line-height: normal;
						}
					}
				}
			}
		}

		.button-box {
			display: flex;
			align-items: center;
			margin-top: 40rpx;

			.button {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 100rpx;
				background: $theme-color;
				border-radius: 8rpx;

				&.reject {
					background: #E94848;
				}

				&+.button {
					margin-left: 30rpx;
				}

				.text {
					font-weight: 500;
					font-size: 32rpx;
					color: #FFFFFF;
					line-height: normal;
				}
			}
		}
	}

	::v-deep .input-placeholder,
	::v-deep .textarea-placeholder {
		color: #BBBEC6;
	}

	::v-deep .checkbox-box {
		padding: 80rpx 40rpx 40rpx;

		.tn-checkbox-class {
			margin-top: 20rpx;
		}
	}

	::v-deep .reject-box {
		box-sizing: border-box;
		width: calc(100vw - 80rpx);
		padding: 0 32rpx 32rpx;

		.header {
			display: flex;
			justify-content: center;
			align-items: center;
			height: 88rpx;
			font-weight: 700;
			font-size: 32rpx;
			color: #4A4A4A;
			line-height: normal;
		}

		.node {
			display: flex;
			align-items: center;
			height: 96rpx;
			border-bottom: 2rpx solid #F0F0F0;

			.label {
				width: 200rpx;
				font-size: 28rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.text {
				flex: 1;
				font-size: 28rpx;
				color: #BBBEC6;
				line-height: normal;
				text-align: right;

				&.selected {
					color: #555555;
				}
			}

			.icon {
				display: block;
				width: 28rpx;
				height: 16rpx;
				margin-left: 30rpx;
			}
		}

		.remark {
			display: flex;
			padding: 34rpx 0;
			border-bottom: 2rpx solid #F0F0F0;

			.label {
				width: 200rpx;
				font-size: 28rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.textarea {
				flex: 1;
				height: 96rpx;
				font-size: 28rpx;
				color: #555555;
				line-height: normal;
				text-align: right;
			}
		}

		.button-box {
			display: flex;
			align-items: center;
			margin-top: 40rpx;

			.button {
				display: flex;
				justify-content: center;
				align-items: center;
				flex: 1;
				height: 80rpx;
				background: #E94848;
				border-radius: 8rpx;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: normal;

				&.cancel {
					background: #FFFFFF;
					border: 2rpx solid #DCDFE6;
					color: #666666;
				}
			}
		}
	}
</style>