.tab-bar {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	box-sizing: border-box;
	height: calc(112rpx + env(safe-area-inset-bottom));
	padding-bottom: env(safe-area-inset-bottom);
	background: #fff;
	box-shadow: 0 0 12rpx 0 rgba(0,0,0,0.1);
	z-index: 99999;
}

.tab-bar-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	text-align: center;
}

.tab-bar-item image {
	display: block;
	width: 54rpx;
	height: 54rpx;
}

.tab-bar-item view {
	margin-top: 4rpx;
	font-size: 20rpx;
	line-height: normal;
}