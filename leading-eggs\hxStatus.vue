<template>
	<view class="container">
		<view class="hr"></view>
		<image class="icon" src="../static/cghx.png" mode="" v-if="isStatus"></image>
		<image class="icon" src="../static/sbhx.png" mode="" v-else></image>
		<text class="title" :style="{color: isStatus ? '' : 'red'}">{{ title }}</text>
		<text class="sub-title" :style="{color: isStatus ? '' : 'red'}">{{ info }}</text>
		<text class="sub-title">点击「返回」按钮离开当前页面</text>
		<view style="display: flex;justify-content: space-between;margin-top: 292rpx;gap: 50rpx;">
			<view class="button" @tap="back()">
				<text class="text">返回首页</text>
			</view>
			<view class="button" @tap="jump" style="background: #FBBD12;" v-if="dayZb">
				<text class="text">去准备</text>
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '核销成功',
				dataCode: {
					aid: "201",
					ph: "18813082663",
					uid: "10049"
				},
				isStatus: true,
				dayZb: false,
				info: '' // 提示语
			}
		},
		onLoad(e) {
			if(e.hasOwnProperty( 'scene') ){
				if (e.scene.includes('%')) {
				  // 包含编码字符，说明未被自动解码
				  this.dataCode = this.$api.praseScene(decodeURIComponent(e.scene))
				  console.log(e, '我是苹果解密之后')
				} else {
				  // 已被解码，直接使用
				  this.dataCode = e
				  console.log(e, '我是安卓未解密之后')
				}
			} else{
				this.dataCode = e
				console.log(e, '我是安卓解密之后')
			}
			this.setWriteOff()
		},
		methods: {
			jump(){
				uni.navigateTo({
					url: `/leading-eggs/index?id=0&status=add&isS=1`
				})
			},
			setWriteOff(){
				this.$api.request('activity/setWriteOff', {phone: this.dataCode.ph, activity_id: this.dataCode.aid, belong_user_id: this.dataCode.uid }, (res)=>{
					if(res.status == 'ok'){
						this.title = '核销成功'
						this.isStatus = true
						this.info = res.info + "用户手机号：" + this.dataCode.ph
					}else{
						if(!res.is_day_work_add){
							this.dayZb = false
						}
						this.title = '核销失败'
						this.isStatus = false
						this.info = res.info
					}
				})
			},
			back(){
				uni.navigateTo({
					url: '/pages/index/index'
				})
			}
		}
	}
</script>

<style>
	page {
		background: #fff !important;
	}
</style>
<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;

		.hr {
			width: 100%;
			height: 12rpx;
			background: #F8F8F8;
		}

		.icon {
			width: 300rpx;
			height: 300rpx;
			margin-top: 200rpx;
		}

		.title {
			margin: 44rpx 0 32rpx;
			font-size: 36rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			color: #3A3B40;
			line-height: 36rpx;
		}

		.sub-title {
			font-size: 28rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			color: #666666;
			line-height: 48rpx;
			letter-spacing: 1px;
		}

		.button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 300rpx;
			height: 100rpx;
			background: #039BD2;
			border-radius: 8rpx;

			.text {
				font-size: 32rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				line-height: normal;
			}
		}
	}
</style>