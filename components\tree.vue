<template>
  <view class="tree-container">
    <!-- 递归渲染树节点 -->
    <view v-for="node in treeData" :key="node.id">
      <view class="node-item">
        <!-- 复选框 -->
        <view 
          class="checkbox"
          :class="{
            'checked': node.checked,
            'half-checked': node.halfChecked
          }"
          @click="toggleCheck(node)"
        >
          {{ getCheckboxSymbol(node) }}
        </view>

        <!-- 节点名称 -->
        <text class="label">{{ node.label }}</text>

        <!-- 展开/折叠图标（仅父节点显示） -->
        <text 
          v-if="node.children"
          class="expand-icon"
          @click="toggleExpand(node)"
        >
          {{ node.expanded ? '▼' : '▶' }}
        </text>
      </view>

      <!-- 子节点容器 -->
      <view 
        v-show="node.expanded && node.children"
        class="children-container"
      >
        <tree-node 
          :nodes="node.children"
          :parentId="node.id"
          @node-toggle="handleNodeToggle"
        />
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'tree-node',
  props: {
    nodes: {
      type: Array,
      default: () => []
    },
    parentId: [Number, String]
  },
  data() {
    return {
      // 树形数据示例（默认展开所有节点）
      treeData: [
        {
          id: 1,
          label: '父节点1',
          checked: false,
          halfChecked: false,
          expanded: true,  // 默认展开
          children: [
            {
              id: 11,
              label: '子节点1-1',
              checked: false,
              expanded: true  // 如果子节点还有子节点也需要展开
            },
            {
              id: 12,
              label: '子节点1-2',
              checked: false
            }
          ]
        },
        {
          id: 2,
          label: '父节点2',
          checked: false,
          halfChecked: false,
          expanded: true,  // 默认展开
          children: [
            {
              id: 21,
              label: '子节点2-1',
              checked: false,
              children: [
                {
                  id: 211,
                  label: '孙节点2-1-1',
                  checked: false
                }
              ]
            }
          ]
        }
      ]
    }
  },
  created() {
    // 初始化时展开所有节点（可选）
    // this.expandAllNodes(true)
  },
  methods: {
    /* 核心方法：复选框点击 */
    toggleCheck(node) {
      node.checked = !node.checked
      node.halfChecked = false
      
      // 处理子节点
      if (node.children) {
        this.toggleChildren(node, node.checked)
      }
      
      // 更新父节点状态
      this.updateParentStatus(node)
      
      this.$forceUpdate()
    },

    /* 递归更新子节点状态 */
    toggleChildren(node, status) {
      if (!node.children) return
      
      node.children.forEach(child => {
        child.checked = status
        child.halfChecked = false
        if (child.children) this.toggleChildren(child, status)
      })
    },

    /* 更新所有父节点状态 */
    updateParentStatus(node) {
      const parent = this.findParent(node.id)
      if (!parent) return

      const childrenStatus = parent.children.map(c => c.checked)
      const allChecked = childrenStatus.every(Boolean)
      const someChecked = childrenStatus.some(Boolean)

      parent.checked = allChecked
      parent.halfChecked = !allChecked && someChecked

      // 递归向上更新
      this.updateParentStatus(parent)
    },

    /* 查找父节点 */
    findParent(childId) {
      // 在整棵树中查找父节点
      let parent = null
      const walkTree = (nodes) => {
        for (const node of nodes) {
          if (node.children) {
            if (node.children.some(c => c.id === childId)) {
              parent = node
              break
            }
            walkTree(node.children)
          }
        }
      }
      walkTree(this.treeData)
      return parent
    },

    /* 展开/折叠切换 */
    toggleExpand(node) {
      node.expanded = !node.expanded
      this.$forceUpdate()
    },

    /* 展开所有节点方法 */
    expandAllNodes(expand = true) {
      const walk = nodes => {
        nodes.forEach(node => {
          if (node.children) {
            node.expanded = expand
            walk(node.children)
          }
        })
      }
      walk(this.treeData)
      this.$forceUpdate()
    },

    /* 获取复选框显示符号 */
    getCheckboxSymbol(node) {
      if (node.halfChecked) return '-'
      return node.checked ? '✓' : ''
    },

    /* 处理子组件事件 */
    handleNodeToggle(payload) {
      this.$emit('node-toggle', payload)
    }
  }
}
</script>

<style scoped>
.tree-container {
  padding: 15px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.checkbox {
  width: 24px;
  height: 24px;
  border: 1px solid #999;
  border-radius: 4px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.checked {
  background: #007AFF;
  border-color: #007AFF;
  color: white;
}

.half-checked {
  background: #e0e0e0;
  border-color: #999;
}

.label {
  flex: 1;
  font-size: 16px;
}

.expand-icon {
  padding: 0 10px;
  font-size: 12px;
  color: #666;
}

.children-container {
  margin-left: 30px;
}
</style>