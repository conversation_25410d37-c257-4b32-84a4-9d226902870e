<template>
	<view class="app">
		<view class="title"></view>
		<view class="input_app">
			<view class="input_info" v-if="statusNow != 'detail'">
				<view class="item_list">
					<view class="lable">
						省市区
					</view>
					<view class="value" @click="showPicker = true">
						<view class="tn-color-gray" v-if="selectedAddress == ''">
							请选择省市区
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="" v-else>
							{{ selectedAddress }}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						村镇串户数
					</view>
					<view class="value">
						<tn-button backgroundColor="#01BEFF" fontColor="#fff" size="sm" @click="addVillage">添 加</tn-button>
					</view>
				</view>
				<view class="villageInfo" v-for="(item, index) in formData.village" :key="index">
					<view class="villageMin">
						{{ index + 1 }}
					</view>
					<view class="villageLeft">
						<picker @change="(e) => showStreetPickerStatus(e, index)" :value="item.indexTwon" :range="townList" range-key="name">
							<view class="item_list">
								<view class="lable">
									乡镇街道
								</view>
								<view class="value">
									<view class="tn-color-gray" v-if="item.indexTwon === -1">
										乡/镇
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{townList[item.indexTwon].name}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</picker>
						<picker @change="(e) => getVillageName(e, index)" :value="item.indexVillage" :range="item.villageArr" range-key="name">
							<view class="item_list">
								<view class="lable">
									乡村/小区
								</view>
								<view class="value">
									<view class="tn-color-gray" v-if="item.indexVillage === -1">
										乡村/小区
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{item.villageArr[item.indexVillage].name}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view> 
						</picker>
						<view class="item_list">
							<view class="lable">
								串户数
							</view>
							<view class="value">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<tn-number-box v-model="item.link_num"></tn-number-box>
								</view>
							</view>
						</view>
					</view>
					<view class="villageRight" @click="deleteVillage(index)">
						<text class="tn-icon-delete-fill"></text>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						转发朋友圈数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.wechat_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						转抖音条数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.tiktok_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						通话数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.calls_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						面访数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.interview_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						带看进店数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.visits_store_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						建企业微信群数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.build_group_num" :key="formData.build_group_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						加企业微信群人数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.join_group_num"></tn-number-box>
						</view>
					</view>
				</view>
				
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						租房意向数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.intention_num"></tn-number-box>
						</view>
					</view>
				</view>

                <view class="item_list" v-if="isTrue">
					<view class="lable">
						备注
					</view>
				</view>
                <view class="item_list" v-if="isTrue">
					<!-- <textarea class="textarea" v-model="formData.remark" :placeholder="请输入" ></textarea> -->
                    <textarea class="textarea" v-model="formData.remark" placeholder="请输入"></textarea>
				</view>
                

			</view>
			<view class="input_info" v-else>
				<view class="item_list">
					<view class="lable">
						省市区
					</view>
					<view class="value">
						<view class="tn-color-gray">
							{{ selectedAddress }}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						村镇串户数
					</view>
				</view>
				<view class="villageInfo" v-for="(item, index) in formData.village" :key="index">
					<view class="villageMin">
						{{ index + 1 }}
					</view>
					<view class="villageLeft">
						<view class="item_list">
							<view class="lable">
								乡镇街道
							</view>
							<view class="value">
								<view class="tn-color-gray">
									{{townList[item.indexTwon].name}}
								</view>
							</view>
						</view>
						<view class="item_list">
							<view class="lable">
								乡村/小区
							</view>
							<view class="value">
								<view class="tn-color-gray">
									{{item.villageArr[item.indexVillage].name}}
								</view>
							</view>
						</view> 
						<view class="item_list">
							<view class="lable">
								串户数
							</view>
							<view class="value">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									{{ item.link_num }}
								</view>
							</view>
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						转发朋友圈数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{ formData.wechat_num }}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						转抖音条数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{formData.tiktok_num}}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						通话数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{formData.calls_num}}
						</view>
					</view>
				</view>
				<view class="item_list" v-if="isTrue">
					<view class="lable">
						面访数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							<tn-number-box v-model="formData.interview_num"></tn-number-box>
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						带看进店数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{formData.visits_store_num}}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						建企业微信群数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{formData.build_group_num}}
						</view>
					</view>
				</view>
				<view class="item_list">
					<view class="lable">
						加企业微信群人数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{formData.join_group_num}}
						</view>
					</view>
				</view>
				
				<view class="item_list">
					<view class="lable">
						租房意向数
					</view>
					<view class="value">
						<view class="tn-color-gray tn-color-black" style="max-width:50vw">
							{{formData.intention_num}}
						</view>
					</view>
				</view>
                <view class="item_list" v-if="isTrue">
					<view class="lable">
						备注
					</view>
				</view>
                <view class="item_list" v-if="isTrue">
					{{ formData.remark }}
				</view>
			</view>
		</view>
		<view class="buton" @click="addView" v-if="statusNow == 'add'"> 提 交 </view>
		<view class="buton" style="background: #FBBD12;" @click="editView" v-if="statusNow == 'edit'"> 确认修改 </view>
		<u-picker :show="showPicker" :columns="columns" keyName="name" @confirm="confirmHandler" @cancel="showPicker = false" @change="changeHandler" :defaultIndex="defaultIndex" :key="pickerKey" />
	</view>
</template>
<script>
import httpApi from '@/config/app.js'
	export default{
		data(){
			return{
				threeList: [],
				townList: [],
				statusNow: 'add',
				twoShow: false,
				showPicker: false,
				showStreetPicker: false,
				streetColumns: [[], []], // 街道列和小区列
				selectedStreet: '',
				currentDistrictCode: null, // 保存当前选择的区级编码
				columns: [[], [], []], // 三级数据容器
				rawData: [],          // 原始接口数据
				selectedAddress: '',   // 选中结果
				channelSourceArr: [],
				defaultIndex: [0, 0, 0],
				treetPicker: [0, 0],
				indexChannel: -1,
				formData: {
					remark: '',
					province: '',
					city: '',
					area: '',
					area_code: 0,
					wechat_num: 0,
					tiktok_num: 0,
					visits_store_num: 0,
					build_group_num: 0,
					intention_num: 0,
					calls_num: 0,
					interview_num: 0,
					join_group_num: 0,
					village: [],
				},
				formImg: {
					dir: 'profile',
					type: 'image',
				},
				action: httpApi + '/api/mini.main/upload',
				fileList: [],
				disabled: false,
				autoUpload: true,
				maxCount: 9,
				showUploadList: true,
				showProgress: true,
				deleteable: true,
				customBtn: false,
				pickerKey: 0,
				setCode: '',
				isS: 0,
				isTrue: false,
				ids: 0,
				getCtivIndex: 0
			}
		},
		onLoad(e) {
			this.getCity()
			this.statusNow = e.status
			if(e.id != 0){
				this.ids = e.id
				this.detail()
				console.log('我是数据像呼吸')
			} else{
				this.formData.class = e.class
				this.isTrue = true
			}
			
		},
		methods: {
			deleteVillage(i){
				this.formData.village.splice(i, 1)
			},
			addVillage(){
				this.formData.village.push({town: '', village: '', link_num: '', town_code: '', village_code: '', villageArr: [], indexTwon: -1, indexVillage: -1})
			},
			removeImg(index, lists, name){
				console.log(this.$refs.imageUpload)
				console.log(index, lists, name)
			},
			detail(){
				this.$api.request('village/info', {id: this.ids}, (res)=>{
					if(res.status == 'ok'){
						this.formData.id = res.data.id
						this.formData.build_group_num = res.data.build_group_num
						this.formData.join_group_num = res.data.join_group_num
						this.formData.tiktok_num = res.data.tiktok_num
						this.formData.visits_store_num = res.data.visits_store_num
						this.formData.intention_num = res.data.intention_num
						this.formData.calls_num = res.data.calls_num
						this.formData.interview_num = res.data.interview_num
						this.formData.remark = res.data.remark
						
						this.formData.wechat_num = res.data.wechat_num
						// 省市区回显
						this.selectedAddress = `${res.data.province}-${res.data.city}-${res.data.area}`;
						this.$nextTick(()=>{
							let that = this
							that.rawData.map((v, i) => {
								if(v.name == res.data.province){
									that.defaultIndex[0] = i
									v.children.map((x, y)=>{
										if(x.name == res.data.city){
											that.defaultIndex[1] = y
											x.children.map((x1, y1)=>{
												if(x1.name == res.data.area){
													that.defaultIndex[2] = y1
													this.getDetailParent(x1.code, res.data.village_detail)
												}
											})
										}
									})
								}
							})
							console.log(this.defaultIndex, 'rawData')
						})
						this.isTrue = true
						console.log(this.isTrue, 'this.isTrue')
					}
				})
			},
			getDetailParent(code, item){
				this.$api.request('common/getCityList', {code: code}, (res)=>{
					if(res.status == 'ok'){
						this.townList = res.list.info
						for(let i = 0; i < item.length; i++){
							const index = this.townList.findIndex(value => value.code == item[i].town_code);
							this.formData.village.push({town: item[i].town, village: item[i].village, link_num: item[i].link_num, town_code: item[i].town_code, village_code: item[i].village_code, villageArr: [], indexTwon: index, indexVillage: -1})
							this.getDetailCz(this.formData.village[i], i)
						}
					}
				})
			},
			getDetailCz(item, i){
				this.$api.request('common/getCityList', {code: item.town_code}, (res)=>{
					if(res.status == 'ok'){
						this.formData.village[i].villageArr = res.list.info
						const index = res.list.info.findIndex(value => value.code == item.village_code);
						this.formData.village[i].indexVillage = index
						console.log(item, res.list.info)
					}
				})
			},
			setTimeIndex(){
				setTimeout(()=>{
					uni.reLaunch({
						url: '/signIn/list/index'
					})
				}, 500)
			},
			handleScan() {
				uni.scanCode({
					onlyFromCamera: true,
					success: res => {
						if (res.errMsg == 'scanCode:ok') {
							console.log(res)
							console.log(res)
							if(res.path.includes('%')){
								uni.navigateTo({
									url: '/' + res.path
								})
							}else {
								let str = res.path.split('scene=')[0] + res.path.split('scene=')[1]
								console.log(str, '我是跳转地址')
								uni.navigateTo({
									url: '/' + str
								})
							}
						} else {
							uni.showToast({
								icon: 'none',
								title: '识别失败，请联系管理员!'
							})	
						}
					},
					fail() {
						console.log('取消识别')
					}
				});
			},
			// village/submit
			addView(){
				if(this.selectedAddress == '') return uni.showToast({ icon: 'none',title: '省市区不能为空' })
				if(this.formData.village.length == 0) return uni.showToast({ icon: 'none',title: '请至少添加一个串户数据' })
				if(this.formData.village[0].town == "") return uni.showToast({ icon: 'none',title: '请选择串户乡镇' })
				if(this.formData.village[0].village == "") return uni.showToast({ icon: 'none',title: '请选择串户地点' })
				console.log(this.formData)
				this.formData.data = JSON.stringify(this.formData.village)
				delete this.formData.village
				this.formData.type = 1
				this.$api.request('village/submit', this.formData, (res)=>{
					if(res.status == 'ok'){
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.setTimeIndex()
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			// village/edit
			editView(){
				if(this.selectedAddress == '') return uni.showToast({ icon: 'none',title: '省市区不能为空' })
				if(this.formData.village.length == 0) return uni.showToast({ icon: 'none',title: '请至少添加一个串户数据' })
				if(this.formData.village[0].town == "") return uni.showToast({ icon: 'none',title: '请选择串户乡镇' })
				if(this.formData.village[0].village == "") return uni.showToast({ icon: 'none',title: '请选择串户地点' })
				this.formData.data = JSON.stringify(this.formData.village)
				delete this.formData.village
				this.$api.request('village/edit', this.formData, (res)=>{
					if(res.status == 'ok'){
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.setTimeIndex()
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			
			showStreetPickerStatus(e, i){
				const index = e.detail.value
				this.formData.village[i].town_code = this.townList[index].code
				this.formData.village[i].town = this.townList[index].name
				this.formData.village[i].indexTwon = index
				this.getCz(this.townList[index].code, i)
				
			},
			getVillageName(e, i){
				const index = e.detail.value
				this.formData.village[i].village_code = this.formData.village[i].villageArr[index].code
				this.formData.village[i].village = this.formData.village[i].villageArr[index].name
				this.formData.village[i].indexVillage = index
			},
			// 街道选择变化
			streetChange(e) {
			    if (e.columnIndex === 0) {
					const street = e.value[0]
					this.$set(this.streetColumns, 1, street.children || [])
			    }
			},
			confirmThree(e){
				const [street] = e.value
				this.formData.village[this.getCtivIndex].village = street.name
				this.formData.village[this.getCtivIndex].village_code = street.code
				this.twoShow = false
			},
			// 街道/小区确认
			confirmStreet(e) {
			    const [street] = e.value
				console.log(street.code)
			    // this.selectedStreet = `${street.name}/${community.name}`
				this.formData.village[this.getCtivIndex].town = street.name
				this.formData.village[this.getCtivIndex].town_code = street.code
				this.getCz(street.code)
			    this.showStreetPicker = false
			},
			getCz(code, i){
				this.$api.request('common/getCityList', {code: code}, (res)=>{
					if(res.status == 'ok'){
						this.formData.village[i].villageArr = res.list.info
						this.formData.village[i].village_code = ''
						this.formData.village[i].village = ''
						this.formData.village[i].indexVillage = -1
						this.formData.village[i].link_num = 0
						console.log(this.formData.village[i])
						
						// this.threeList.push(res.list.info)
						
					}
				})
			},
			formatData(list) {
			    return list.map(item => ({
			        ...item,
					children: item.children || []  // 确保 children 字段存在
			    }))
			},
			// 初始化嵌套列数据（核心修复方法）
			initNestedColumns() {
			    // 第一列
			    const provinces = this.formatData(this.rawData)
			    // 第二列（取第一个省的子级）
			    const cities = provinces[0]?.children || []
			    this.$set(this.columns, 1, this.formatData(cities))
			    // 第三列（取第一个市的子级）
			    const districts = cities[0]?.children || []
			    this.$set(this.columns, 2, this.formatData(districts))
			},
			// 联动更新逻辑（优化版）
			changeHandler(e) {
			    const { columnIndex, values } = e
			    // 省份变化时更新市、区
			    if (columnIndex === 0) {
			        const cities = values[0]?.children || []
			        this.$set(this.columns, 1, this.formatData(cities))
			        const districts = cities[0]?.children || []
			        this.$set(this.columns, 2, this.formatData(districts))
			        // 更新默认选中索引
			        this.defaultIndex = [e.indexs[0], 0, 0]
			    }
			    // 市变化时更新区
			    if (columnIndex === 1) {
			        const districts = values[1]?.children || []
			        this.$set(this.columns, 2, this.formatData(districts))
			        this.defaultIndex = [this.defaultIndex[0], e.indexs[1], 0]
			    }
				console.log(this.defaultIndex)
			},
			
			// 确认选择
			confirmHandler(e) {
				
				const [province, city, district] = e.value;
				this.selectedAddress = `${province.name}-${city.name}-${district.name}`;
				this.showPicker = false;
				this.formData.area_code = province.code
				this.formData.province = province.name
				this.formData.city = city.name
				this.formData.area = district.name
				this.formData.village = []
				//获取完整编码信息
				console.log({provinceCode: province.code, cityCode: city.code, districtCode: district.code});
				this.getParent(district.code)
			},
			getCity(){
				this.$api.request('common/getNewProcitydisList', {}, (res)=>{
					if(res.status == 'ok'){
						this.rawData = res.list;
						// 初始化第一列（省份）
						this.$set(this.columns, 0, this.formatData(this.rawData))
						this.initNestedColumns()
					}
				})
			},
			// common/getCityList
			getParent(code){
				this.$api.request('common/getCityList', {code: code}, (res)=>{
					if(res.status == 'ok'){
						console.log(res.list.info)
						this.townList = res.list.info
					}
				})
			},
			beforeUpload(file) {
				const token = uni.getStorageSync('token');
				uni.uploadFile({
					url: this.action,
					filePath: file[file.length - 1].url, // 必须使用本地文件路径（file.path）
					name: 'file', // 与后端接口字段一致
					formData: {
						dir: 'profile',
						type: 'image',
					},
					header: {
						'token': token,
						'Content-Type': 'application/json'
					},
					success: (res) => {
						if (res.statusCode === 200) {
							const response = JSON.parse(res.data);
							this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].onLine = response.url
							this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].postLine = response.path
							console.log(this.$refs.imageUpload)
						}
					},
					fail(err) {
						console.log(err)
					}
				});
				return false; // 阻止组件默认上传
			},
			onSortList(files) {
				this.fileList = files; // 直接赋值新数组
			},
			getActivity(){
				this.$api.request('activity/listing', {}, (res)=>{
					if(res.status == 'ok'){
						this.channelSourceArr = res.list
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
    .textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx;
				line-height: 1.5;
				text-align: left;
				background: #f8f8f8;
				border-radius: 12rpx;
	}
	.villageInfo{
		width: calc(100% - 32rpx);
		padding: 10rpx 0 0;
		// background: red;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		.villageLeft{
			width: calc(100% - 140rpx);
			.item_list{
				display: flex;
				justify-content: space-between;
			}
		}
		.villageMin{
			text-align: center;
			width: 30rpx;
			font-size: 40rpx;
			// padding: 10rpx;
			color: #01BEFF;
			border-radius: 50%;
		}
		.villageRight{
			text-align: center;
			width: 50rpx;
			font-size: 40rpx;
			// padding: 10rpx;
			color: red;
			border-radius: 50%;
		}
	}
	.buton{
		width: 400rpx;
		height: 70rpx;
		text-align: center;
		margin: 80rpx auto 0;
		line-height: 70rpx;
		border-radius: 40rpx;
		color: #fff;
		background: #01BEFF;
		margin-bottom: 50rpx;
	}
	.app{
		width: 100%;
		.title{
			width: 100%;
			text-align: center;
			font-size: 32rpx;
			font-weight: 550;
			padding: 30rpx 0;
		}
		.input_app{
			width: calc(100% - 64rpx);
			background: #fff;
			padding: 20rpx 0;
			border-radius: 10rpx;
			margin: 0 auto;
			.lable{
				// text-align: center;
				min-width: 170rpx;
			}
			.input_info{
				width: calc(100% - 40rpx);
				margin: 0 auto;
				padding: 10rpx;
				// background-color: plum;
			}
			.item_list{
				display: flex;
				margin-top: 35rpx;
				justify-content: space-between;
			}
		}
	}
</style>