<template>
	<view class="template-login">
		<view class="login">
			<!-- 顶部背景图片-->
			<view class="login__bg login__bg--top">
				<image class="bg" src="https://resource.tuniaokj.com/images/login/2/login-top2.png" mode="widthFix">
				</image>
			</view>

			<view class="login__wrapper">
				<view class="tn-margin-left tn-margin-right tn-text-bold" style="font-size: 60rpx;">
					欢迎回来
				</view>
				<!-- 输入框内容-->
				<view class="login__info tn-flex tn-flex-direction-column tn-flex-col-center tn-flex-row-center">
					<!-- 登录 -->
					<block v-if="currentModeIndex === 0">
						<!-- <view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
							<view class="login__info__item__input__left-icon">
								<view class="tn-icon-my"></view>
							</view>
							<view class="login__info__item__input__content">
								<input maxlength="20" placeholder-class="input-placeholder" placeholder="请输入您的姓名" placeholder-style="color:#AAAAAA" v-model="real_name" />
							</view>
						</view>
						<view class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
							<view class="login__info__item__input__left-icon">
								<view class="tn-icon-identity"></view>
							</view>
							<view class="login__info__item__input__content">
								<picker @change="(e) => bindPickerChange(e, 'shenfen')" :value="indexIncome_source" :range="income_sourceArr" range-key="name">
								       <view class="xing" style="color: #AAA;" v-if="indexIncome_source === -1">
											请选择您的身份
											<text class="tn-icon-right tn-padding-left-xs"></text>
								       </view>
								       <view class="xing" style="color: #000;" v-else>
											{{income_sourceArr[indexIncome_source].name}}
											<text class="tn-icon-right tn-padding-left-xs"></text>
								       </view>
								</picker>
							</view>
						</view> 
						<view v-if="income_sourceArr[indexIncome_source].name == '员工'"
							class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
							<view class="login__info__item__input__left-icon">
								<view class="tn-icon-shop"></view>
							</view>
							<view class="login__info__item__input__content">
								<picker @change="(e) => bindPickerChange(e, 'mendian')" :value="indexShop" :range="shopList" range-key="name">
									<view class="xing" style="color: #AAA;" v-if="indexShop === -1">
										请选择您的门店
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								    <view class="xing" style="color: #000;" v-else>
								        {{shopList[indexShop].name}}
								        <text class="tn-icon-right tn-padding-left-xs"></text>
								    </view>
								</picker>
							</view>
						</view>
						<view v-if="income_sourceArr[indexIncome_source].name == '员工'"
							class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
							<view class="login__info__item__input__left-icon">
								<view class="tn-icon-all"></view>
							</view>
							<view class="login__info__item__input__content">
								<picker @change="(e) => bindPickerChange(e, 'zhiwei')" :value="indexDet" :range="detList" range-key="name">
								    <view class="xing" style="color: #AAA;" v-if="indexDet === -1">
								        请选择您的部门
								        <text class="tn-icon-right tn-padding-left-xs"></text>
								    </view>
								    <view class="xing" style="color: #000;" v-else>
								        {{detList[indexDet].name}}
								        <text class="tn-icon-right tn-padding-left-xs"></text>
								    </view>
								</picker>
							</view>
						</view> -->
						<view
							class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
							<view class="login__info__item__input__left-icon">
								<view class="tn-icon-phone"></view>
							</view>
							<view class="login__info__item__input__content">
								<input maxlength="20" placeholder-class="input-placeholder" placeholder="请输入手机号"
									v-model="phone" />
							</view>
						</view>

						<view
							class="login__info__item__input tn-flex tn-flex-direction-row tn-flex-nowrap tn-flex-col-center tn-flex-row-left">
							<view class="login__info__item__input__left-icon">
								<view class="tn-icon-safe"></view>
							</view>
							<view
								class="login__info__item__input__content login__info__item__input__content--verify-code">
								<input placeholder-class="input-placeholder" placeholder="请输入验证码" v-model="verCode" />
							</view>
							<view class="login__info__item__input__right-verify-code" @tap.stop="getCode">
								<tn-button backgroundColor="#9ebeff" fontColor="#FFFFFF" size="sm" padding="5rpx 10rpx"
									width="100%" shape="round">{{ tips }}</tn-button>
							</view>
						</view>
					</block>

					<view class="tn-protocol">
						<radio :checked="protocol" color="#9ebeff" style="transform:scale(0.7)"
							@click="protocol=!protocol">
						</radio>
						<view @click="protocol=!protocol">授权登录代表同意</view>《<text class="tn-color-blue tn-text-bold"
							@click="goto('/pages/index/protocol')">用户协议</text>》《<text class="tn-color-blue tn-text-bold"
							@click="goto('/pages/index/privacy')">隐私协议</text>》
					</view>

					<view class="login__info__item__button tn-color-white bg1" hover-class="tn-hover"
						:hover-stay-time="150" @click="getPhoneLogin">{{ currentModeIndex === 0 ? '登录' : '注册'}}</view>


					<view v-if="currentModeIndex === 1" :class="[{'login__info__item__tips': currentModeIndex === 0}]">
						<view class="tn-flex tn-flex-row-between tn-padding">
							<view class="" @tap.stop="modeSwitch(0)">前往登录</view>
						</view>
					</view>

				</view>

			</view>

			<!-- 底部背景图片-->
			<view class="login__bg login__bg--bottom">
				<image src="https://resource.tuniaokj.com/images/login/2/login-bottom2.png" mode="widthFix"></image>
			</view>

		</view>
		<view class="bd_phone" v-if="improveStatus">
			<view class="boxs">
				<view class="titles">
					完善个人信息
				</view>
				<view class="phones">
					<input class="inputs" placeholder="请选择输入您的姓名" name="input" placeholder-style="color:#AAAAAA" v-model="real_name"></input>
				</view>
				<view class="news1">
					<picker @change="(e) => bindPickerChange(e, 'shenfen')" :value="indexIncome_source" :range="income_sourceArr" range-key="name">
						<view class="xing" style="color: #AAA;" v-if="indexIncome_source === -1">
							请选择您的身份
		
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="xing" style="color: #000;" v-else>
							{{income_sourceArr[indexIncome_source].name}}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</picker>
				</view>
				<view class="news1" v-if="income_sourceArr[indexIncome_source].name == '员工'">
					<picker @change="(e) => bindPickerChange(e, 'mendian')" :value="indexShop" :range="shopList"
						range-key="name">
						<view class="xing" style="color: #AAA;" v-if="indexShop === -1">
							请选择您的门店
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="xing" style="color: #000;" v-else>
							{{shopList[indexShop].name}}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</picker>
				</view>
				<view class="news1" v-if="income_sourceArr[indexIncome_source].name == '员工'">
					<picker @change="(e) => bindPickerChange(e, 'zhiwei')" :value="indexDet" :range="detList"
						range-key="name">
						<view class="xing" style="color: #AAA;" v-if="indexDet === -1">
							请选择您的部门
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
						<view class="xing" style="color: #000;" v-else>
							{{detList[indexDet].name}}
							<text class="tn-icon-right tn-padding-left-xs"></text>
						</view>
					</picker>
				</view>
				<view class="sumbit" @click="queryPhone">
					确定完善
				</view>
			</view>
		</view>
		<!-- 验证码倒计时 -->
		<tn-verification-code ref="code" uniqueKey="login-demo-4" :seconds="60" @change="codeChange"></tn-verification-code>
	</view>
</template>

<script>
	export default {
		name: 'login-demo-4',
		data() {
			return {
				improveStatus: false,
				name: '',
				protocol: false, //隐私协议
				phone: '', //手机号
				verCode: '', //验证码
				// 当前选中的模式
				currentModeIndex: 0,
				// 模式选中滑块
				modeSliderStyle: {
					left: 0
				},
				real_name: '',
				// 是否显示密码
				showPassword: false,
				// 倒计时提示文字
				tips: '获取验证码',
				shopList: [],
				detList: [],
				is_staff: '',
				store_id: '',
				duty_id: '',
				indexShop: -1,
				indexDet: -1,
				indexIncome_source: -1,
				income_sourceArr: [
					{
						name: '员工',
						value: 1
					},
					{
						name: '非员工',
						value: 2
					}
				]
			}
		},
		watch: {
			currentModeIndex(value) {
				const sliderWidth = uni.upx2px(476 / 2)
				this.modeSliderStyle.left = `${sliderWidth * value}px`
			}
		},
		onLoad() {
			this.getListInput()
		},
		methods: {
			queryPhone() {
				let that = this
				let obj = {
					is_staff: this.is_staff,
					store_id: this.store_id,
					duty_id: this.duty_id,
					real_name: this.real_name
				}
				if(this.real_name == ''){
					uni.showToast({
						icon: 'none',
						title: "请输入您的姓名"
					})
					return
				}
				this.$api.request('wxuser/setUserPhone', obj, (res) => {
					if (res.status == 'ok') {
						// 存储用户信息
						// uni.setStorageSync('userInfo', res.user)
						let objs = uni.getStorageSync('userInfo')
						objs.real_name = this.real_name
						uni.setStorageSync('userInfo', objs)
						uni.reLaunch({
							url: '/pages/index/index'
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
						this.improveStatus = true
					}
				})
			},
			bindPickerChange(e, type){
				const index = e.detail.value
				if(type == 'shenfen'){
					this.indexIncome_source = index
					this.is_staff = this.income_sourceArr[index].value
					this.indexShop = -1
					this.indexDet = -1
					this.store_id = ''
					this.duty_id = ''
				}
				if(type == 'mendian'){
					console.log(e)
					this.indexShop = index
					this.store_id = this.shopList[index].id
				}
				if(type == 'zhiwei'){
					console.log(e)
					this.indexDet = index
					this.duty_id = this.detList[index].id
				}
			},
			getListInput(){
				this.$api.request('common/getShopList', {}, (res)=>{
					this.shopList = res.list
					console.log(this.shopList, 'shop')
				})
				this.$api.request('common/getDeptList', {}, (res)=>{
					this.detList = res.list
					console.log(this.detList, 'det')	
				})
			},
			//登录
			getPhoneLogin() {
				let that = this	
				if (!this.phone || !this.verCode) {
					this.$api.toast('请输入手机号或验证码 !')
					return
				}
				if (!this.protocol) {
					this.$api.toast('请先勾选协议 !')
					return
				}
				if (!this.$api.isMobile(this.phone)) {
					this.$api.toast('手机号格式输入错误 !')
					return
				}
				let data = {
					phone: that.phone,
					code: that.verCode,
					is_staff: that.is_staff,
					store_id: that.store_id,
					duty_id: that.duty_id,
					real_name: that.real_name
				}
				this.$api.request('wxuser/wxPhoneLogin', data, (res)=>{
					if(res.status != 'ok'){
						uni.showToast({ icon: 'none', title: res.info })
					} else{
						if(res.user.real_name == ""){
							uni.setStorageSync('token', res.token)
							// 存储用户信息
							res.user.is_phone_bind = 1
							uni.setStorageSync('userInfo', res.user)
							this.improveStatus = true
						} else{
							uni.setStorageSync('token', res.token)
							// 存储用户信息
							res.user.is_phone_bind = 1
							uni.setStorageSync('userInfo', res.user)
							uni.reLaunch({
								url: '/pages/index/index'
							})
						}
						
					}
				})
			},
			// 切换模式
			modeSwitch(index) {
				this.currentModeIndex = index
				this.showPassword = false
			},
			// 获取验证码
			getCode() {
				
				let that = this;
				if (!that.phone) return this.$api.toast('请输入手机号 !')
				if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(that.phone)) return this.$api.toast('手机号格式输入错误 !')
				let obj = {
					phone: that.phone,
					type: 1
				}
				if (that.$refs.code.canGetCode) {
					this.$api.request('common/sms', obj, (res)=>{
						// that.$api.toast(data.info);
						that.btnDisabled = false;
						that.$tn.message.closeLoading()
						that.$tn.message.toast('验证码已经发送')
						that.$refs.code.start()
					})
				} else {
					this.$tn.message.toast(this.$refs.code.secNum + '秒后再重试')
				}
			},
			// 获取验证码倒计时被修改
			codeChange(event) {
				this.tips = event
			},
			//跳转页面
			goto(url) {
				uni.navigateTo({
					url: url
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.tn-protocol {
		display: flex;
		justify-content: center;
		align-items: center;
		margin: 40rpx auto 20rpx auto;
		height: 50rpx;
	}

	.login {
		position: relative;
		height: 100%;
		z-index: 1;

		/* 背景图片 start */
		&__bg {
			z-index: -1;
			position: fixed;

			&--top {
				top: 0;
				left: 0;
				right: 0;
				width: 100%;

				.bg {
					width: 750rpx;
					will-change: transform;
				}
			}

			&--bottom {
				bottom: -10rpx;
				left: 0;
				right: 0;
				width: 100%;
				// height: 144px;
				margin-bottom: env(safe-area-inset-bottom);

				image {
					width: 750rpx;
					will-change: transform;
				}
			}
		}

		/* 背景图片 end */

		/* 内容 start */
		&__wrapper {
			margin-top: 150rpx;
			width: 100%;
		}

		/* 切换 start */
		&__mode {
			position: relative;
			margin: 0 auto;
			width: 476rpx;
			height: 77rpx;
			margin-top: 100rpx;
			background-color: rgba(255, 255, 255, 0.6);
			box-shadow: 0rpx 10rpx 50rpx 0rpx rgba(0, 3, 72, 0.1);
			border-radius: 39rpx;

			&__item {
				height: 77rpx;
				width: 100%;
				line-height: 77rpx;
				text-align: center;
				font-size: 31rpx;
				color: #080808;
				letter-spacing: 1em;
				text-indent: 1em;
				z-index: 2;
				transition: all 0.4s;

				&--active {
					font-weight: bold;
					color: #FFFFFF;
				}
			}

			&__slider {
				position: absolute;
				height: inherit;
				width: calc(476rpx / 2);
				border-radius: inherit;
				box-shadow: 0rpx 18rpx 72rpx 18rpx rgba(0, 195, 255, 0.1);
				z-index: 1;
				transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
			}
		}

		/* 切换 end */

		/* 登录注册信息 start */
		&__info {
			margin: 0rpx 30rpx 10rpx 30rpx;
			padding-bottom: 0;
			border-radius: 20rpx;

			&__item {

				&__input {
					margin-top: 20rpx;
					width: 100%;
					height: 77rpx;
					border: 1rpx solid #E6E6E6;
					border-radius: 39rpx;

					&__left-icon {
						width: 10%;
						font-size: 44rpx;
						margin-left: 20rpx;
						color: #838383;
					}

					&__content {
						width: 80%;
						padding-left: 10rpx;

						&--verify-code {
							width: 56%;
						}

						input {
							font-size: 24rpx;
							// letter-spacing: 0.1em;
						}
					}

					&__right-icon {
						width: 10%;
						font-size: 44rpx;
						margin-right: 20rpx;
						color: #838383;
					}

					&__right-verify-code {
						width: 34%;
						margin-right: 20rpx;
					}
				}

				&__button {
					margin-top: 30rpx;
					margin-bottom: 39rpx;
					width: 100%;
					height: 77rpx;
					text-align: center;
					font-size: 31rpx;
					font-weight: bold;
					line-height: 77rpx;
					letter-spacing: 1em;
					text-indent: 1em;
					border-radius: 39rpx;
					box-shadow: 1rpx 10rpx 24rpx 0rpx rgba(60, 129, 254, 0.35);
				}

				&__tips {
					margin: 30rpx 0;
					color: #AAAAAA;
				}
			}
		}

		/* 登录注册信息 end */

		/* 登录方式切换 start */
		&__way {
			margin: 0 auto;
			margin-top: 110rpx;

			&__item {
				&--icon {
					width: 85rpx;
					height: 85rpx;
					font-size: 80rpx;
					margin-bottom: 18rpx;
					position: relative;
					z-index: 1;
				}
			}
		}
	}
	.bd_phone {
		width: 100%;
		height: 100vh;
		position: fixed;
		z-index: 999;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, 0.3);
	
		.boxs {
			width: 90%;
			padding: 40rpx;
			background: #fff;
			border-radius: 20rpx;
			overflow: hidden;
			margin: 180rpx auto 0;
	
			.titles {
				font-weight: 550;
				text-align: center;
				margin-top: 30rpx;
			}
	
			.phones {
				width: 90%;
				margin: 50rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #AAA;
	
				.inputs {
					width: 90%;
					margin-left: 5%;
					height: 70rpx;
	
				}
			}
	
			.news1 {
				width: 90%;
				margin: 16rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #AAA;
	
				.xing {
					width: 90%;
					margin-left: 5%;
					height: 70rpx;
					line-height: 70rpx;
				}
	
				.inputs {
					width: 90%;
					margin-left: 5%;
					height: 70rpx;
	
				}
			}
	
			.yzms {
				width: 90%;
				margin: 16rpx auto 0;
				height: 70rpx;
				border-radius: 35rpx;
				border: 1rpx solid #AAA;
				display: flex;
				align-items: center;
	
				.inputs {
					width: 60%;
					margin-left: 5%;
					height: 70rpx;
	
				}
	
				.scode {
					width: 35%;
					color: #AAA;
					text-align: center;
					line-height: 68rpx;
					font-size: 26rpx;
					height: 68rpx;
					border-left: 1rpx solid #AAA;
					box-sizing: border-box;
				}
			}
	
			.sumbit {
				width: 90%;
				margin: 40rpx auto;
				height: 70rpx;
				border-radius: 35rpx;
				color: #fff;
				background: skyblue;
				text-align: center;
				line-height: 70rpx;
			}
		}
	}
	
</style>