<template>
	<view class="detail-page">
		<!-- 选项卡 -->
		<view class="tab-bar">
			<view class="tab-item" v-for="(tab, index) in tabList" :key="index"
				:class="{ active: activeTab === tab.name }" @click="activeTab = tab.name">
				{{ tab.label }}
			</view>
		</view>

		<!-- 内容区域 -->
		<view :class="['content-box', activeTab]">
			<view v-if="activeTab === 'baseInfo'" class="base-info">
				<block v-for="(section, sIndex) in infoSections" :key="sIndex">
					<view class="info-section"
						:style="{ borderLeftColor: section.color, backgroundColor: section.bgColor }">
						<view class="section-header">
							<view class="icon-wrapper" :style="{ backgroundColor: section.color }">
								<uni-icons :type="section.icon" size="28" color="#fff"></uni-icons>
							</view>
							<text class="section-title">{{ section.title }}</text>
						</view>
						<view class="field-group">
							<view class="field-item" v-for="field in section.fields" :key="field">
								<text class="label">{{ fieldMap[field] }}</text>
								<text class="value" v-if="fieldMap[field] != '草图：'">{{ baseInfo[field] || '-' }}</text>
								<view class="value" v-else>
									<image v-for="(item, index) in baseInfo[field]" :key="index" :src="item"
										style="width: 100rpx;height: 100rpx;border-radius: 10rpx;margin-right: 10rpx;"
										mode=""></image>
								</view>
							</view>
						</view>
					</view>
				</block>
			</view>

			<!-- 跟进信息 & 日志信息 -->
			<view v-else class="list-box">
				<view class="list-item" v-for="(item, index) in activeList" :key="index">
					<view class="item-header">
						<uni-icons :type="activeTab === 'followInfo' ? 'time' : 'list'" size="24" color="#999"></uni-icons>
						<view class="item-info" v-if="activeTab === 'followInfo'">
							<view class="info-grid-items" style="margin-top: 0;">
								<text class="info-label">跟进时间：</text>
								<text class="info-value">{{ item.create_time }}</text>
							</view>
							<view class="info-grid-items" style="margin-top: 15rpx;" v-if="item.next_follow_time">
								<text class="info-label">下次跟进时间：</text>
								<text class="info-value">{{ item.next_follow_time }}</text>
							</view>
							<view class="info-grid">
								<view class="info-grid-item">
									<text class="info-label">跟进人：</text>
									<text class="info-value">{{ item.userName }}</text>
								</view>
								<view class="info-grid-item">
									<text class="info-label">跟进方式：</text>
									<text class="info-value">{{ item.isWayName ? item.isWayName : '--' }}</text>
								</view>
								<view class="info-grid-item">
									<text class="info-label">跟进状态：</text>
									<text class="info-value">{{ item.isStateName ? item.isStateName : '--' }}</text>
								</view>
								<view class="info-grid-item">
									<text class="info-label">见面场所：</text>
									<text class="info-value">{{ item.storeName ? item.storeName : '--' }}</text>
								</view>
							</view>
							<view class="info-grid-img" v-if="item.followImg.length">
								<view class="info-values">跟进图片：</view>
								<view style="width: 100%;height: 80rpx;display: flex;gap: 10rpx;flex-wrap: wrap;margin-top: 10rpx;">
									<view style="width: 80rpx;height: 80rpx;background: plum;border-radius: 10rpx;overflow: hidden;" v-for="(v, i) in item.followImg" :key="i">
										<image style="width: 100%;height: 100%;" :src="v.name_path" mode="" @tap="previewMedia(item.followImg, i)"></image>
									</view>
								</view>
							</view>
							<view class="info-grid-items" v-if="item.content">
								<text class="info-values">跟进内容： {{ item.content }}</text>
							</view>
						</view>
						<view class="" v-else>
							{{ item.userName }} 在 {{ item.create_time }} 进行了 {{ item.desc }}<text v-if="item.crmClueName">，线索名称是{{ item.crmClueName }}。</text>
						</view>
					</view>
				</view>
			</view>


		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				activeTab: 'baseInfo',
				tabList: [{
						name: 'baseInfo',
						label: '基础信息'
					},
					{
						name: 'followInfo',
						label: '跟进信息'
					},
					{
						name: 'logInfo',
						label: '日志信息'
					}
				],
				fieldMap: {
					id: 'ID：',
					clue_name: '线索名称：',
					create_time: '创建时间',
					update_time: '更新日期',
					phone: '联系电话：',
					province: '省份：',
					follow_state: '客户状态',
					followup_num: '跟进次数',
					meet_num: '见面次数',
					tel_num: '电话次数',
					is_connect: '是否接通',
					is_meet_tel: '是否是见面',
					followup_time: '最新跟进时间',
					advance_store_state: '进店状态',
					next_followup_time: '下次跟进日期',
					deal_state: '成交状态',
					picture_state: '设计出图状态',
					city: '城市：',
					area: '区域：',
					address: '详细地址：',
					channel_source: '渠道来源：',
					two_channel_source: '二级渠道来源：',
					customer_name: '客户姓名：',
					mobile1: '备用电话1：',
					mobile2: '备用电话2：',
					sex: '性别：',
					birthday: '出生日期：',
					id_card: '身份证号：',
					id_card_front: '身份证正面：',
					id_card_back: '身份证背面：',
					intention: '建房意向：',
					storey: '层数：',
					structural_style: '建筑结构：',
					construction: '建筑类型：',
					homestead_extent: '宅基地面积：',
					building_region: '建房区域：',
					building_budget: '建房预算：',
					building_style: '建筑风格：',
					approval_status: '审批状态：',
					is_dispute: '是否存在纠纷：',
					income_source: '收入来源：',
					preference: '兴趣爱好：',
					population_numbe: '家庭人口数量：',
					is_spouse: '有无配偶：',
					family_structure: '家庭结构：',
					bedroom_numbe: '卧室数量：',
					parlor_numbe: '客厅数量：',
					study_numbe: '书房数量：',
					toilet_numbe: '卫生间数量：',
					gym_numbe: '健身房数量：',
					storage_numbe: '储物间数量：',
					cloakroom: '衣帽间数量：',
					sketch: '草图：',
					remarks: '备注：'
				},
				infoSections: [{
						icon: 'info',
						title: '基本信息',
						color: '#007aff',
						bgColor: 'rgba(0, 122, 255, 0.03)',
						fields: ['clue_name', 'phone', 'area', 'address', 'channel_source', 'two_channel_source']
					},
					{
						icon: 'person',
						title: '家庭信息',
						color: '#34C759',
						bgColor: 'rgba(52, 199, 89, 0.03)',
						fields: ['customer_name', 'mobile1', 'mobile2', 'sex', 'id_card', 'population_numbe',
							'is_spouse', 'family_structure', 'income_source', 'preference'
						]
					},
					{
						icon: 'home',
						title: '建房需求',
						color: '#FF9500',
						bgColor: 'rgba(255, 149, 0, 0.03)',
						fields: ['intention', 'storey', 'structural_style', 'construction', 'homestead_extent',
							'building_region', 'building_budget', 'building_style', 'approval_status',
							'is_dispute', 'bedroom_numbe', 'parlor_numbe', 'study_numbe', 'toilet_numbe',
							'gym_numbe', 'storage_numbe', 'cloakroom', 'sketch'
						]
					},
					{
						icon: 'other',
						title: '其他信息',
						color: '#cccccc',
						bgColor: 'rgba(200, 200, 200, 0.03)',
						fields: ['remarks', 'create_time', 'update_time', 'followup_time', 'next_followup_time', 'followup_num',
							'meet_num', 'tel_num', 'is_connect', 'is_meet_tel', 'advance_store_state',
							'deal_state', 'picture_state', 'follow_state'
						]
					}
				],
				baseInfo: {},
				isLoading: false, // 新增加载状态
				followList: [],
				logList: [],

			};
		},
		onLoad(options) {
			const id = options.id; // 获取 ID 值
			this.fetchData(id);
			this.fetchFollowInfo(id); // 新增跟进信息请求
			this.fetchLogInfo(id); // 新增日志信息请求
		},
		computed: {
			activeList() {
				return this.activeTab === 'followInfo' ? this.followList : this.logList;
			}
		},
		methods: {
			previewMedia(list, index) {
				let arr = [] 
				list.map(item => { arr.push(item.name_path) })
				uni.previewImage({
					current: index,
					urls: arr
				})
				
			},
			rgbaColor(hex, opacity) {
				const r = parseInt(hex.slice(1, 3), 16);
				const g = parseInt(hex.slice(3, 5), 16);
				const b = parseInt(hex.slice(5, 7), 16);
				return `rgba(${r}, ${g}, ${b}, ${opacity})`;
			},
			async fetchData(id) {
				this.$api.request('customer/detail', {
					id: id
				}, (res) => {
					console.log(res)
					if (res.status == 'ok') {
						this.baseInfo = {};
						Object.keys(this.fieldMap).forEach(key => {
							this.baseInfo[key] = res.detail[key] || '-';
							this.baseInfo.channel_source = res.detail.channel.split('->')[0]
							this.baseInfo.two_channel_source = res.detail.channel.split('->')[1]
						});
						this.baseInfo.family_structure = res.data.family_structure
						this.baseInfo.population_numbe = res.data.population_numbe
						this.baseInfo.population_numbe = res.data.population_numbe
						this.baseInfo.is_spouse = res.data.is_spouse == 1 ? '有' : '无'
						if (res.detail.sketchArr) {
							this.baseInfo.sketch = res.detail.sketchArr.split(',')
						}
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},

			async fetchFollowInfo(id) {
				// const token = "Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM";

				this.$api.request('customer/followuplog', {
					pageNo: 1,
					pageSize: 10000,
					crm_clue_id: id
				}, (res) => {
					
					if (res.status == 'ok') {
						if(res.list.length){
							this.followList = res.list.map(item => ({ ...item, statusColor: this.getStatusColor(item.status), followImg: JSON.parse(item.img)}));
							console.log(this.followList, 'this.followList')
						}
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},

			async fetchLogInfo(id) {
				// const token = "Guyp2juWb5Mg1iBJiKcmAJ27Vp4h4DEKbp38Yu7tkXOZeQsdNtH+7TbjiPtdn9Uqh/xjOQlpiNgTrFzUpey6ZDwBm/El+Jw9jY2AvIydeZvVwKi4/ULtzvjTuDCsSP0j6YVitm+If1yh7/8KmpCT9id/4eCZmQ3CHtJcHVsF3T/yUJocCmGqeV1x4FWhLq4l0slY08nN0+WMFFpjXVpQ9BuX/DM";
				this.$api.request('customer/logall', {
					pageNo: 1,
					pageSize: 100000,
					crm_clue_id: id
				}, (res) => {
					console.log(res)
					if (res.status == 'ok') {
						if(res.list.length){
							// console.log(res[1].list);
							this.logList = res.list.map(item => ({
								...item,
								operator: item.operator || '系统自动'
							}));
						}
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			getStatusColor(status) {
				const statusMap = {
					'待跟进': '#FF9500',
					'已成交': '#34C759',
					'已放弃': '#FF4545'
				};
				return statusMap[status] || '#666';
			}


		}
	};
</script>

<style scoped lang="scss">
	.item-info {
		margin-bottom: 16rpx;
	}

	.info-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 15rpx 30rpx;
		margin-top: 15rpx;
	}

	.info-grid-item {
		display: flex;
		align-items: center;
		font-size: 26rpx;
	}
	.info-grid-img{
		width: 100%;
		align-items: center;
		font-size: 26rpx;
		margin-top: 15rpx;
	}
	.info-grid-items {
		display: flex;
		width: 100%;
		align-items: center;
		font-size: 26rpx;
		margin-top: 15rpx;

		.info-label {
			color: #999;
			min-width: 100rpx;
			flex-shrink: 0;
		}

		.info-value {
			color: #666;
			flex: 1;
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.info-values {
			color: #666;
			width: 100%;
		}
	}

	.info-label {
		color: #999;
		min-width: 100rpx;
		flex-shrink: 0;
	}

	.info-values {
		color: #666;
		width: 100%;
	}

	.info-value {
		color: #666;
		flex: 1;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.detail-page {
		background-color: #f8f8f8;
		min-height: 100vh;

		.tab-bar {
			display: flex;
			background: #fff;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.06);

			.tab-item {
				flex: 1;
				text-align: center;
				padding: 28rpx 0;
				font-size: 30rpx;
				color: #666;
				transition: all 0.2s;
				position: relative;

				&.active {
					color: #007aff;
					font-weight: 500;

					&::after {
						content: '';
						position: absolute;
						bottom: 0;
						left: 50%;
						transform: translateX(-50%);
						width: 80rpx;
						height: 4rpx;
						background: #007aff;
						border-radius: 2rpx;
					}
				}
			}
		}

		.content-box {
			background: white;
			border-radius: 16rpx;
			margin: 24rpx;
			padding: 20rpx 32rpx;
			box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);

			.base-info {
				.info-section {
					margin-bottom: 32rpx;
					padding: 32rpx;
					border-radius: 16rpx;
					position: relative;
					border-left: 8rpx solid;
					box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);

					&:last-child {
						margin-bottom: 0;
					}

					.section-header {
						display: flex;
						align-items: center;
						margin-bottom: 32rpx;

						.icon-wrapper {
							width: 56rpx;
							height: 56rpx;
							border-radius: 16rpx;
							display: flex;
							align-items: center;
							justify-content: center;
						}

						.section-title {
							font-size: 34rpx;
							color: #333;
							margin-left: 20rpx;
							font-weight: 600;
						}
					}

					.field-group {
						.field-item {
							display: flex;
							justify-content: space-between;
							align-items: flex-start;
							margin-bottom: 4rpx;
							padding: 16rpx 0;

							.label {
								color: #666;
								font-size: 28rpx;
								flex-shrink: 0;
								width: 200rpx;
							}

							.value {
								color: #333;
								font-size: 28rpx;
								flex: 1;
								word-break: break-all;
								text-align: right;
								line-height: 1.6;
							}
						}
					}
				}
			}

			.list-box {
				.list-item {
					// padding: 32rpx 0 0 0;
					// border-bottom: 1rpx solid #f0f0f0;

					&:last-child {
						border-bottom: none;
					}

					.item-header {
						display: flex;
						align-items: center;
						margin-bottom: 16rpx;

						.date {
							color: #999;
							font-size: 26rpx;
							margin-left: 16rpx;
						}

						.status,
						.operator {
							margin-left: auto;
							font-size: 26rpx;
							padding: 6rpx 20rpx;
							border-radius: 8rpx;
						}

						.status {
							color: #007aff;
						}

						.operator {
							color: #666;
						}
					}

					.content {
						font-size: 28rpx;
						color: #333;
						line-height: 1.6;
						padding-left: 40rpx;
					}
				}
			}
		}
	}
</style>