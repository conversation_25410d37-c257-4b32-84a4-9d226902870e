export default {
	/**
	 * @description 保留当前页面，跳转到应用内的某个页面
	 * @param {string} url 【页面路径】针对文件夹、文件同名的url，只需传文件名即可，由该方法拼接补全，非此类型url需传完整路径
	 * @param {object} params 【页面参数】
	 * @param {object} extendParams 【额外参数】
	 */
	navigateTo(url, params, extendParams) {
		let args = {
			url: url.indexOf('pages/') !== -1 ? url : `/pages/${url}/${url}`,
			animationDuration: 150
		}
		if (typeof params === 'object') {
			args.url += uni.$u.queryParams(params)
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		uni.navigateTo(args)
	},

	/**
	 * @description 关闭当前页面，跳转到应用内的某个页面
	 * @param {string} url 【页面路径】针对文件夹、文件同名的url，只需传文件名即可，由该方法拼接补全，非此类型url需传完整路径
	 * @param {object} params 【页面参数】
	 * @param {object} extendParams 【额外参数】
	 */
	redirectTo(url, params, extendParams) {
		let args = {
			url: url.indexOf('pages/') !== -1 ? url : `/pages/${url}/${url}`,
			animationDuration: 150
		}
		if (typeof params === 'object') {
			args.url += uni.$u.queryParams(params)
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		uni.redirectTo(args)
	},

	/**
	 * @description 关闭所有页面，打开到应用内的某个页面。
	 * @param {string} url 【页面路径】针对文件夹、文件同名的url，只需传文件名即可，由该方法拼接补全，非此类型url需传完整路径
	 * @param {object} params 【页面参数】
	 * @param {object} extendParams 【额外参数】
	 */
	reLaunch(url, params, extendParams) {
		let args = {
			url: url.indexOf('pages/') !== -1 ? url : `/pages/${url}/${url}`,
			animationDuration: 150
		}
		if (typeof params === 'object') {
			args.url += uni.$u.queryParams(params)
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		uni.reLaunch(args)
	},

	/**
	 * @description 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
	 * @param {string} url 【页面路径】
	 * @param {object} extendParams 【额外参数】
	 */
	switchTab(url, extendParams) {
		let args = {
			url: url.indexOf('pages/') !== -1 ? url : `/pages/${url}/${url}`,
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		uni.switchTab(args)
	},

	/**
	 * @description 关闭当前页面，返回上一页面或多级页面
	 * @param {string} delta 【返回的页面数】
	 * @param {object} extendParams 【额外参数】
	 */
	navigateBack(delta, extendParams) {
		let args = {
			delta: delta || 1,
			animationDuration: 150
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		uni.navigateBack(args)
	},

	/**
	 * @description 触发全局自定义事件
	 * @param {string} eventName 【事件名】
	 * @param {object} extra 【参数】
	 */
	triggerEvent(eventName, extra) {
		uni.$emit(eventName, extra || {})
	},

	/**
	 * @description 监听全局的自定义事件，只触发一次，在第一次触发之后移除监听器
	 * @param {string} eventName 【事件名】
	 */
	eventListenerCe(eventName) {
		return new Promise((resolve) => {
			uni.$once(eventName, function(data) {
				resolve(data)
			})
		})
	},

	/**
	 * @description 监听全局的自定义事件
	 * @param {string} eventName 【事件名】
	 */
	eventListener(eventName) {
		return new Promise((resolve) => {
			uni.$on(eventName, function(data) {
				resolve(data)
			})
		})
	},

	/**
	 * @description 移除全局自定义事件监听器
	 * @param {string} eventName 【事件名】
	 */
	removeEventListener(eventName) {
		uni.$off(eventName)
	},
	/**
	 * @description 将 data 存储在本地缓存中指定的 key 中，同步接口
	 * @param {string} key 【缓存中的指定的 key】
	 * @param {Any} data 【需要存储的数据】
	 */
	setStorageSync(key, data) {
		uni.setStorageSync(key, data)
	},

	/**
	 * @description 从本地缓存中获取指定 key 对应的内容，同步接口
	 * @param {string} key 【缓存中的指定的 key】
	 */
	getStorageSync(key) {
		const value = uni.getStorageSync(key)
		return value
	},

	/**
	 * @description 从本地缓存中同步移除指定 key，同步接口
	 * @param {string} key 【缓存中的指定的 key】
	 */
	removeStorageSync(key) {
		uni.removeStorageSync(key)
	},

	/**
	 * @description 清理本地所有数据缓存，谨慎使用此接口，同步接口
	 */
	clearStorageSync() {
		uni.clearStorageSync()
	},

	/**
	 * @description 显示 loading 提示框
	 * @param {string} title 【提示的内容】
	 * @param {boolean} mask 【是否显示透明蒙层】
	 */
	showLoading(title, mask) {
		uni.showLoading({
			title: title || '加载中...',
			mask: mask || false
		})
	},

	/**
	 * @description 隐藏 loading 提示框
	 */
	hideLoading() {
		uni.hideLoading()
	},

	/**
	 * @description 显示消息提示框
	 * @param {string} title 【提示的内容】
	 * @param {object} extendParams 【额外参数】
	 */
	showToast: (title, extendParams) => {
		let args = {
			title: title,
			icon: 'none',
			position: 'bottom'
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		uni.showToast(args)
	},

	/**
	 * @description 显示确认对话弹窗
	 * @param {string} content 【提示的内容】
	 * @param {object} extendParams 【额外参数】
	 */
	showAlert(content, extendParams) {
		let args = {
			content: content,
			showCancel: false,
			confirmText: '确定',
			confirmColor: '#6F87F8 '
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		return new Promise((resolve) => {
			args.success = function(res) {
				resolve(res.confirm ? 'confirm' : 'cancel')
			}
			uni.showModal(args)
		})
	},

	/**
	 * @description 显示确认对话弹窗
	 * @param {string} content 【提示的内容】
	 * @param {object} extendParams 【额外参数】
	 */
	showConfirm(content, extendParams) {
		let args = {
			content: content,
			cancelText: '取消',
			cancelColor: '#999',
			confirmText: '确定',
			confirmColor: '#6F87F8 '
		}
		if (typeof extendParams === 'object') {
			args = Object.assign(args, extendParams)
		}
		return new Promise((resolve) => {
			args.success = function(res) {
				resolve(res.confirm ? 'confirm' : 'cancel')
			}
			uni.showModal(args)
		})
	},

	/**
	 * @description 校验数据是否为空，并提示
	 * @param valueArray {array}【校验的字段数组】
	 * @param toastArray {array}【为空时提示文案】
	 * @return {boolean}
	 */
	checkDataIsNull(valueArray, toastArray) {
		let checkResult = true // 校验结果
		for (let i = 0, len = valueArray.length; i < len; i++) {
			if (!valueArray[i]) {
				// 值为空，修改输出结果，提示toast，结束校验
				checkResult = false
				this.showToast(toastArray[i])
				break
			}
		}
		return checkResult
	},

	/**
	 * @description 获取url参数
	 * @param url {str}【url】
	 */
	getQueryString(url) {
		// str为？之后的参数部分字符串
		const str = url.substr(url.indexOf('?') + 1)
		// arr每个元素都是完整的参数键值
		const arr = str.split('&')
		// result为存储参数键值的集合
		const result = {}
		for (let i = 0; i < arr.length; i++) {
			// item的两个元素分别为参数名和参数值
			const item = arr[i].split('=')
			result[item[0]] = item[1]
		}
		return result
	},

	/**
	 * @description 格式化时间
	 * @param {String|Number} dateTime 需要格式化的时间戳
	 * @param {String} fmt 格式化规则 yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合 默认yyyy-mm-dd
	 * @returns {string} 返回格式化后的字符串
	 */
	parseTime(dateTime = null, formatStr = 'yyyy-mm-dd') {
		let date
		// 若传入时间为假值，则取当前时间
		if (!dateTime) {
			date = new Date()
		} else if (/^\d{10}$/.test(dateTime?.toString().trim())) {
			// 若为unix秒时间戳，则转为毫秒时间戳（逻辑有点奇怪，但不敢改，以保证历史兼容）
			date = new Date(dateTime * 1000)
		} else if (typeof dateTime === 'string') {
			// 若用户传入字符串格式时间戳，new Date无法解析，需做兼容
			date = new Date(dateTime)
		} else {
			// 其他都认为符合 RFC 2822 规范
			// 处理平台性差异，在Safari/Webkit中，new Date仅支持/作为分割符的字符串时间
			date = new Date(typeof dateTime === 'string' ? dateTime.replace(/-/g, '/') : dateTime)
		}

		const timeSource = {
			'y': date.getFullYear().toString(), // 年
			'm': (date.getMonth() + 1).toString(), // 月
			'd': date.getDate().toString(), // 日
			'h': date.getHours().toString(), // 时
			'M': date.getMinutes().toString(), // 分
			's': date.getSeconds().toString() // 秒
			// 有其他格式化字符需求可以继续添加，必须转化成字符串
		}

		for (const key in timeSource) {
			const [ret] = new RegExp(`${key}+`).exec(formatStr) || []
			if (ret) {
				if (key === 'y') {
					const beginIndex = ret.length === 2 ? 2 : 0
					formatStr = formatStr.replace(ret, timeSource[key].slice(beginIndex))
				} else {
					formatStr = formatStr.replace(ret, timeSource[key].padStart(ret.length, '0'))
				}
			}
		}

		return formatStr
	}
}
