<template>
	<view class="container">
		<view class="info-box">
			<view class="title">资料修改</view>
			<view class="form-item form-input">
				<view class="label">客户姓名</view>
				<input class="input"  v-model="infoData.flow_custom_name" type="text" maxlength="64" placeholder="请输入" />
			</view>
			<view class="form-item form-input">
				<view class="label">客户地址</view>
				<input class="input" v-model="infoData.address" type="text" maxlength="64" placeholder="请输入" />
			</view>
			<view class="form-item form-input">
				<view class="label">联系手机</view>
				<input class="input" v-model="infoData.phone" type="text" maxlength="64" placeholder="请输入" />
			</view>
			<!-- 			<view class="form-item form-select" @tap="selectRadio('key', '选择的标题')">
				<view class="label">label</view>
				<view class="text line-1" :class="{'selected': true}">
					{{ false || '请选择' }}
				</view>
				<image class="icon" src="/static/icon/<EMAIL>" mode=""></image>
			</view> -->
		</view>
		<view class="button" @tap="submit()">提交修改</view>

		<!-- <tn-picker v-model="pickerConfig.show" :title="pickerConfig.title" mode="selector" :range="pickerConfig.range"
			rangeKey="text" @confirm="handlePickerConfirm"></tn-picker> -->
	</view>
</template>

<script>
	import {
		getCustomerInfo,
		submitInfoSet
	} from '@/api/process.js'

	export default {
		data() {
			return {
				infoData: {}, // 信息数据
			}
		},
		// 小程序点击分享
		onShareAppMessage() {
			return {
				title: '博迪智家',
				path: '/pages/main/main'
			}
		},
		onLoad(options) {
			this.flow_custom_id = options.flow_custom_id
			// 获取客户资料
			this.getCustomerInfoData()
		},
		methods: {
			// 提交修改
			submit() {
				// 提取表单数据
				submitInfoSet({
					id: this.flow_custom_id,
					flow_custom_name:this.infoData.flow_custom_name,
					address:this.infoData.address,
					phone:this.infoData.phone
				}).then(res => {
					if (res) {
						// 发送刷新事件
						uni.$emit('refreshProcess')
						this.$util.redirectTo('success', {
							type: 'saveDuration',
							info: res.info
						})
					}
				})
			},
			// 获取客户资料
			getCustomerInfoData() {
				getCustomerInfo({
					flow_custom_id: this.flow_custom_id
				}).then(res => {
					if (res) {
						this.infoData = res.detail;
						if(this.infoData.clue_name){
							uni.setNavigationBarTitle({
								title: this.infoData.clue_name + '客户资料'
							})
						} else{
							uni.setNavigationBarTitle({
								title: '客户资料'
							})
						}
						
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 40rpx;
		padding-bottom: calc(40rpx + env(safe-area-inset-bottom));



		.info-card {
			position: relative;
			padding: 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			&+.info-card {
				margin-top: 38rpx;
			}

			.info-item {
				display: flex;
				align-items: center;
				height: 96rpx;
				border-bottom: 2rpx solid #F0F0F0;

				.label {
					width: 200rpx;
					font-size: 28rpx;
					color: #3A3B40;
					line-height: normal;
				}

				.text {
					flex: 1;
					font-size: 28rpx;
					color: #555555;
					line-height: normal;
					text-align: right;
				}
			}
		}


		.info-box {
			margin-top: 30rpx;
			padding: 0 32rpx 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			.title {
				padding: 48rpx 0 20rpx;
				font-weight: 700;
				font-size: 32rpx;
				color: #3A3B40;
				line-height: normal;
			}

			.form-item {
				display: flex;
				border-bottom: 2rpx solid #F0F0F0;

				.label {
					width: 200rpx;
					font-size: 28rpx;
					color: #3A3B40;
					line-height: normal;
				}

				.text {
					flex: 1;
					font-size: 28rpx;
					color: #BBBEC6;
					line-height: normal;
					text-align: right;

					&.selected {
						color: #555555;
					}
				}

				.icon {
					display: block;
					width: 28rpx;
					height: 16rpx;
					margin-left: 30rpx;
				}

				&.form-input {
					display: flex;
					align-items: center;
					height: 96rpx;

					.input {
						flex: 1;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
						text-align: right;
					}
				}

				// select选择框
				&.form-select {
					display: flex;
					align-items: center;
					height: 96rpx;
				}
			}
		}

		.button {
			display: flex;
			justify-content: center;
			align-items: center;
			flex: 1;
			height: 80rpx;
			margin-top: 40rpx;
			background: $theme-color;
			border-radius: 8rpx;
			font-weight: 500;
			font-size: 28rpx;
			color: #FFFFFF;
			line-height: normal;
		}
	}
</style>