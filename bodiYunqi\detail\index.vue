<template>
  <view class="container">
    <!-- 顶部标题 -->
    <view class="header">
      <text class="header-title">{{detailData.leadName || '线索详情'}}</text>
    </view>

    <!-- Tab 切换 -->
    <view class="tab-container">
      <view class="tab-header">
        <view
          class="tab-item"
          :class="{'active': currentTab === 0}"
          @tap="switchTab(0)"
        >
          <text class="tab-text">客户资料</text>
        </view>
        <view
          class="tab-item"
          :class="{'active': currentTab === 1}"
          @tap="switchTab(1)"
        >
          <text class="tab-text">流程</text>
        </view>
      </view>
    </view>

    <!-- 内容区域 -->
    <view class="content-container">
      <!-- 客户资料 -->
      <view v-if="currentTab === 0" class="tab-content">
        <view class="section">
          <view class="section-title">相关负责人</view>
          <view class="info-card">
            <view class="info-row">
              <text class="info-label">销售人：</text>
              <text class="info-value">{{detailData.salesperson}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">流程负责人：</text>
              <text class="info-value">{{detailData.nodeOwner}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">当前节点：</text>
              <text class="info-value">{{detailData.currentNode}}</text>
            </view>
          </view>
        </view>

        <view class="section">
          <view class="section-title">客户基本信息</view>
          <view class="info-card">
            <view class="info-row">
              <text class="info-label">客户姓名：</text>
              <text class="info-value">{{detailData.customerName}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">联系电话：</text>
              <text class="info-value">{{detailData.customerPhone}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">客户类型：</text>
              <text class="info-value">{{detailData.customerType}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">客户来源：</text>
              <text class="info-value">{{detailData.customerSource}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">意向产品：</text>
              <text class="info-value">{{detailData.interestedProduct}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">预算范围：</text>
              <text class="info-value">{{detailData.budgetRange}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">客户地址：</text>
              <text class="info-value">{{detailData.customerAddress}}</text>
            </view>
            <view class="info-row" v-if="detailData.remark">
              <text class="info-label">备注信息：</text>
              <text class="info-value">{{detailData.remark}}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 流程 -->
      <view v-if="currentTab === 1" class="tab-content">
        <view class="section">
          <view class="section-title">流程信息</view>
          <view class="info-card">
            <view class="info-row">
              <text class="info-label">开启时间：</text>
              <text class="info-value">{{detailData.startTime}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">截止时间：</text>
              <text class="info-value">{{detailData.deadline}}</text>
            </view>
            <view class="info-row">
              <text class="info-label">当前状态：</text>
              <text class="info-value status-text" :class="'status-' + detailData.status">
                {{getStatusText(detailData.status)}}
              </text>
            </view>
            <view class="info-row" v-if="detailData.alertInfo">
              <text class="info-label">报警信息：</text>
              <text class="info-value alert-text">{{detailData.alertInfo}}</text>
            </view>
          </view>
        </view>

        <view class="section">
          <view class="section-title">流程节点</view>
          <text class="process-desc">流程节点功能开发中...</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DetailIndex',
  data() {
    return {
      currentTab: 0, // 当前选中的tab，0-客户资料，1-流程
      detailData: {} // 详情数据
    }
  },
  onLoad(options) {
    console.log('详情页参数:', options)
    this.loadDetailData(options)
  },
  methods: {
    // 切换tab
    switchTab(index) {
      this.currentTab = index
    },

    // 加载详情数据
    loadDetailData(options) {
      // 模拟数据，实际应该根据options.id调用API获取数据
      this.detailData = {
        id: options.id || 1,
        leadName: '张先生智能家居项目',
        salesperson: '小王',
        nodeOwner: '小王',
        currentNode: '需求确认',
        customerName: '张先生',
        customerPhone: '138****1234',
        customerType: '个人客户',
        customerSource: '线上推广',
        interestedProduct: '智能家居系统',
        budgetRange: '10-20万',
        customerAddress: '北京市朝阳区xxx小区',
        remark: '客户对智能家居很感兴趣，希望能够实现全屋智能化',
        startTime: '2024-01-15 10:30',
        deadline: '2024-01-20 18:00',
        status: 'pending',
        alertInfo: '即将超时'
      }
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待处理',
        'processing': '处理中',
        'completed': '已完成',
        'cancelled': '已取消',
        'active': '进行中',
        'potential': '潜在客户'
      }
      return statusMap[status] || '未知状态'
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  background-color: #ffffff;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.header-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.tab-container {
  background-color: #ffffff;
  border-bottom: 1rpx solid #e5e5e5;
}

.tab-header {
  display: flex;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  position: relative;

  &.active {
    .tab-text {
      color: #007aff;
      font-weight: bold;
    }

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background-color: #007aff;
      border-radius: 2rpx;
    }
  }
}

.tab-text {
  font-size: 30rpx;
  color: #666666;
  transition: color 0.3s;
}

.content-container {
  padding: 20rpx;
}

.tab-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.section {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #e5e5e5;
}

.info-card {
  padding: 30rpx;
}

.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  font-size: 28rpx;
  color: #666666;
  min-width: 160rpx;
  flex-shrink: 0;
}

.info-value {
  font-size: 28rpx;
  color: #333333;
  flex: 1;
  line-height: 1.5;
}

.status-text {
  font-weight: 500;

  &.status-pending {
    color: #ff9500;
  }

  &.status-processing {
    color: #007aff;
  }

  &.status-completed {
    color: #34c759;
  }

  &.status-cancelled {
    color: #ff3b30;
  }

  &.status-active {
    color: #007aff;
  }

  &.status-potential {
    color: #ff9500;
  }
}

.alert-text {
  color: #ff3b30;
  font-weight: 500;
}

.process-desc {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
  padding: 60rpx 30rpx;
  display: block;
}
</style>
