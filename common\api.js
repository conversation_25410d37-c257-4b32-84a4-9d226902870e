import md5 from '@/common/md5.js';
import httpApi from '@/config/app.js'
//封装的API接口
const api = {
	ver: '0.0.1', //版本号
	debug: true, //是否开启调试模式
	config: {
		// 系统配置
		logo: '@/static/logo.png',
		appName: '应用名称',
		host: httpApi,
		// testHost: '',
		testHost: 'http://bdbuild.com',
		// #ifdef H5
		channel: 'h5',
		module: '/api/mini.', //接口模块
		apiKey: '123123', //APIkey
		apiSecret: '1231123', //API秘钥
		// #endif
		// #ifdef APP
		channel: 'app',
		module: '/api/mini.', //接口模块
		apiKey: '123123', //APIkey
		apiSecret: '1231123', //API秘钥
		// #endif
		// #ifdef MP
		channel: 'mini',
		module: '/api/mini.', //接口模块
		apiKey: '123123', //APIkey
		apiSecret: '1231123', //API秘钥
		// #endif
		homePath: '', //首页路径
		loginPath: '', //登陆页面路径
		regPath: '', //注册页面
		loadText: {
			contentdown: "上拉显示更多",
			contentrefresh: "正在加载...",
			contentnomore: "已加载全部数据了"
		}, //	加载提示文字
		company: '北京博笛智家科技有限公司', //公司名称
		companyAbbr: '博笛智家', //公司简称
		address: '北京市顺义区木林镇顺焦路木林段83号2层249室', //	公司地址
		contact: '博笛', //联系人
		tel: '010-61419053', //联系电话
	},
	wxMini: {
		//微信小程序配置
		appId: '',
		appSecret: ''
	},
	//微信公众号配置
	wxMp: {
		appId: '',
		appSecret: ''
	},
	wxQy: {
		//微信企业号配置
		appId: '',
		appSecret: ''
	},
	data: {}, //本地缓存数据接口
	//判断数据类型
	isTargetType: function(obj, typeString) {
		return typeof obj === typeString;
	},
	//是否为函数
	isFunction: function(obj) {
		return this.isTargetType(obj, "function");
	},
	//是否是字符串
	isString: function(str) {
		return this.isTargetType(str, "string");
	},
	//是否为数组
	isArray: function(str) {
		return str instanceof Array;
	},
	// 回调函数
	isBack: function(callback, data) {
		if (callback == undefined) {
			return false;
		}
		data = data || "";
		if (typeof callback == 'function') {
			callback(data);
		}
	},
	// 解析网址中的各项参数，并转换为对象
	praseScene(param, k, p) {
			// let params = new Object();
			// let deScene = decodeURIComponent(scene).split('&');
			// for (let i = 0; i < deScene.length; i++) {
			// 	params[deScene[i].split('=')[0]] = deScene[i].split('=')[1];
			// }
			// return params;
		if (typeof param != 'string') return {};
		k = k ? k : '&'; //整体参数分隔符
		p = p ? p : '='; //单个参数分隔符
		var value = {};
		if (param.indexOf(k) !== -1) {
			param = param.split(k);
			for (var val in param) {
				if (param[val].indexOf(p) !== -1) {
					var item = param[val].split(p);
					value[item[0]] = item[1];
				}
			}
		} else if (param.indexOf(p) !== -1) {
			var item = param.split(p);
			value[item[0]] = item[1];
		} else {
			return param;
		}
		return value;
			
	},
	//设置或更新数据
	setData: function(key, value) {
		if (value) {
			return uni.setStorageSync(key, value);
		} else {
			return uni.removeStorageSync(key);
		}
	},
	//获取数据
	getData: function(key, defaultValue) {
		let value = uni.getStorageSync(key);
		return value ? value : (defaultValue ? defaultValue : '');
	},
	//删除数据
	deleteData: function(key) {
		let that = this;
		// 如果是数组
		if (that.isArray(key)) {
			for (var i = 0; i < key.length; i++) {
				uni.removeStorageSync(key[i]);
			}
			return false;
		}
		// 如果是单独删除
		if (that.isString(key)) {
			return uni.removeStorageSync(key);
		}
	},
	//清除所有缓存数据
	clearData: function() {
		uni.clearStorageSync();
	},
	// 初始化数据
	init: function(data) {
		let that = this;
		for (var i = 0; i < data.length; i++) {
			that.data[data[i]] = that.getData(data[i]);
		}
	},
	//验证数据
	valid: function(dataArray, callback) {
		let that = this;
		let checkStatus = true; // 校验结果
		let msg = '验证通过';
		for (let i = 0, len = dataArray.length; i < len; i++) {
			if (!dataArray[i][0] && dataArray[i][0] == false) {
				that.config.debug || console.log(dataArray[i][1] + '为' + dataArray[i][0]);
				checkStatus = false;
				if (dataArray[i][2]) {
					msg = dataArray[i][2];
					break;
				} else {
					msg = dataArray[i][1] + '不得为空';
					break;
				}
			}
		}

		// 有回调的话，返回数组，没有的话，提示字符串
		if (callback) {
			callback({
				"status": checkStatus,
				"info": msg
			});
		} else {
			that.toast(msg);
			return checkStatus;
		}
	},
	//取小数部分
	decimal(number) {
		if (typeof number == 'number') {
			let str = number.toFixed(2).toString().split(".");
			if (str.length == 2) {
				return str[1];
			} else {
				return "00";
			}
		} else if (typeof number == 'string') {
			let str = number.split(".");
			if (str.length == 2) {
				return str[1];
			} else {
				return "00";
			}
		} else {
			return "--";
		}
	},
	//获取随机长度字符串
	randString: function(len, type) {
		let all = 'abacdefghjklmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ123456789';
		let num = '0123456789';
		let char = 'abacdefghjklmnpqrstuvwxyzABCDEFGHJKLMNPQRSTUVWXYZ';
		let min = 0;
		let str = ''; //定义随机字符串 变量
		//判断是否指定长度，否则默认长度为16
		len = len || 16;
		//循环生成字符串
		for (var i = 0, index; i < len; i++) {
			if (type == 2) {
				index = Math.floor(Math.random() * (charNum.length - 1 - min)) + min;
				str += charNum[index];
			} else if (type == 3) {
				index = Math.floor(Math.random() * (charAlp.length - 1 - min)) + min;
				str += charAlp[index];
			} else {
				index = Math.floor(Math.random() * (charAll.length - 1 - min)) + min;
				str += charAll[index];
			}
		}
		return str;
	},
	//删除数组重复元素
	removeDuplicate(arr) {
		const uniqueArr = arr.filter((value, index, self) => {
			return self.indexOf(value) === index;
		});
		return uniqueArr;
	},
	// 提示信息
	toast: function(title, duration, success) {
		uni.showToast({
			title: title || "操作提示",
			icon: success ? 'success' : 'none',
			duration: duration || 2000
		})
	},
	// 弹窗
	modal: function(title, content, param, callback) {
		let that = this;

		let args = Object.assign( {
			title: title,
			content: content,
			cancelText: '取消',
			cancelColor: '#999',
			confirmText: '确定',
			confirmColor: '#6F87F8 '
		},param);

		args.success = function(res) {
			if (res.confirm) {
				that.isBack(callback, {
					status: 'confirm'
				});
			} else if (res.cancel) {
				that.isBack(callback, {
					status: 'cancel'
				})
			} else {
				that.isBack(callback, {
					status: 'error'
				})
			}
		}
		uni.showModal(args);
	},
	//展示加载
	showLoading: function(title, mask) {
		uni.showLoading({
			mask: mask || true,
			title: title || '加载中...'
		})
	},
	//关闭加载
	hideLoading: function() {
		uni.hideLoading();
	},
	// 复制内容
	copy: function(value) {
		uni.setClipboardData({
			data: value,
			success() {
				api.toast('复制成功');
			}
		});
	},
	//环境微信判断
	isWechat: function() {
		return String(navigator.userAgent.toLowerCase().match(/MicroMessenger/i)) === "micromessenger";
	},
	//是否是手机号
	isMobile: function(mobile) {
		return /^(0|86)?(1[2-9][0-9])[0-9]{8}$/.test(mobile);
	},
	//是否登陆
	isLogin: function() {
		let that = this;
		that.init(['token']);
		return that.data.token ? true : false;
	},
	//需要登录可见
	needLogin: function() {
		let that = this;
		if (!that.isLogin()) {
			that.reLaunch(that.config.loginPath);
		}
	},
	//退出登陆
	loginOut: function() {
		let that = this;
		that.modal("确认退出", "您确认要退出账号吗？", {}, function(event, e) {
			if (event.status == 'confirm') {
				that.deleteData(['token']);
				that.reLaunch(that.config.homePath);
			}
		});
	},
	/** 跳转底层
	 * @param {Object} url
	 * @param {Object} param
	 * @param {Object} isLogin
	 */
	to: function(url, param, isLogin) {
		let that = this;
		param = param || {};
		// 合并配置
		let args = Object.assign({
			url: url,
			toType: 'navigate',
			animationDuration: 300,
			animationType: 'pop-in',
			fail: function(res) {
				that.toast(res.errMsg);
				return false;
			}
		}, param);

		that.config.debug || console.log(args);

		if (isLogin) {
			if (that.isLogin()) {
				if (args['toType'] == 'navigate') {
					uni.navigateTo(args);
				} else if (args['toType'] == 'redirect') {
					uni.redirectTo(args);
				} else if (args['toType'] == 'relaunch') {
					uni.reLaunch(args);
				} else if (args['toType'] == 'switch') {
					uni.switchTab(args);
				} else {
					that.toast("不支持的跳转类型");
				}
			} else {
				that.modal('暂未登陆', '您需要登陆后才可以查看该内容', {}, function(res) {
					if (res.status == 'confirm') {
						if (that.config.loginPath) {
							that.to(that.config.loginPath);
						}
						return false;
					} else {
						return false;
					}
				});
			}
		} else {
			that.config.debug || console.log('无需登录就可以切换');
			if (args['toType'] == 'navigate') {
				uni.navigateTo(args);
			} else if (args['toType'] == 'redirect') {
				uni.redirectTo(args);
			} else if (args['toType'] == 'relaunch') {
				uni.reLaunch(args);
			} else if (args['toType'] == 'switch') {
				uni.switchTab(args);
			} else {
				that.toast("不支持的跳转类型");
			}
		}
	},
	/**
	 * 跳转
	 * @param {Object} url 跳转的地址
	 * @param {Object} param	跳转参数
	 * @param {Object} isLogin 是否需要登录
	 */
	navigateTo: function(url, param, isLogin) {
		let that = this;
		that.extends(param, {
			toType: 'navigate'
		});
		that.to(url, param, isLogin);
	},
	redirectTo: function(url, param, isLogin) {
		let that = this;
		that.extends(param, {
			toType: 'redirect'
		});
		that.to(url, param, isLogin);
	},
	relaunchTo: function(url, param, isLogin) {
		let that = this;
		that.extends(param, {
			toType: 'relaunch'
		});
		that.to(url, param, isLogin);
	},
	switchTab: function(url, param, isLogin) {
		let that = this;
		that.extends(param, {
			toType: 'switch'
		});
		that.to(url, param, isLogin);
	},
	// 回退
	navigateBack: function(delta, param) {
		let that = this;
		let args = {
			delta: (delta || that.data.delta) || 1,
			animationDuration: 150
		}
		if (typeof param === 'object') {
			that.extend(args, param);
		}
		that.config.debug || console.log(args);
		that.setData('delta', 1);
		uni.navigateBack(args);
	},
	// 回到首页
	backHome: function() {
		let that = this;
		that.switchTab(that.config.homePath);
	},
	//查看图片
	picture: function(e) {
		let that = this;
		let src = [];
		if (that.isString(e)) {
			src = [e];
		} else if (that.isArray(e)) {
			src = e;
		} else {
			src = [e.currentTarget.dataset.src];
		}
		uni.previewImage({
			urls: src,
			longPressActions: {
				itemList: ['发送给朋友', '保存图片']
			},
			fail(err) {
				that.config.debug || console.log(err);
			}
		});
	},
	//获取url中的参数值
	getUrlParam: function(url, param) {
		return decodeURIComponent((new RegExp('[?|&]' + param + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [,
			''
		])[1].replace(/\+/g, '%20')) || null
	},
	//未获取code时跳转
	getWechatCode: function(href, wxappId, agentId) {
		let local = encodeURIComponent(href); //获取当前页面地址作为回调地址
		let appId = wxappId || api.config.mpAppId;

		let url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=" +
			appId +
			"&redirect_uri=" +
			local +
			"&agentid=" + agentId +
			"&response_type=code&scope=snsapi_base&state=AAAAAAAA";
		//通过微信官方接口获取code之后，会重新刷新设置的回调地址【redirect_uri】
		window.location.href = url;
	},
	//通过code换取openId
	getOpenId: function(url, code, type, callback) {
		let that = this;
		that.request(url, {
			code: code,
			type: type
		}, function(res) {
			that.isBack(callback, res);
		})
	},
	// 格式化当前日期
	formatDate: function() {
		//三目运算符
		const Dates = new Date();
		//年份
		const Year = Dates.getFullYear();
		//月份下标是0-11
		const Months = (Dates.getMonth() + 1) < 10 ? '0' + (Dates.getMonth() + 1) : (Dates.getMonth() + 1);
		//具体的天数
		const Day = Dates.getDate() < 10 ? '0' + Dates.getDate() : Dates.getDate();
		//小时
		const Hours = Dates.getHours() < 10 ? '0' + Dates.getHours() : Dates.getHours();
		//分钟
		const Minutes = Dates.getMinutes() < 10 ? '0' + Dates.getMinutes() : Dates.getMinutes();
		//秒
		const Seconds = Dates.getSeconds() < 10 ? '0' + Dates.getSeconds() : Dates.getSeconds();
		//返回数据格式
		return Year + '-' + Months + '-' + Day + '-' + Hours + ':' + Minutes + ':' + Seconds;
	},
	/**
	 * @title 拨打电话
	 * @param {String} phone 电话
	 */
	call: function(phone) {
		uni.makePhoneCall({
			phoneNumber: phone
		})
	},
	//签名
	sign: function(data) {
		let that = this;
		data.TimeStamp = Date.parse(new Date()) * 0.001;
		data.ApiKey = api.config.apiKey;
		data.ApiSecret = api.config.apiSecret;
		data = objKeySort(data);
		let urlEncode = function(param, key, encode) {
			if (param == null) return "";
			let paramStr = "";
			let t = typeof(param);
			that.config.debug || console.log("参数" + key + "值为" + param + "类型为" + t);
			if (t == "string" || t == "number" || t == "boolean") {
				paramStr += "&" + key + "=" + param;
			} else {
				if (!(param instanceof Array)) {
					for (let i in param) {
						let k = key == null ? i : key + (param instanceof Array ? " [" + i + "]" : "." + i);
						paramStr += urlEncode(param[i], k, encode);
					}
				}
			}
			return paramStr;
		};

		function objKeySort(obj) { //排序的函数
			let newkey = Object.keys(obj).sort();
			let newObj = {}; //创建一个新的对象，用于存放排好序的键值对
			for (let i = 0; i < newkey.length; i++) { //遍历newkey数组
				if (obj[newkey[i]] != "") {
					newObj[newkey[i]] = obj[newkey[i]]; //向新创建的对象中按照排好的顺序依次增加键值对
				} else if (obj[newkey[i]] === 0) {
					newObj[newkey[i]] = 0;
				}
			}
			return newObj; //返回排好序的新对象
		}
		let Temp = urlEncode(data).substr(1);
		that.config.debug || console.log("签名字符串：" + z);
		let z = md5.md5(Temp).toUpperCase();
		that.config.debug || console.log("签名为：" + z);
		delete data.ApiSecret;
		data.Sign = z;
		return data;
	},
	//请求网络
	request: async function(url, opt, callback) {
		let that = this;
		let uri = '';
		//初始化数据
		that.init(['token']);
		//构建完整网址
		if (that.config.debug) {
			uri = that.config.testHost + that.config.module + url;
		} else {
			uri = that.config.host + that.config.module + url;
		}

		//附带数据
		opt.deviceType = that.config.channel;
		opt.ver = that.v;
		opt = that.sign(opt);

		//测试情况下打印请求信息
		that.config.debug || console.log("请求地址：" + uri);
		that.config.debug || console.log("请求参数：" + JSON.stringify(opt));

		uni.request({
			url: uri,
			data: opt,
			method: "POST",
			header: {
				'Access-Control-Allow-Origin': '*',
				'Access-Control-Allow-Methods': 'GET,POST',
				'Access-Control-Allow-Headers': 'X-Requested-With,Content-Type',
				'token': that.data.token
			},
			success: (res) => {
				that.config.debug || console.log('请求结果:请求成功');
				// that.config.debug || console.log('请求结果:' + JSON.stringify(res.data));

				//统一处理过期
				if (res.data.status === 'unlogin') {
					that.toast('您的登录状态已失效,请刷新本页面');
					that.clearData();
					//api.navigateTo(api.config.loginPath);
				} else if (res.status == 'noauth') {
					that.toast('您没有权限访问该功能');
				} else {
					that.isBack(callback, res.data);
				}
			},
			fail: (res) => {
				that.config.debug || console.log('请求结果:' + JSON.stringify(res));
				that.modal('请求失败', '请检查网络状态');
			}
		})
	},
	//上传文件
	upload: async function(filePath, opt, callback, progress) {
		let that = this;
		//初始化数据
		that.init(['token']);
		//构建完整网址
		if (that.config.debug) {
			uri = that.config.testHost + that.config.module + that.config.uploadUrl;
		} else {
			uri = that.config.host + that.config.module + that.config.uploadUrl;
		}

		//附带数据
		opt.deviceType = that.config.channel;
		opt.fieldName = opt.fieldName || 'pic';
		//测试情况下打印请求信息
		that.config.debug || console.log("请求地址：" + uri);


		//附带数据
		opt.ver = that.v;
		opt = that.sign(opt);

		that.config.debug || console.log("请求参数：" + opt);

		var uploadTask = uni.uploadFile({
			url: uri,
			filePath: filePath,
			header: {
				'Access-Control-Allow-Origin': '*',
				'Access-Control-Allow-Methods': 'GET,POST',
				'Access-Control-Allow-Headers': 'X-Requested-With,Content-Type',
				'token': that.data.token
			},
			name: opt.FieldName,
			formData: opt,
			timeout: 20000,
			success: (res) => {
				that.config.debug || console.log('请求结果:' + JSON.stringify(res.data));

				let data = JSON.parse(res.data);
				//统一处理过期
				if (data.status === 'unlogin') {
					api.toast('您的登录状态已失效');
					api.clearData();
				} else if (data.status == 'noauth') {
					api.toast('您没有权限访问该功能');
				} else if (data.status == 'error') {
					api.toast(data.info);
				} else {
					that.isBack(callback, data);
				}
			},
			fail: (err) => {
				that.isBack(callback, {
					'status': 'error',
					'info': '请求失败'
				});
			}
		})

		uploadTask.onProgressUpdate((res) => {
			if (progress) {
				progress(progress);
			}
		});
	}


}

export default api