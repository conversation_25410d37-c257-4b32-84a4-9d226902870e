<template>
	<view class="container">
<!-- 		<view class="customer-information">
			<view class="main-info">客户姓名</view>
			<view class="secondary-info">户型</view>
		</view> -->
		<view class="time-line__wrap">
			<tn-time-line>
				<block v-for="(item, index) in expressData" :key="index">
					<tn-time-line-item>
						<template slot="content">
							<view>
								<view class="time-line-item__content__title">
									<text class="text">{{ item.work_name }}</text>
									<text class="status">({{ item.state }})</text>
								</view>
								<view class="process-item-card" v-if="item.children">
									<view v-for="(cItem, cIndex) in item.children" class="node-item">
										<view class="label">{{ cItem.work_name }}</view>
										<view class="input-box">
											<input v-model.number="cItem.duration_self" class="input" type="number"
												placeholder="请输入" />
										</view>
										<view class="unit">小时</view>
									</view>
								</view>
							</view>
						</template>
					</tn-time-line-item>
				</block>
			</tn-time-line>
		</view>

		<view class="button-box">
			<view class="button" @tap="submit()">提交设置</view>
		</view>
	</view>
</template>

<script>
	import {
		getCustomerWorkList,
		submitDurationSet
	} from '@/api/process.js'
	export default {
		data() {
			return {
				flow_custom_id: 0, // 线索id
				expressData: [] // 流程数据
			}
		},
		onLoad(options) {
			this.flow_custom_id = options.flow_custom_id
			// 获取客户流程
			this.getCustomerWorkList()
		},
		methods: {
			// 获取客户流程
			getCustomerWorkList() {
				getCustomerWorkList({
					flow_custom_id: this.flow_custom_id,
					filter: 'duration'
				}).then(res => {
					if (res) {
						this.expressData = res.flow
					}
				})
			},
			// 提交
			submit() {
				// 提取表单数据
				const data = []
				this.expressData.forEach((item) => {
					if (item.children && item.children.length) {
						item.children.forEach(cItem => {
							data.push({
								id: cItem.id,
								value: cItem.duration_self
							})
						})
					}
				})
				submitDurationSet({
					flow_custom_id: this.flow_custom_id,
					data: JSON.stringify(data),
					remark: ''
				}).then(res => {
					if (res) {
						// 发送刷新事件
						uni.$emit('refreshProcess')
						this.$util.redirectTo('success', {
							type: 'saveDuration',
							info: res.info
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		padding: 40rpx 40rpx 100rpx;
		padding-bottom: calc(40rpx + 100rpx + env(safe-area-inset-bottom));

		.customer-information {
			margin-bottom: 40rpx;
			padding: 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			.main-info {
				font-weight: 500;
				font-size: 30rpx;
				color: #3A3B40;
				line-height: 30rpx;
			}

			.secondary-info {
				display: block;
				margin-top: 16rpx;
				font-weight: 500;
				font-size: 26rpx;
				color: #74747B;
				line-height: 26rpx;
			}

			.main-info+.secondary-info {
				margin-top: 32rpx !important;
			}
		}

		.process-item-card {
			margin-top: 20rpx;
			padding: 24rpx 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			.node-item {
				display: flex;
				align-items: center;

				&+.node-item {
					margin-top: 20rpx;
				}

				.label {
					flex: 1;
					font-size: 28rpx;
					color: #3A3B40;
					line-height: normal;
				}

				.input-box {
					display: flex;
					align-items: center;
					box-sizing: border-box;
					width: 160rpx;
					height: 68rpx;
					padding: 0 30rpx;
					background: #F4F4F4;
					border-radius: 4rpx;
					border: 2rpx solid #E9EAEB;

					.input {
						flex: 1;
						font-weight: 400;
						font-size: 28rpx;
						color: #555555;
						line-height: normal;
					}
				}

				.unit {
					margin-left: 20rpx;
					font-weight: 400;
					font-size: 28rpx;
					color: #666666;
					line-height: normal;
				}
			}
		}

		.button-box {
			position: fixed;
			left: 0;
			bottom: 0;
			box-sizing: content-box;
			width: 100%;
			padding-bottom: env(safe-area-inset-bottom);
			background: #FFFFFF;
			z-index: 999;

			.button {
				display: flex;
				justify-content: center;
				align-items: center;
				height: 100rpx;
				background: $theme-color;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				line-height: normal;
			}
		}
	}

	.tn-time-line-class {
		.tn-time-line-item-class {
			&:first-child {
				.tn-time-line-item__node {
					.time-line-item__node {
						background-color: $tn-main-color !important;
					}
				}
			}
		}
	}

	.time-line {

		&__wrap {
			padding: 20rpx;
		}

		&-item {
			&__node {
				width: 44rpx;
				height: 44rpx;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #AAAAAA;

				&--active {
					background-color: $tn-main-color;
				}

				&--icon {
					color: #FFFFFF;
					font-size: 24rpx;
				}
			}

			&__content {
				&__title {
					display: flex;
					align-items: center;


					.text {
						font-weight: 700;
						font-size: 32rpx;
						color: #4A4A4A;
						line-height: 32rpx;
					}

					.status {
						margin-left: 20rpx;
						font-weight: 700;
						font-size: 32rpx;
						color: #4FD1C5;
						line-height: 32rpx;
					}
				}

				&__desc {
					color: $tn-font-sub-color;
					font-size: 28rpx;
					margin-bottom: 6rpx;
				}

				&__time {
					color: $tn-font-holder-color;
					font-size: 26rpx;
				}
			}
		}
	}
</style>