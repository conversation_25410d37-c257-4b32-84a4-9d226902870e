<template>
	<view class="container">
		<view class="box" v-if="storeList.length">
			<!-- 门店id请求 职位列表   -->
			<view class="box_left">
				<view class="top">
					<picker @change="(e) => bindPickerChange(e, 'store')" :value="storeIndex" :range="storeList"
						range-key="name">
						<view style="color: #000;padding: 0 10rpx;">
							{{storeList[storeIndex].name}}
						</view>
					</picker>
				</view>
				<view class="bottom">
					<view v-for="(item, index) in detList" @click="headClick(item, index)" :key="index"
						:class="[copyIndex == index ? 'list_all ok_style' : 'list_all']">
						{{ item.name }}
					</view>
				</view>
			</view>
			<view class="box_right">
				<view class="list_left">
					<view class="items" v-for="(item, index) in userList" :key="index">
						<view class="item_info" @click="headSumbit(item)">
							<image v-if="item.avatar" class="item_att" :src="item.avatar" mode=""></image>
							<text v-else class="tn-icon-my item_att"
								style="padding: 10rpx;font-size: 90rpx;color: #c3cdd9;"></text>
							<view class="item_user">
								<view class="item_name">
									{{ item.real_name }}
								</view>
								<view class="item_det">
									部门-{{ item.dept_name }}
								</view>
								<view class="item_det" style="margin-top: 5rpx;">
									{{ item.phone ? item.phone : '--' }}
								</view>
							</view>
						</view>
						<view class="item_gn" @click="phones(item.phone)" v-if="item.phone">
							<text class="tn-icon-tel"></text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="" v-else>
			<tn-empty mode="data"></tn-empty>
		</view>
		<tn-tips ref="tips" position="center"></tn-tips>
		<tn-popup v-model="show" mode="center" borderRadius="10" @close="close">
			<view class="tips">
				<view class="tips_top">
					确定是否把当前线索分配给{{ tipsValue }}
				</view>
				<view class="btns" @click="confirmAssign">
					确认分配
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				selectedUserLabel: '',
				clueId: '',
				show: false,
				tipsValue: '顺义区-销售-苗海燕',
				//分配字段
				showAssignModal: false,
				assignUsers: [],
				selectedUserId: null,
				assignLoading: false,
				copyIndex: 0,
				storeIndex: 0,
				detList: [],
				storeList: [],
				userList: [],
				allList: []
			}
		},
		onLoad(option) {
			this.clueId = option.id;
			this.fetchAssignUsers();
		},
		methods: {
			filterByStoreDept(data, targetStoreId, targetDeptId = null, options = {}) {
				// 参数校验
				if (!Array.isArray(data)) throw new Error('第一个参数必须是数组');
				if (targetStoreId === undefined || targetStoreId === null) throw new Error('必须提供store_id');

				// 配置合并
				const {
					exactMatch = true, strictType = true
				} = options;
				const storeIdStr = String(targetStoreId).trim();

				return data.filter(item => {
					// 字段存在性校验
					if (!('store_id' in item) || typeof item.store_id !== 'string') return false;
					if (targetDeptId !== null && !('dept_id' in item)) return false;

					// store_id匹配逻辑
					const storeIds = item.store_id.replace(/\s/g, '').split(',');
					const idMatch = exactMatch ?
						storeIds.includes(storeIdStr) :
						item.store_id.includes(storeIdStr);

					// dept_id匹配逻辑
					let deptMatch = true;
					if (targetDeptId !== null) {
						if (strictType) {
							deptMatch = item.dept_id === targetDeptId;
						} else {
							deptMatch = Number(item.dept_id) === Number(targetDeptId);
						}
					}

					return idMatch && deptMatch;
				});
			},
			bindPickerChange(e, type) {
				const index = e.detail.value
				this.copyIndex = 0
				this.storeIndex = index
				this.userList = this.filterByStoreDept(this.allList, this.storeList[index].id, null, {
					strictType: true
				})
				console.log(this.userList)
			},
			close() {
				this.selectedUserId = null
				this.open()
			},
			open() {
				this.$refs.tips.show({
					msg: '已取消分配',
					backgroundColor: 'tn-main-gradient-cyan',
					fontColor: '#FFFFFF',
					duration: '1000'
				})
			},
			phones(phoneNumber) {
				uni.showModal({
					title: '确认拨号',
					content: `是否拨打 ${phoneNumber}？`,
					success: (res) => {
						if (res.confirm) {
							uni.makePhoneCall({
								phoneNumber: phoneNumber.toString()
							})
						}
					}
				})
			},
			// 点击事件
			headClick(item, index) {
				this.copyIndex = index
				if (this.copyIndex) {
					this.userList = this.filterByStoreDept(this.allList, this.storeList[this.storeIndex].id, item.id, {
						strictType: true
					})
				} else {
					this.userList = this.filterByStoreDept(this.allList, this.storeList[this.storeIndex].id, null, {
						strictType: true
					})
				}

			},
			onBack() {
				uni.navigateBack();
			},
			// 点击提交
			headSumbit(item) {
                console.log(item)
				this.tipsValue = this.storeList[this.storeIndex].name + '-' + item.dept_name + '-' + item.real_name
				this.selectedUserId = item.id
				// item.name
				this.show = true
			},
			selectUser(userId, userLabel) {
				this.selectedUserId = userId;
				this.selectedUserLabel = userLabel;
			},
			fetchAssignUsers() {
				this.$api.request('user/getUserList', {}, (res) => {
					console.log(res)
					if (res.status == 'ok') {
						this.detList = [{ id: 0, name: '全部' }, ...res.dept_info]
						this.storeList = res.store_info
						this.allList = res.user_info
						this.userList = this.filterByStoreDept(this.allList, this.storeList[0].id, null, {
							strictType: true
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},
			confirmAssign() {

				this.assignClue();
			},
			assignClue() {
				this.$api.request('partner/allocate', { id: this.clueId, belong_user_id: this.selectedUserId, store_id: this.storeList[this.storeIndex].id }, (res) => {
					console.log(res)
					if (res.status == 'ok') {
						uni.showToast({
							title: res.info
						});
						const timer = setTimeout(() => {
							uni.navigateBack();
							clearTimeout(timer)
						}, 500)
					} else {
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		flex: 1;
		// background-color: #f5f5f5;
		padding: 0rpx;
	}

	.tips {
		padding: 30rpx;
		border-radius: 10rpx;

		.tips_top {
			width: 360rpx;
		}

		.btns {
			margin: 0 auto;
			margin-top: 40rpx;
			font-size: 24rpx;
			width: 180rpx;
			text-align: center;
			padding: 10rpx 0rpx;
			border-radius: 70rpx;
			background: #00FFC8;
			color: #fff;
		}
	}

	.box {
		width: 100%;
		height: 100vh;
		display: flex;
		background: #fff;

		.box_left {
			// flex: 1;
			background-color: #eef7ff;
			height: 100%;
			box-shadow: 6px 0 12px -4px rgba(0, 0, 0, 0.12);

			.top {
				width: 100%;
				height: 90rpx;
				text-align: center;
				line-height: 90rpx;
				box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.08);
				font-size: 28rpx;
				/* 解决部分安卓机渲染问题 */
				transform: translateZ(0);
			}

			.bottom {
				height: calc(100% - 90rpx);
				overflow: hidden;
				overflow-y: auto;

				.ok_style {
					color: #31C9E8 !important;
				}

				.list_all {
					font-size: 24rpx;
					width: 100%;
					height: 90rpx;
					text-align: center;
					line-height: 90rpx;
				}

			}
		}

		.box_right {
			flex: 1;
			height: 100%;
			background-color: #e5eefd;

			.list_left {
				width: 100%;
				height: 100%;
				overflow: hidden;
				overflow-y: auto;

				.items {
					width: calc(100% - 48rpx);
					margin: 0 auto;
					padding: 10rpx 0;
					display: flex;
					align-items: center;
					justify-content: space-between;

					.item_info {
						display: flex;
						align-items: center;
						gap: 20rpx;

						.item_att {
							width: 90rpx;
							height: 90rpx;
							border-radius: 50%;
						}

						.item_user {
							.item_name {
								font-size: 26rpx;
								font-weight: 550;
							}

							.item_det {
								font-size: 20rpx;
								color: #aaa;
							}
						}
					}

					.item_gn {
						position: relative;
						top: 20rpx;
						right: 30rpx;
					}
				}
			}
		}
	}

	.header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		background-color: #ffffff;
		margin-bottom: 20rpx;
	}

	.title {
		font-size: 36rpx;
		font-weight: bold;
	}

	.back-btn {
		font-size: 30rpx;
		color: #666;
		padding: 10rpx 20rpx;
	}

	.user-list {
		background-color: #ffffff;
		border-radius: 12rpx;
		padding: 20rpx;
	}

	.user-item {
		padding: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
		font-size: 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		cursor: pointer;

		&.active {
			color: #007AFF;
			font-weight: bold;
		}
	}

	.footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		padding: 30rpx;
		border-top: 1rpx solid #f0f0f0;
	}

	.confirm-btn {
		width: 100%;
		padding: 20rpx;
		background-color: #007AFF;
		color: #ffffff;
		border-radius: 8rpx;
		font-size: 30rpx;
		border: none;
		cursor: pointer;
	}
</style>