<template>
	<view class="app">
		<view class="list">
			<view class="list_info">
				<view class="items" v-for="(item, index) in dataList" :key="index" @click="jumpXx(item)">
					<view class="items_info">
						<view class="">{{ item.userName }}</view>
						<view class="rights">{{ item.create_time }}</view>
						<image src="/static/wqd.png" class="imgs" mode="widthFix" v-if="item.id == 0"></image>
						<image src="/static/yqd.png" class="imgs" mode="widthFix" v-if="item.id != 0"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>
-
<script>
	export default{
		data(){
			return {
				pageData: {
					pageNo: 1,
					pageSize: 10
				},
				dataList: [],
				count: 0
			}
		},
		onLoad() {
			this.getList()
		},
		onShow() {
			this.getList()
		},
		onReachBottom() {
			if(this.count > this.pageData.pageSize) return
			this.pageData.pageSize += 10
			this.getList()
		},
		methods: {
			jumpXx(item){
				console.log(item)
				let status = 'detail'
				if(item.id == 0){
					status = 'add'
				} else if(item.is_day == 2){
					status = 'edit'
				} else{
					status = 'detail'
				}
				uni.navigateTo({
					url: `/leading-eggs/index?id=${item.id}&status=${status}&isS=0`
				})
			},
			formatDate(date = new Date(), separator = '-') {
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				return [year, month, day].join(separator)
			},
			getList(){
				this.$api.request('activityDayWork/listing', this.pageData, (res)=>{
					this.dataList = res.list
					this.count = res.count
					const timer = this.formatDate()
					if(res.is_add){
						this.dataList.unshift({id: 0, create_time: timer, userName: "暂无记录" })
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.app{
		width: 100%;
		.list{
			width: 100%;
			.list_info{
				width: calc(100% - 64rpx);
				margin: 0 auto;
				position: relative;
				.items{
					position: relative;
					margin-top: 20rpx;
					width: 100%;
					padding: 20rpx;
					background: #fff;
					border-radius: 10rpx;
					.imgs{
						width: 80rpx;
						position: absolute;
						bottom: 0rpx;
						right: 0;
					}
					.items_info{
						width: calc(100% - 24rpx);
						margin: 0 auto;
						
						display: flex;
						justify-content: space-between;
						.rights{
							margin-right: 100rpx;
						}
					}
				}
			}
		}
	}
</style>