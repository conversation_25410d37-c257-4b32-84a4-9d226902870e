<template>
	<view class="oa-content">
		<view class="" :style="{paddingTop: vuex_custom_bar_height + 'px'}">
			<view class="tn-margin"
				style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx 20rpx 30rpx;">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min"
					style="padding: 30rpx 30rpx 20rpx 0rpx;" @click="toggleSection('showBasicInfo')">
					<view class="justify-content-item">
						<text class="tn-text-bold tn-text-xl">基本信息</text>
					</view>
					<view class="justify-content-item tn-color-gray--dark">
						<!-- *为必填 -->
					</view>
				</view>
				<view v-if="showBasicInfo">
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								线索名称 <text class="tn-color-red tn-padding-left-xs">*</text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入线索名称" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.clue_name"></input>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								联系电话 <text class="tn-color-red tn-padding-left-xs">*</text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入联系电话" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.phone"></input>
							</view>
						</view>
					</view>

					<view class="">
						<tn-picker mode="region" v-model="form.showMap" :areaCode='["01", "0101", "010101"]'
							@confirm="confirmPickerMap"></tn-picker>
					</view>

					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom"
						@tap="showPickerMap">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								省市区 <text class="tn-color-red tn-padding-left-xs">*</text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<view class="tn-color-gray" v-if="form.resultMap===''">
									请选择
									<text class="tn-icon-right tn-padding-left-xs"></text>
								</view>
								<view class="" v-else>
									{{ form.resultMap }}
									<text class="tn-icon-right tn-padding-left-xs"></text>
								</view>
							</view>
						</view>
					</view>

					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								详细地址 <text class="tn-color-red tn-padding-left-xs"></text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入详细地址" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.address"></input>
							</view>
						</view>
					</view>






					<picker @change="(e) => bindPickerChange(e, 'channel')" :value="indexChannel"
						:range="channelSourceArr" range-key="label">
						<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									渠道<text class="tn-color-red tn-padding-left-xs">*</text>
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexChannel === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{ channelSourceArr[indexChannel].label }}

										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>
					<!-- <view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								渠道<text class="tn-color-red tn-padding-left-xs">*</text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<view>{{ channelSourceArr[indexChannel].label }}</view>
							</view>
						</view>
					</view> -->
					<picker @change="(e) => bindPickerChange(e, 'twoChannel')" :value="indexTwoChannel"
						:range="twoChannelSourceArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									二级渠道<text class="tn-color-red tn-padding-left-xs">*</text>
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexTwoChannel === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{twoChannelSourceArr[indexTwoChannel].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>
					<!-- <view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								二级渠道<text class="tn-color-red tn-padding-left-xs">*</text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<view>
									{{twoChannelSourceArr[indexTwoChannel].label}}
								</view>
							</view>
						</view>
					</view> -->
				</view>
			</view>



			<view class="tn-margin"
				style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx 30rpx 30rpx;">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min"
					style="padding: 30rpx 30rpx 20rpx 0rpx;" @click="toggleSection('showCustomerIntention')">
					<view class="justify-content-item">
						<text class="tn-text-bold tn-text-xl">客户意向</text>
					</view>
					<view class="justify-content-item tn-color-gray--dark">
						<!-- *为必填 -->
					</view>
				</view>

				<view v-if="showCustomerIntention">
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">意向</view>
						</view>
						<!-- 新增 tn-justify-end 类实现右对齐，调整间距 -->
						<view class="tn-flex tn-justify-end" style="flex: 1; gap: 12rpx;">
							<view v-for="(item, idx) in intentionArr" :key="idx" class="tn-radio-button"
								:class="[form.intention == item.value ? 'active' : '']"
								@click="form.intention = item.value">
								{{ item.label }}
							</view>
						</view>
					</view>


					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">层数</view>
						</view>
						<!-- 新增 tn-justify-end 类实现右对齐，调整间距 -->
						<view class="tn-flex tn-justify-end" style="flex: 1; gap: 12rpx;">
							<view v-for="(item, idx) in storeyArr" :key="idx" class="tn-radio-button"
								:class="[form.storey == item.value ? 'active' : '']" @click="form.storey = item.value">
								{{ item.label }}
							</view>
						</view>
					</view>


					<picker @change="(e) => bindPickerChange(e, 'structural_style')" :value="indexStructural_style" :range="structural_styleArr" range-key="label">
						<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									结构形式
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexStructural_style === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{structural_styleArr[indexStructural_style].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>

					<picker @change="(e) => bindPickerChange(e, 'construction')" :value="indexConstruction"
						:range="constructionArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									建房时效
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexConstruction === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{constructionArr[indexConstruction].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'homestead_extent')" :value="indexHomestead_extent"
						:range="homestead_extentArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									宅基地面积
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexHomestead_extent === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{homestead_extentArr[indexHomestead_extent].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'building_region')" :value="indexBuilding_region"
						:range="building_regionArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									建房区域
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexBuilding_region === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{building_regionArr[indexBuilding_region].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'building_budget')" :value="indexBuilding_budget"
						:range="building_budgetArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									预算
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexBuilding_budget === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{building_budgetArr[indexBuilding_budget].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'building_style')" :value="indexBuilding_style"
						:range="building_styleArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									建房风格
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexBuilding_style === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{building_styleArr[indexBuilding_style].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'approval_status')" :value="indexApproval_status"
						:range="approval_statusArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									审批情况
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexApproval_status === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{approval_statusArr[indexApproval_status].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'is_dispute')" :value="indexIs_dispute"
						:range="is_disputeArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									有无纠纷
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexIs_dispute === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{is_disputeArr[indexIs_dispute].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<picker @change="(e) => bindPickerChange(e, 'income_source')" :value="indexIncome_source" :range="income_sourceArr" range-key="label">
						<view
							class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
							<view class="justify-content-item" style="min-width: 150rpx;">
								<view class="tn-text-bold">
									收入来源
								</view>
							</view>
							<view class="justify-content-item tn-color-grey tn-text-right">
								<view class="tn-color-gray tn-color-black" style="max-width:50vw">
									<view class="tn-color-gray" v-if="indexIncome_source === -1">
										请选择
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
									<view class="" v-else>
										{{income_sourceArr[indexIncome_source].label}}
										<text class="tn-icon-right tn-padding-left-xs"></text>
									</view>
								</view>
							</view>
						</view>
					</picker>


					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								喜好 <text class="tn-color-red tn-padding-left-xs"></text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入喜好" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.preference"></input>
							</view>
						</view>
					</view>
				</view>
			</view>


			<view class="tn-margin"
				style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx 30rpx 30rpx;">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min"
					style="padding: 30rpx 30rpx 20rpx 0rpx;" @click="toggleSection('showCustomerBaseInfo')">
					<view class="justify-content-item">
						<text class="tn-text-bold tn-text-xl">客户基础信息</text>
					</view>
					<view class="justify-content-item tn-color-gray--dark">
						<!-- *为必填 -->
					</view>
				</view>


				<view v-if="showCustomerBaseInfo">
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								姓名 <text class="tn-color-red tn-padding-left-xs"></text>
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入姓名" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.customer_name"></input>
							</view>
						</view>
					</view>

					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								备用电话1
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入电话1" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.mobile1"></input>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								备用电话2
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入电话2" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.mobile2"></input>
							</view>
						</view>
					</view>

					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">性别</view>
						</view>
						<!-- 新增 tn-justify-end 类实现右对齐，调整间距 -->
						<view class="tn-flex tn-justify-end" style="flex: 1; gap: 12rpx;">
							<view v-for="(item, idx) in sexArr" :key="idx" class="tn-radio-button"
								:class="[form.sex === item ? 'active' : '']" @click="form.sex = item">
								{{ item }}
							</view>
						</view>
					</view>


					<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								身份证号
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入身份证号" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.id_card"></input>
							</view>
						</view>
					</view>
					
					<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								地推姓名
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入地推姓名" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.push_name"></input>
							</view>
						</view>
					</view>
					<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								地推电话
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入地推电话" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.push_phone"></input>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="tn-margin"
				style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx 30rpx 30rpx;">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min"
					style="padding: 30rpx 30rpx 20rpx 0rpx;" @click="toggleSection('showFamilyInfo')">
					<view class="justify-content-item">
						<text class="tn-text-bold tn-text-xl">家庭信息</text>
					</view>
					<view class="justify-content-item tn-color-gray--dark">
						<!-- *为必填 -->
					</view>
				</view>

				<view v-if="showFamilyInfo">
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								家庭人口数量
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入人口数量" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.population_numbe"></input>
							</view>
						</view>
					</view>

					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">有无配偶</view>
						</view>
						<!-- 新增 tn-justify-end 类实现右对齐，调整间距 -->
						<view class="tn-flex tn-justify-end" style="flex: 1; gap: 12rpx;">
							<view v-for="(item, idx) in is_spouseArr" :key="idx" class="tn-radio-button"
								:class="[form.is_spouse === item ? 'active' : '']" @click="form.is_spouse = item">
								{{ item }}
							</view>
						</view>
					</view>


					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								家庭结构
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<input placeholder="请输入家庭结构" name="input" placeholder-style="color:#AAAAAA"
									v-model="form.family_structure"></input>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="tn-margin"
				style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx 30rpx 30rpx;">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min"
					style="padding: 30rpx 30rpx 20rpx 0rpx;" @click="toggleSection('showDemandInfo')">
					<view class="justify-content-item">
						<text class="tn-text-bold tn-text-xl">需求信息</text>
					</view>
					<view class="justify-content-item tn-color-gray--dark">
						<!-- *为必填 -->
					</view>
				</view>

				<view v-if="showDemandInfo">
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								卧室
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.bedroom_numbe"></tn-number-box>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								客厅
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.parlor_numbe"></tn-number-box>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								书房
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.study_numbe"></tn-number-box>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								卫生间
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.toilet_numbe"></tn-number-box>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								健身房
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.gym_numbe"></tn-number-box>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								储物间
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.storage_numbe"></tn-number-box>
							</view>
						</view>
					</view>
					<view
						class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min tn-padding-top tn-padding-bottom">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								衣帽间
							</view>
						</view>
						<view class="justify-content-item tn-color-grey tn-text-right">
							<view class="tn-color-gray tn-color-black" style="max-width:50vw">
								<tn-number-box v-model="form.cloakroom"></tn-number-box>
							</view>
						</view>
					</view>
					<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-padding-top tn-padding-bottom-xs">
						<view class="justify-content-item" style="min-width: 150rpx;">
							<view class="tn-text-bold">
								草图
							</view>
						</view>
					</view>

					<view class="" style="padding: 20rpx 0rpx 0rpx 0rpx;">
						<tn-image-upload ref="imageUpload" :action="action" :width="200" :height="200"
							:formData="formData" :fileList="fileList" :disabled="disabled" :autoUpload="autoUpload"
							:maxCount="maxCount" :showUploadList="showUploadList" :showProgress="showProgress"
							:deleteable="deleteable" :customBtn="customBtn" @sort-list="onSortList"
							@on-choose-complete="beforeUpload" />
						<!-- :beforeUpload="beforeUpload" -->
					</view>

				</view>

			</view>

			<view class="tn-margin"
				style="background-color: #FFFFFF;border-radius: 24rpx;padding: 20rpx 30rpx 20rpx 30rpx;">
				<view class="tn-flex tn-flex-row-between tn-flex-col-center tn-strip-bottom-min"
					style="padding: 30rpx 30rpx 20rpx 0rpx;">
					<view class="justify-content-item">
						<text class="tn-text-bold tn-text-xl">备注说明</text>
					</view>
					<view class="justify-content-item tn-color-gray--dark">
						<!-- *为必填 -->
					</view>
				</view>

				<view class="tn-color-gray--dark tn-text-justify" style="padding: 10rpx 0rpx 0rpx 0rpx;">
					<view class="tn-bg-gray--light" style="border-radius: 10rpx;margin: 20rpx 0rpx 20rpx 0rpx;">
						<textarea v-model="form.remarks" maxlength="300" placeholder="请填写备注说明"
							placeholder-style="color:#AAAAAA"
							style="height: 160rpx;padding:30rpx 0 20rpx 30rpx;"></textarea>
					</view>
				</view>

			</view>


			<!-- 悬浮按钮-->
			<view class="tn-flex tn-footerfixed tn-padding">
				<view class="tn-flex-1 justify-content-item tn-text-center">
					<tn-button backgroundColor="#27ae60 " padding="40rpx 0" width="60%" :fontSize="28"
						fontColor="#FFFFFF" shape="round" @click="submit()">
						<text class="">编辑确认</text>
					</tn-button>
				</view>
			</view>
			<view class="tn-tabbar-height"></view>
		</view>


	</view>
</template>

<script>
	import httpApi from '@/config/app.js'
	export default {
		data() {
			return {
				form: {
					id: '',
					//基本信息
					clue_name: '',
					phone: '',
					address: '',
					province: '', // 新增省字段
					city: '', // 新增市字段
					area: '', // 新增区字段
					channel_source: '',
					two_channel_source: '',
					//客户意向
					intention: '', //意向
					storey: '', //层数
					structural_style: '', //结构形式
					construction: '', //建房时效
					homestead_extent: '', //宅基地面积
					building_region: '', //建筑区域
					building_budget: '', //预算
					building_style: '', //建房风格
					approval_status: '', //审批情况
					is_dispute: '', //有无纠纷
					income_source: '', //收入来源
					preference: '', //喜好
					//客户基础信息 
					push_name: '',
					push_phone: '',
					customer_name: '',
					mobile1: '',
					mobile2: '',
					sex: '',
					id_card: '',
					//家庭信息
					population_numbe: '',
					is_spouse: '',
					family_structure: '',
					//需求信息
					bedroom_numbe: '',
					parlor_numbe: '',
					study_numbe: '',
					toilet_numbe: '',
					gym_numbe: '',
					storage_numbe: '',
					cloakroom: '',
					remarks: '',
					//三级联动省市区
					showMap: false,
					resultMap: '',
					areaCode: [],
					sketch: '',
				},


				// 选择器索引
				indexChannel: -1,
				indexTwoChannel: -1,
				indexStructural_style: -1,
				indexConstruction: -1,
				indexHomestead_extent: -1, //宅基地面积
				indexBuilding_region: -1, //建筑区域
				indexBuilding_budget: -1, //预算
				indexBuilding_style: -1, //建房风格
				indexApproval_status: -1, //审批情况
				indexIs_dispute: -1, //有无纠纷
				indexIncome_source: -1, //收入来源



				// 数据选项菜单
				channelSourceArr: [], //渠道
				twoChannelSourceArr: [], //二级渠道
				intentionArr: [], //意向
				storeyArr: [], //层数
				structural_styleArr: [], //结构形式
				constructionArr: [], //建房时效
				homestead_extentArr: [], //宅基地面积
				building_regionArr: [], //建筑区域
				building_budgetArr: [], //预算
				building_styleArr: [], //建房风格
				approval_statusArr: [], //审批情况
				is_disputeArr: [], //有无纠纷
				income_sourceArr: [], //收入来源
				sexArr: ['男', '女'],
				is_spouseArr: ['有', '无'],


				// 图片上传相关
				action: httpApi + '/api/mini.main/upload',
				formData: {
					dir: 'profile',
					type: 'image',
				},
				fileList: [],
				disabled: false,
				autoUpload: true,
				maxCount: 5,
				showUploadList: true,
				showProgress: true,
				deleteable: true,
				customBtn: false,
				vuex_custom_bar_height: 1,


				// 折叠状态控制
				showBasicInfo: true,
				showCustomerIntention: false,
				showCustomerBaseInfo: false,
				showFamilyInfo: false,
				showDemandInfo: false,
				showRemarks: false
			}
		},
		methods: {

			// 打开Picker
			openPickerMap() {
				this.form.showMap = true
			},

			// 弹出Picker
			showPickerMap(event) {
				this.openPickerMap()
			},
			// 获取 用户信息
			// 获取用户详细信息
			getUserInfo(id) {
				let that = this
				let obj = {
					id: id
				}
				this.$api.request('customer/detail', obj, (res)=>{
					if(res.status == 'ok'){
						let resData = res.data
						console.log('resData', resData)
						// 基础信息赋值
						that.form.clue_name = resData.clue_name
						that.form.phone = resData.phone
						that.form.resultMap = resData.province + '-' + resData.city + '-' + resData.area
						that.form.address = resData.address
						that.indexChannel = that.channelSourceArr.findIndex(item => item.value == resData.channel_source);
						that.twoChannelSourceArr = that.channelSourceArr[that.indexChannel].children
						that.indexTwoChannel = that.twoChannelSourceArr.findIndex(item => item.value ==resData.two_channel_source);
						// even-value 赋值
						that.form.channel_source = resData.channel_source
						that.form.two_channel_source = resData.two_channel_source
						// 客户意向
						that.form.intention = resData.intention
						that.form.storey = resData.storey
						that.indexStructural_style = that.structural_styleArr.findIndex(item => item.value == resData.structural_style)
						that.indexHomestead_extent = that.homestead_extentArr.findIndex(item => item.value == resData.homestead_extent)
						that.indexBuilding_region = that.building_regionArr.findIndex(item => item.value ==resData.building_region)
						that.indexBuilding_style = that.building_styleArr.findIndex(item => item.value ==resData.building_style)
						that.indexConstruction = that.constructionArr.findIndex(item => item.value ==resData.construction)
						// construction constructionArr[indexConstruction]  building_styleArr[indexBuilding_style]  building_style
						that.indexBuilding_budget = that.building_budgetArr.findIndex(item => item.value ==resData.building_budget)
						that.indexApproval_status = that.approval_statusArr.findIndex(item => item.value ==resData.approval_status)
						that.indexIs_dispute = that.is_disputeArr.findIndex(item => item.value == resData.is_dispute)
						that.indexIncome_source = that.income_sourceArr.findIndex(item => item.value ==resData.income_source)
						that.form.preference = resData.preference
						// 放在form表单中building_budget
						
						that.form.structural_style = that.indexStructural_style != -1 ? that.channelSourceArr[that.indexStructural_style].value : ''
						that.form.homestead_extent = that.indexHomestead_extent != -1 ? that.homestead_extentArr[that.indexHomestead_extent].value : ''
						that.form.building_region = that.indexBuilding_region != -1 ? that.building_regionArr[that.indexBuilding_region].value : ''
						
						that.form.building_budget = that.indexBuilding_budget != -1 ? that.building_budgetArr[that.indexBuilding_budget].value : ''
						that.form.approval_status = that.indexApproval_status != -1 ? that.approval_statusArr[that.indexApproval_status].value : ''
						that.form.is_dispute = that.indexIs_dispute != -1 ? that.is_disputeArr[that.indexIs_dispute].value : ''
						that.form.income_source = that.indexIncome_source != -1 ? that.income_sourceArr[that.indexIncome_source].value : ''
						that.form.construction = that.indexConstruction != -1 ? that.constructionArr[that.indexConstruction].value : ''
						that.form.building_style = that.indexBuilding_style != -1 ? that.building_styleArr[that.indexBuilding_style].value : ''
						// homestead_extent
						// 客户基础信息
						that.form.customer_name = resData.customer_name
						that.form.push_name = resData.push_name
						that.form.push_phone = resData.push_phone
						that.form.mobile1 = resData.mobile1
						that.form.mobile2 = resData.mobile2
						that.form.sex = resData.sex
						that.form.id_card = resData.id_card
						// 家庭信息
						that.form.population_numbe = resData.population_numbe
						that.form.is_spouse = resData.is_spouse == 1 ? '有' : '无'
						that.form.family_structure = resData.family_structure
						// 需求信息
						that.form.bedroom_numbe = resData.bedroom_numbe
						that.form.parlor_numbe = resData.parlor_numbe
						that.form.study_numbe = resData.study_numbe
						that.form.storage_numbe = resData.storage_numbe
						that.form.toilet_numbe = resData.toilet_numbe
						that.form.gym_numbe = resData.gym_numbe
						that.form.remarks = resData.remarks
						that.form.cloakroom = resData.cloakroom
						that.form.sketch = resData.sketch
						
						if (res.detail.sketchArr) {
							let arrOne = res.detail.sketchArr.split(',')
						
							for (let i = 0; i < arrOne.length; i++) {
								let objOne = {
									url: arrOne[i]
								}
								this.fileList.push(objOne)
							}
							console.log(this.fileList)
						}
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},


			// 切换默认地区
			defaultRegionChange(event) {
				if (event.index === 0) {
					this.defaultRegion = ['北京市', '市辖区', '东城区']
					this.form.areaCode = []
				} else if (event.index === 1) {
					this.defaultRegion = []

					this.form.areaCode = ['01', '0101', '010101']
				}
				this.openPickerMap()
			},
			// 切换点击遮罩关闭
			maskCloseableChange(event) {
				this.maskCloseable = event.index === 0 ? true : false
				this.openPickerMap()
			},

			// 点击确认按钮
			confirmPickerMap(event) {
				this.form.resultMap = `${event.province.label}-${event.city.label}-${event.area.label}`
				this.form.province = `${event.province.label}`
				this.form.city = `${event.city.label}`
				this.form.area = `${event.area.label}`
			},



			fetchOptions() {
				
				this.$api.request('common/dictionaryTypeList', { type: 2 }, (res)=>{
					if(res.status == 'ok'){
						this.channelSourceArr = this.transformChannelData(res.info, 26);
						this.structural_styleArr = this.transformChannelData(res.info, 88);
						this.intentionArr = this.transformChannelData(res.info, 93);
						this.storeyArr = this.transformChannelData(res.info, 97);
						this.constructionArr = this.transformChannelData(res.info, 100);
						
						this.homestead_extentArr = this.transformChannelData(res.info, 104);
						this.building_regionArr = this.transformChannelData(res.info, 108);
						this.building_budgetArr = this.transformChannelData(res.info, 112);
						this.building_styleArr = this.transformChannelData(res.info, 117);
						this.approval_statusArr = this.transformChannelData(res.info, 126);
						this.is_disputeArr = this.transformChannelData(res.info, 130);
						this.income_sourceArr = this.transformChannelData(res.info, 134);
						this.getUserInfo(this.form.id)
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},

			// 数据转换
			transformChannelData(apiData, id) {
				const channelData = apiData.find(item => item.id === id);
				if (!channelData || !channelData.children) return [];

				return channelData.children.map(parent => ({
					label: parent.name,
					value: parent.ext_value,
					children: parent.children ? parent.children.map(child => ({
						label: child.name,
						value: child.ext_value
					})) : []
				}));
			},
			onPickerChange(e) {
				const index = e.detail.value;
				this.selectedOption = this.options[index];
			},
			bindPickerChange(e, type) {
				const index = e.detail.value
				switch (type) {
					case 'channel':
						this.indexChannel = index
						this.form.channel_source = this.channelSourceArr[index].value
						// 动态二级渠道选项
						this.twoChannelSourceArr = this.channelSourceArr[index].children
						this.indexTwoChannel = -1
						this.form.two_channel_source = ''
						break
					case 'twoChannel':
						this.indexTwoChannel = index
						this.form.two_channel_source = this.twoChannelSourceArr[index].value
						break
					case 'structural_style':
						this.indexStructural_style = index
						this.form.structural_style = this.structural_styleArr[index].value
						break
					case 'construction':
						this.indexConstruction = index
						this.form.construction = this.constructionArr[index].value
						break
					case 'homestead_extent':
						this.indexHomestead_extent = index
						this.form.homestead_extent = this.homestead_extentArr[index].value
						break
					case 'building_region':
						this.indexBuilding_region = index
						this.form.building_region = this.building_regionArr[index].value
						break
					case 'building_budget':
						this.indexBuilding_budget = index
						this.form.building_budget = this.building_budgetArr[index].value
						break
					case 'building_style':
						this.indexBuilding_style = index
						this.form.building_style = this.building_styleArr[index].value
						break
					case 'approval_status':
						this.indexApproval_status = index
						this.form.approval_status = this.approval_statusArr[index].value
						break
					case 'is_dispute':
						this.indexIs_dispute = index
						this.form.is_dispute = this.is_disputeArr[index].value
						break
					case 'income_source':
						this.indexIncome_source = index
						this.form.income_source = this.income_sourceArr[index].value
						break
				}
			},
			submit() {
				let arrs = []
				if (this.$refs.imageUpload !== undefined) {
					this.$refs.imageUpload.lists.map(item => {
						arrs.push(item.postLine)
					})
				}
				this.form.sketch = arrs.join(',')
				const token = uni.getStorageSync('token');
				//打印form表单数据
				this.form.province = this.form.resultMap.split('-')[0]
				this.form.city = this.form.resultMap.split('-')[1]
				this.form.area = this.form.resultMap.split('-')[2]
				
				this.$api.request('customer/edit', this.form, (res)=>{
					if(res.status == 'ok'){
						uni.showToast({
							title: res.info,
							icon: 'success'
						});
						uni.navigateBack(1)
					} else{
						uni.showToast({
							icon: 'none',
							title: res.info
						})
					}
				})
			},




			onSortList(files) {
				this.fileList = files; // 直接赋值新数组
			},

			beforeUpload(file) {
				const token = uni.getStorageSync('token');
				uni.uploadFile({
					url: this.action,
					filePath: file[file.length - 1].url, // 必须使用本地文件路径（file.path）
					name: 'file', // 与后端接口字段一致
					formData: {
						dir: 'profile',
						type: 'image',
					},
					header: {
						'token': token,
						'Content-Type': 'application/json'
					},
					success: (res) => {
						if (res.statusCode === 200) {
							const response = JSON.parse(res.data);
							this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].onLine =
								response.url
							this.$refs.imageUpload.lists[this.$refs.imageUpload.lists.length - 1].postLine =
								response.path
						}
					},
					fail(err) {
						console.log(err)
					}
				});
				return false; // 阻止组件默认上传
			},


			onLoad(e) {
				console.log('我是当前传参id', e)
				this.form.id = e.id
				this.fetchOptions();

			},

			toggleSection(section) {
				this[section] = !this[section];

			},
			bindRegionChange(e) {
				const [province, city, district] = e.detail.value;
				this.form.province = province;
				this.form.city = city;
				this.form.district = district;
			}

		}
	}
</script>

<style lang="scss" scoped>
	/* 胶囊*/
	.tn-custom-nav-bar__back {
		width: 60%;
		height: 100%;
		position: relative;
		display: flex;
		justify-content: space-evenly;
		align-items: center;
		box-sizing: border-box;
		background-color: rgba(0, 0, 0, 0.15);
		border-radius: 1000rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.5);
		color: #FFFFFF;
		font-size: 18px;

		.icon {
			display: block;
			flex: 1;
			margin: auto;
			text-align: center;
		}

	}

	.oa-content {
		max-width: 640px;
		margin: 0 auto;
		background-color: #E7EDF7;
		min-height: 100vh;
		padding-bottom: 60rpx;
		padding-bottom: calc(80rpx + env(safe-area-inset-bottom) / 2);
		padding-bottom: calc(80rpx + constant(safe-area-inset-bottom));
	}




	/* 间隔线 start*/
	.tn-strip-bottom-min {
		width: 100%;
		border-bottom: 1rpx solid #F8F9FB;
	}

	.tn-strip-bottom {
		width: 100%;
		border-bottom: 20rpx solid rgba(241, 241, 241, 0.8);
	}

	/* 间隔线 end*/


	/* 用户头像 start */
	.logo-image {
		width: 80rpx;
		height: 80rpx;
		position: relative;
	}

	.logo-pic {
		background-size: cover;
		background-repeat: no-repeat;
		// background-attachment:fixed;
		background-position: top;
		border: 2rpx solid rgba(255, 255, 255, 0.05);
		box-shadow: 0rpx 0rpx 80rpx 0rpx rgba(0, 0, 0, 0.15);
		border-radius: 50%;
		overflow: hidden;
		// background-color: #FFFFFF;
	}


	/* 底部悬浮按钮 start*/
	.tn-tabbar-height {
		min-height: 160rpx;
		height: calc(180rpx + env(safe-area-inset-bottom) / 2);
		height: calc(180rpx + constant(safe-area-inset-bottom));
	}

	.tn-footerfixed {
		max-width: 640px;
		margin: 0 auto;
		position: fixed;
		width: 100%;
		bottom: calc(40rpx + env(safe-area-inset-bottom));
		z-index: 1024;
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0);
	}

	/* 底部悬浮按钮 end*/

	/* 自定义上传事件 start */
	.image-upload {
		&__wrap {
			display: flex;
			align-items: center;
		}

		&__item {
			flex: 1;
			width: calc(100% - 60rpx);
			max-width: 50%;
			height: 260rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.image {
				width: 100%;
				height: 100%;
				border: 2rpx dashed #E6E6E6;
				border-radius: 15rpx;
				overflow: hidden;
			}
		}

		&__image,
		&__empty {
			width: 100%;
			height: 90%;
		}

		&__title {
			padding-top: 30rpx;
			line-height: 1;
		}

		&__item+&__item {
			margin-left: 20rpx;
		}
	}

	/* 新增右对齐样式 */
	.tn-justify-end {
		justify-content: flex-end !important;
	}

	.tn-radio-button {
		padding: 8rpx 30rpx;
		border-radius: 4rpx;
		border: 1rpx solid #e6e6e6;
		background: #f8f8f8;
		transition: all 0.3s;
		color: #666;
	}

	.tn-radio-button.active {
		background: #6699ff;
		/* 调整为更柔和的蓝色 */
		color: #fff;
		border-color: #6699ff;
		box-shadow: 0 2rpx 4rpx rgba(102, 153, 255, 0.2);
	}
</style>