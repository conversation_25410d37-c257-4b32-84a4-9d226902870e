
import httpApi from '@/config/app.js'
const baseURL = httpApi

const install = (Vue, vm) => {
	// 初始化请求配置
	uni.$u.http.setConfig((config) => {
		// 域名设置
		config.baseURL = baseURL
		config.header = {
			'Content-Type': 'application/json;charset=UTF-8',
			'Content-Type': 'application/json-patch+json'
		}
		// 设置为json，返回后会对数据进行一次JSON.parse()
		config.dataType = 'json'
		config.responseType = 'text'
		// 注：如果局部custom与全局custom有同名属性，则后面的属性会覆盖前面的属性，相当于Object.assign(全局，局部)
		config.custom = {
			// 请求接口展示Loading
			ShowLoading: true,
			// Loading中是否遮罩
			LoadingMask: true,
			// Loading文本
			LoadingText: '正在加载...',
		} // 全局自定义参数默认值
		// #ifdef H5 || APP-PLUS || MP-ALIPAY || MP-WEIXIN
		config.timeout = 30000
		// #endif
		// #ifdef APP-PLUS
		// 验证 ssl 证书 仅5+App安卓端支持（HBuilderX 2.3.3+）
		config.sslVerify = true
		// #endif
		// #ifdef H5
		// 跨域请求时是否携带凭证（cookies）仅H5支持（HBuilderX 2.6.15+）
		config.withCredentials = false
		// #endif
		// #ifdef APP-PLUS
		// DNS解析时优先使用ipv4 仅 App-Android 支持 (HBuilderX 2.8.0+)
		config.firstIpv4 = false
		// #endif
		// 全局自定义验证器。参数为statusCode 且必存在，不用判断空情况。
		config.validateStatus = (statusCode) => { // statusCode 必存在。此处示例为全局默认配置
			return statusCode >= 200 && statusCode < 300
		}
		return config
	});

	// 请求拦截部分，如配置，每次请求前都会执行
	uni.$u.http.interceptors.request.use((config) => {
		// console.log('uveiw')
		if (config.custom.ShowLoading) {
			// 显示loading
			showLoading(config.custom)
		}
		config.data = config.data || {}

		const _token = uni.getStorageSync('token')
		if (_token) {
			config.header.token = _token
		}

		// 引用token
		// 方式一，存放在vuex的token，假设使用了uView封装的vuex方式
		// 见：https://uviewui.com/components/globalVariable.html
		// config.header.token = vm.token;

		// 方式二，如果没有使用uView封装的vuex方法，那么需要使用$store.state获取
		// config.header.token = vm.$store.state.token;

		// 方式三，如果token放在了globalData，通过getApp().globalData获取
		// config.header.Authorization = _token;

		// 方式四，如果token放在了Storage本地存储中，拦截是每次请求都执行的
		// 所以哪怕您重新登录修改了Storage，下一次的请求将会是最新值
		// const token = uni.getStorageSync('token');
		// config.header.token = token;
		//config.header.Token = 'xxxxxx';

		// 可以对某个url进行特别处理，此url参数为this.$u.get(url)中的url值
		//if (config.url == '/user/login') config.header.noToken = true;

		// 最后需要将config进行return
		return config

	}, config => {
		// 隐藏loading
		hideLoading()
		return Promise.reject(config)
	})

	// 响应拦截，如配置，每次请求结束都会执行本方法
	uni.$u.http.interceptors.response.use((res) => {
		if (res.config.custom.ShowLoading) {
			// 隐藏loading
			hideLoading()
		}
		// if 状态码是否正常
		if (res.statusCode == 200) {
			let result = res.data
			// if 与后台规定status代码进行处理数据返回
			if (result.status === 'ok') { //后台约定status = ok为请求成功
				return result
			} else if (result.code === 'unlogin') { //后台约定 status = unlogin 为未授权，需要重新登录
				// 清除数据
				getApp().globalData.userInfo = {}
				vm.$util.removeStorageSync('userInfo')
				vm.$util.showToast('您的登录信息已过期')
				// 发送事件
				uni.$emit('login')
				setTimeout(function() {
					uni.reLaunch({
						url: '/pages/main/main'
					})
				}, 1000)
				return false
			}
			vm.$util.showToast(result.info)
		} else {
			vm.$util.showToast(res.errMsg)
		}
		return false
	}, (response) => {
		// 隐藏loading
		hideLoading()
		vm.$util.showToast('请求失败～')
		return Promise.reject(response)
	})
}

let loadingNum = 0 // loading加载次数

// 显示loading
const showLoading = (custom) => {
	if (loadingNum <= 0) {
		uni.showLoading({
			title: custom.LoadingText || '正在加载...',
			mask: custom.LoadingMask || false
		})
		loadingNum = 0
	}
	loadingNum++
}

// 隐藏loading
const hideLoading = () => {
	loadingNum--
	if (loadingNum <= 0) {
		uni.hideLoading()
		loadingNum = 0
	}
}

export default {
	install
}