Component({
	data: {
		selected: 0, // 当前选中的tab
		color: '#aaa', // tab 上的文字默认颜色
		selectedColor: '#09152A', // tab 上的文字选中时的颜色
		list: [{
				"show": true,
				"pagePath": "/pages/main/main",
				"iconPath": "/static/tabbar/home.png",
				"selectedIconPath": "/static/tabbar/homeed.png",
				"text": "首页"
			}, {
				"show": true,
				"pagePath": "/pages/process/process",
				"text": "流程",
				"iconPath": "/static/tabbar/home.png",
				"selectedIconPath": "/static/tabbar/homeed.png"
			}, {
				"show": true,
				"pagePath": "/pages/early-warn/early-warn",
				"text": "预警",
				"iconPath": "/static/tabbar/home.png",
				"selectedIconPath": "/static/tabbar/homeed.png"
			},
			{
				"show": true,
				"pagePath": "/pages/my/my",
				"text": "我的",
				"iconPath": "/static/tabbar/my.png",
				"selectedIconPath": "/static/tabbar/myed.png"
			}
		]
	},
	methods: {
		// 切换tabbar
		switchTab(e) {
			const data = e.currentTarget.dataset
			const {
				index,
				path
			} = data
			wx.switchTab({
				url: path
			})
			this.setData({
				selected: index
			})
		},
		// 切换tabbar
		changeTab(index) {
			this.setData({
				selected: index
			})
			wx.switchTab({
				url: this.data.list[index].pagePath
			})
		},
		// 更新tabbar信息
		updateTabbar(type) {
			let list = this.data.list
			switch (type) {
				case 1:
					// 权限1，显示xxtab
					list[1].show = true
					break
				default:
					// 无参数，默认未登录状态，隐藏除首页和个人中心外的全部tab
					list[1].show = false
					break
			}
			this.setData({
				list
			})
		}
	}
})