<template>
	<view class="container">
		<view class="customer-information">
			<view class="main-info">{{infoData.flow_custom_name ? infoData.flow_custom_name : '--'}}</view>
			<view class="secondary-info">结构：{{infoData.structure || '未设置'}}</view>
			<view class="secondary-info">面积：{{infoData.area ? infoData.area : '--'}}</view>
			<view class="secondary-info">已支付：{{infoData.paid ? infoData.paid : '--'}}</view>
			<view class="secondary-info">未支付：{{infoData.surplus ? infoData.surplus : '--'}}</view>
			<view class="secondary-info">总金额：{{infoData.total ? infoData.total : '--'}}</view>
			<view class="secondary-info">开工日期：{{infoData.start_open_date || '--'}}</view>
		</view>

		<view class="customer-information" v-if="dutyList.length>0">
			<view class="main-info">相关负责人</view>
			<block v-for="(item, index) in dutyList" :key="index">
				<view class="secondary-info">{{item.name}}：{{item.real_name}}</view>
			</block>
		</view>

		<tn-sticky>
			<tn-tabs :list="tabList" :isScroll="false" :current="tabCurrent" name="tab-name"
				@change="tabChange"></tn-tabs>
		</tn-sticky>

		<!-- S 客户资料 -->
		<view v-if="tabCurrent === 0" class="main info">
			<view class="info-card">
				<view class="info-item">
					<view class="label">姓名</view>
					<view class="text">{{infoData.flow_custom_name ? infoData.flow_custom_name : '--' }}</view>
				</view>
				<view class="info-item">
					<view class="label">性别</view>
					<view class="text">{{infoData.sex}}</view>
				</view>
				<view class="info-item">
					<view class="label">电话</view>
					<view class="text">{{infoData.phone ? infoData.phone : '--'}}</view>
					<text v-if="infoData.phone" class="tn-icon-copy-fill" style="margin-left: 10rpx;" @click="copyText(infoData.phone)"></text>
				</view>
				<view class="info-item">
					<view class="label">地址</view>
					<view class="text">{{infoData.address ? infoData.address : '--'}}</view>
				</view>
				<view class="info-item">
					<view class="label">位置</view>
					<view class="text">{{infoData.province}} {{infoData.city}} {{infoData.area ? infoData.area : '--'}}</view>
				</view>
			</view>
		</view>
		<!-- E 客户资料 -->

		<!-- S 流程 -->
		<view v-if="tabCurrent === 1" class="main process">
			<view class="time-line__wrap">
				<tn-time-line>
					<block v-for="(item, index) in expressData" :key="index">
						<tn-time-line-item>
							<template slot="content">
								<view>
									<view class="time-line-item__content__title">
										<text class="text">{{ item.work_name }}</text>
										<text class="status" :style="{ color: item.state == '已完成' ? '#98F3A4' : item.state == '待开启' ? '#3D7EFF' : '#FFA726' }">({{ item.state }})</text>
									</view>
									<!-- 此处加不了v-if判断有没有 -->
									<view class="process-item-card" v-if="item.children" @click="handleFlowClick(item.id,item.work_name)">
										<block v-for="(item1, index1) in item.children" :key="index1">
											<view class="node">{{index1 + 1}}. {{item1.work_name}} ( {{item1.state}} )
											</view>
										</block>
									</view>
								</view>
							</template>
						</tn-time-line-item>
					</block>
				</tn-time-line>
			</view>
		</view>
		<!-- E 流程 -->
	</view>
</template>

<script>
	import {
		getCustomerInfo,
		getCustomerWorkList
	} from '@/api/process.js'

	export default {
		data() {
			return {
				flow_custom_id: 0, // 流程客户id
				// tab列表
				tabList: [{
					'tab-name': '客户资料'
				}, {
					'tab-name': '流程'
				}],
				tabCurrent: 0, // 当前tab
				infoData: {}, // 客户资料数据
				expressData: [] ,// 流程数据
				dutyList: [], //负责人列表
			}
		},
		onLoad(options) {
			this.flow_custom_id = options.flow_custom_id
			// 获取客户资料
			this.getCustomerInfoData()
			// 获取客户流程
			this.getCustomerWorkList()
		},
		methods: {
			copyText(text) {
			      uni.setClipboardData({
			        data: text, // 要复制的文本内容
			        success: () => {
			          uni.showToast({
			            title: '复制成功',
			            icon: 'none'
			          });
			        },
			        fail: (err) => {
			          uni.showToast({
			            title: '复制失败',
			            icon: 'none'
			          });
			        }
			      });
			    },
			// 获取客户资料
			getCustomerInfoData() {
				getCustomerInfo({
					flow_custom_id: this.flow_custom_id
				}).then(res => {
					if (res) {
						this.infoData = res.detail;
						this.dutyList = res.duty;
						if(this.infoData.clue_name){
							uni.setNavigationBarTitle({
								title: this.infoData.clue_name + '客户资料'
							})
						} else{
							uni.setNavigationBarTitle({
								title: '客户资料'
							})
						}
					}
				})
			},
			// 获取客户流程
			getCustomerWorkList() {
				getCustomerWorkList({
					flow_custom_id: this.flow_custom_id
				}).then(res => {
					if (res) {
						this.expressData = res.flow;
					}
				})
			},
			// tab切换
			tabChange(index) {
				this.tabCurrent = index
				// 将页面滚动至顶部
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				})
			},
			handleFlowClick(flow_id, flow_name) {
				this.$util.navigateTo('process-flow', {
					flow_id: flow_id,
					work_name: flow_name
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		.customer-information {
			margin: 40rpx;
			padding: 32rpx;
			background: #FFFFFF;
			box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
			border-radius: 12rpx;

			.main-info {
				font-weight: 500;
				font-size: 30rpx;
				color: #3A3B40;
				line-height: 30rpx;
			}

			.secondary-info {
				display: block;
				margin-top: 16rpx;
				font-weight: 500;
				font-size: 26rpx;
				color: #74747B;
				line-height: 26rpx;
			}

			.main-info+.secondary-info {
				margin-top: 32rpx !important;
			}
		}

		::v-deep .tn-tabs {
			background: #fff;
		}

		.main {
			padding: 40rpx;
			padding-bottom: calc(40rpx + env(safe-area-inset-bottom));

			// 资料
			&.info {
				.info-card {
					position: relative;
					padding: 32rpx;
					background: #FFFFFF;
					box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
					border-radius: 12rpx;

					&+.info-card {
						margin-top: 38rpx;
					}

					.info-item {
						display: flex;
						align-items: center;
						height: 96rpx;
						border-bottom: 2rpx solid #F0F0F0;

						.label {
							width: 200rpx;
							font-size: 28rpx;
							color: #3A3B40;
							line-height: normal;
						}

						.text {
							flex: 1;
							font-size: 28rpx;
							color: #555555;
							line-height: normal;
							text-align: right;
						}
					}
				}
			}

			// 流程
			&.process {
				.process-item-card {
					margin-top: 20rpx;
					padding: 30rpx;
					background: #FFFFFF;
					box-shadow: 0 4rpx 8rpx 0 rgba(20, 20, 22, 0.08);
					border-radius: 12rpx;

					.node {
						line-height: 50rpx;
						color: #545454
					}
				}

				.button-wrap {
					position: fixed;
					left: 0;
					bottom: 0;
					width: 100%;
					background-color: #FFFFFF;
					padding-bottom: env(safe-area-inset-bottom);
					z-index: 999;

					.button {
						display: flex;
						justify-content: center;
						align-items: center;
						height: 100rpx;
						background: $theme-color;

						.text {
							font-weight: 500;
							font-size: 32rpx;
							color: #FFFFFF;
							line-height: normal;
						}
					}
				}
			}
		}
	}

	.tn-time-line-class {
		.tn-time-line-item-class {
			&:first-child {
				.tn-time-line-item__node {
					.time-line-item__node {
						background-color: $tn-main-color !important;
					}
				}
			}
		}
	}


	.time-line {

		&__wrap {
			padding: 20rpx;
		}

		&-item {
			&__node {
				width: 44rpx;
				height: 44rpx;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				background-color: #AAAAAA;

				&--active {
					background-color: $tn-main-color;
				}

				&--icon {
					color: #FFFFFF;
					font-size: 24rpx;
				}
			}

			&__content {
				&__title {
					display: flex;
					align-items: center;


					.text {
						font-weight: 700;
						font-size: 32rpx;
						color: #4A4A4A;
						line-height: 32rpx;
					}

					.status {
						margin-left: 20rpx;
						font-weight: 700;
						font-size: 32rpx;
						color: #4FD1C5;
						line-height: 32rpx;
					}
				}

				&__desc {
					color: $tn-font-sub-color;
					font-size: 28rpx;
					margin-bottom: 6rpx;
				}

				&__time {
					color: $tn-font-holder-color;
					font-size: 26rpx;
				}
			}
		}
	}
</style>