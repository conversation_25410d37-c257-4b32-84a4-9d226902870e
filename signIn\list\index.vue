<template>
	<view class="app">
		<view class="lists">
			<view class="items" v-for="(item, index) in list" :key="index" @click="jump(index)">
				<view class="status" :style="{borderColor: todayStatus == 2 && index == 0  ? 'red' : item.can_edit == 1 ? '#01BEFF' : '#FBBD12', color:  todayStatus == 2 && index == 0 ? 'red' : item.can_edit == 1 ? '#01BEFF' : '#FBBD12'}">
					{{ todayStatus == 2 && index == 0 ? '未 提 交' : item.can_edit == 1 ? '可 修 改' : '仅 查 看' }}
				</view>
				<view class="info_time">
					<view class="item_view">
						<view class="lable">
							创建时间：
						</view>
						<view class="content">
							{{ item.target_date }}
						</view>
					</view>
				</view>
				<view class="info_time">
					<view class="item_view">
						<view class="lable">
							面访数：
						</view>
						<view class="content">
							{{ item.interview_num }}
						</view>
					</view>
					<view class="item_view">
						<view class="lable">
							带看进店数：
						</view>
						<view class="content">
							{{ item.visits_store_num }}
						</view>
					</view>
				</view>
				<view class="info_time">
					<view class="item_view">
						<view class="lable">
							转抖音条数：
						</view>
						<view class="content">
							{{ item.tiktok_num }}
						</view>
					</view>
					<view class="item_view">
						<view class="lable">
							朋友圈转发数：
						</view>
						<view class="content">
							{{ item.wechat_num }}
						</view>
					</view>
				</view>
				<view class="info_time">
					<view class="item_view">
						<view class="lable">
							建企业微信群数：
						</view>
						<view class="content">
							{{ item.build_group_num }}
						</view>
					</view>
					<view class="item_view">
						<view class="lable">
							加企业微信群人数：
						</view>
						<view class="content">
							{{ item.join_group_num }}
						</view>
					</view>
				</view>
				<view class="info_time">
					<view class="item_view">
						<view class="lable">
							租房意向数：
						</view>
						<view class="content">
							{{ item.intention_num }}
						</view>
					</view>
					<view class="item_view">
						<view class="lable">
							通话数：
						</view>
						<view class="content">
							{{ item.calls_num }}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return {
				pageNo: 1,
				pageSize: 50000,
				list: [],
				count: 0,
				todayStatus: 1
			}
		},
		onLoad() {
			this.getVillageInfo()
		},
		onShow() {
			this.getVillageInfo()
		},
		methods: {
			// 跳转详情
			jump(index){
				console.log(this.list[index])
				let status = ''
				if(this.list[index].id){
					if(this.list[index].can_edit == 1){
						status = 'edit'
					} else{
						status = 'detail'
					}
					
				} else{
					status = 'add'
				}
				uni.navigateTo({
					url: '/signIn/detail/index?id=' + this.list[index].id + '&status=' + status + '&class=1'
				})
			},
			// 获取当前日期
			formatDate(date = new Date(), separator = '-') {
				const year = date.getFullYear()
				const month = String(date.getMonth() + 1).padStart(2, '0')
				const day = String(date.getDate()).padStart(2, '0')
				return [year, month, day].join(separator)
			},
			// 获取当前列表 village/info
			getVillageInfo(){
				this.$api.request('village/list', {pageNo: this.pageNo, pageSize: this.pageSize}, (res)=>{
					if(res.status == 'ok'){
						this.list = res.list
						this.count = res.count
						const timer = this.formatDate()
						this.todayStatus = res.today
						if(res.today == 2){
							this.list.unshift({id: 0, target_date: timer, wechat_num: 0, calls_num: 0, intention_num: 0, tiktok_num: 0, interview_num: 0, visits_store_num: 0, build_group_num: 0, join_group_num: 0})
						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.app{
		width: 100%;
		height: 100vh;
		.lists{
			width: calc(100% - 32rpx);
			padding: 30rpx 0;
			margin: 0 auto;
			.items{
				width: 100%;
				padding: 15rpx 24rpx;
				background: #fff;
				border-radius: 10rpx;
				margin-bottom: 20rpx;
				.status{
					position: absolute;
					right: 20rpx;
					transform: rotate(30deg);
					font-size: 20rpx;
					color: #01BEFF;
					box-sizing: border-box;
					border: 1rpx solid #000;
					padding: 5rpx 10rpx;
				}
				.info_time{
					margin-top: 5rpx;
					width: 100% ;
					display: flex;
					.item_view{
						width: 50%;
						display: flex;
						align-items: center;
						.lable{
							font-weight: 550;
						}
					}
				}
				.btns{
					width: 100%;
					position: relative;
					height: 50rpx;
					margin-top: 20rpx;
					.btns1{
						position: absolute;
						right: 0;
					}
				}
			}
		}
	}
</style>