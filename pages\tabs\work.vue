<template>
	<!-- 工作台 -->
	<view class="box">
		<view class="all_app" v-for="(item, index) in list" :key="index">
			<view class="list_title">
				{{ item.name }}
			</view>
			<view class="list">
				<view class="item" @click="headClick(value)" v-for="(value, ins) in item.children" :key="ins">
					<!-- <image class="img" src="/static/baojing.png" mode=""></image> -->
					<view :class="[true ? `img tn-flex tn-flex-row-center tn-flex-col-center tn-shadow-blur tn-cool-bg-color-${index + 1}` : '']">
						<view class="tn-color-white" :class="[`tn-icon-${value.icon}`]"></view>
					</view>
					<view class="texts">
						{{ value.name }}
					</view>
				</view>
				<!-- <view class="item" @click="headClick('/works/message')">
					<image class="img" src="/static/xiaoxi.png" mode=""></image>
					<view class="texts">
						消息
					</view>
				</view> -->
				<!-- 订金金额   门店姓名    
				<view class="item" @click="headClick('/works/message')">
					<image class="img" src="/static/kf.png" mode=""></image>
					<view class="texts">
						公海
					</view>
				</view>
				<view class="item" @click="headClick('/works/message')">
					<image class="img" src="/static/kf.png" mode=""></image>
					<view class="texts">
						领鸡蛋
					</view>
				</view>
				-->
			</view>
		</view>
	</view>
</template>

<script>

	export default{
		data(){
			return {	
				list: []
			}
		},
		mounted() {
			// console.log("我是工作台")
			this.getList()
		},
		onShow() {
			
		},
		methods: {
			getList(){
				this.$api.request('user/getWorkbench', {}, (res)=>{
					if(res.status == 'ok'){
						console.log(res)
						this.list = res.info
					}
				})
			},
			headClick(value){
				console.log(JSON.stringify(value.url) )
				if(value.url){
					uni.setStorageSync('data_url', value.url)
					uni.navigateTo({
						url: "/" + value.path_url
					})
				}else{
					uni.navigateTo({
						url: "/" + value.path_url
					})
				}
				
			}
		}
	}
</script>	

<style lang="scss" scoped>
	.all_app{
		padding: 32rpx 32rpx;
	}
	.box{
		width: 100%;
		height: 100vh;
		overflow: hidden;
		.list_title{
			font-size: 34rpx;
			font-weight: 550;
		}
		.list{
			margin-top: 20rpx;
			display: flex;
			gap: 20rpx 40rpx;
			align-items: center;
			flex-wrap: wrap;
			.item{
				// width: 100rpx;
				height: 150rpx;
				.img{
					width: 100rpx;
					height: 100rpx;
					font-size: 60rpx;
                    margin: 0 auto;
					border-radius: 15rpx;
					margin-bottom: 18rpx;
					z-index: 1;
					&::after {
					  content: " ";
					  position: absolute;
					  z-index: -1;
					  width: 100%;
					  height: 100%;
					  left: 0;
					  bottom: 0;
					  border-radius: inherit;
					  opacity: 1;
					  transform: scale(1, 1);
					  background-size: 100% 100%;
					}
				}
				.texts{
					width: 100%;
					margin-top: 10rpx;
					height: 50rpx;
					font-size: 28rpx;
					line-height: 50rpx;
					text-align: center;	
				}
			}
		}
	}
</style>	