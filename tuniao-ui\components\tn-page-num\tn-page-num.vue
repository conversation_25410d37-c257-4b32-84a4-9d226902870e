<template>
	<view v-if="totalShow" class="tn-page-num-box" :style="{
    left: $tn.string.getLengthUnitValue(left),
    right: $tn.string.getLengthUnitValue(right),
    bottom: $tn.string.getLengthUnitValue(bottom),
    zIndex: zIndex
  }">
		<view class="current">{{current}}</view>
		<view class="line"></view>
		<view class="total">{{total}}</view>
	</view>
</template>

<script>
	import componentsColorMixin from '../../libs/mixin/components_color.js'
	export default {
		name: 'tn-page-num',
		mixins: [componentsColorMixin],
		props: {
			// 按钮距离左边的位置
			left: {
				type: [String, Number],
				default: 'auto'
			},
			// 按钮距离右边的位置
			right: {
				type: [String, Number],
				default: 'auto'
			},
			// 按钮距离底部的位置
			bottom: {
				type: [String, Number],
				default: 100
			},
			zIndex: {
				type: Number,
				default: 999
			},
			// 当前数量
			current: {
				type: Number,
				default: 0
			},
			// 总数量
			total: {
				type: Number,
				default: 0,
			},
			show: {
				type: Boolean,
				default: false,
			}
		},
		data() {
			return {
				totalShow:false,
				timeoutId:0
			}
		},
		onload() {
			if (this.show){
				this.totalShow = true;
			}
		},
		watch: {
			current(val) {
				//默认开启，则不会隐藏
				if (!this.show){
					if (val==0){
						//当前页数量为空
						this.totalShow = false;
					}else{
						//显示
						if (!this.totalShow){
							this.totalShow = true;
							this.timeoutId = setTimeout(() => {
								if (this.current!=this.total){
									this.totalShow = false;
								}
							},10000);
						}else{
							clearTimeout(this.timeoutId);
							this.timeoutId = setTimeout(() => {
								if (this.current!=this.total){
									this.totalShow = false;
								}
							},10000);
						}
						
						if (this.current==this.total){
							this.totalShow = true;
						}
					}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tn-page-num-box {
		position: fixed;
		z-index: 9999;
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		background-color: #ffffff;
		box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.5);
		color: #333333;
		text-align: center;
		font-size: 22rpx;
		opacity: 0.6;
	}

	.current {
		margin-top: 16rpx;
	}

	.line {
		width: 80%;
		border-bottom: 1px solid #999999;
		margin: 4rpx auto;
	}
</style>