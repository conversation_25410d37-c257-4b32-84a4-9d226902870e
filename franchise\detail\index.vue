<template>
	<view class="container">
		<!-- 顶部tab切换，仅在详情模式 -->
		<view v-if="isDetail" class="tab-bar-wrap">
			<tn-tabs :list="tabList" :current="activeTab" @change="activeTab = $event" :isScroll="false" height="80"
				activeColor="#2563eb" inactiveColor="#888" :barStyle="{background:'#2563eb',height:'6rpx',borderRadius:'3rpx'}"
				:activeItemStyle="{fontWeight:'bold',background:'#f0f6ff',borderRadius:'12rpx',boxShadow:'0 2rpx 8rpx rgba(37,99,235,0.08)'}"
				:itemWidth="180"/>
		</view>

		<!-- 合作商信息tab内容 -->
		<template v-if="!isDetail || activeTab === 0">
			<!-- 循环表单组 -->
			<view v-for="group in filteredFormData" :key="group.id" class="form-group">
				<view class="form-title">{{ group.form_name }}</view>
				<!-- 循环表单项 -->
				<view v-for="(item, index) in group.children" :key="index" class="form-item" :class="{ 'textarea-item': item.form_type === 'areatext' }">
					<view class="form-header">
						<text class="label">
							<text v-if="item.require" class="required">*</text>
							{{ item.field_name }}
						</text>
						<view class="form-control">
							<!-- 单选框改为picker -->
							<picker v-if="item.form_type === 'radio' && item.id !== 'province_city_area'"
								:range="item.option_list" range-key="name" @change="(e) => handleRadioChange(item.id, e.detail.value, item.option_list)"
								class="custom-picker" :disabled="isDetail">
								<view class="picker-value">
									<text>{{ formValues[item.id] || "请选择" }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</picker>

							<!-- 省市区选择器 -->
							<picker v-else-if="item.form_type === 'radio' && item.id === 'province_city_area'" mode="region" class="picker" @change="(e) => handleRegionChange(item.id, e)" :disabled="isDetail">
								<view class="picker-value">
									<text>{{ formValues[item.id] || "请选择省市区" }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</picker>

							<!-- 复选框 -->
							<checkbox-group v-else-if="item.form_type === 'checkbox'"
								@change="(e) => handleChange(item.id, e.detail.value)" class="checkbox-group" :disabled="isDetail">
								<label v-for="option in item.option_list" :key="option.value" class="checkbox-option">
									<checkbox :value="option.value" :disabled="isDetail" />
									<text>{{ option.name }}</text>
								</label>
							</checkbox-group>

							<!-- 单行文本 -->
							<input v-else-if="item.form_type === 'text'" type="text" class="input-text" v-model="formValues[item.id]" :placeholder="'请输入' + item.field_name" :disabled="isDetail" />
							
							<!--  -->

							<!-- 多行文本 -->
							<textarea v-else-if="item.form_type === 'areatext'" class="textarea"
								v-model="formValues[item.id]"
								:placeholder="'请输入' + item.field_name" :disabled="isDetail"></textarea>

							<!-- 数字 -->
							<input v-else-if="item.form_type === 'number'" type="number" class="input-number"
								v-model="formValues[item.id]"
								:placeholder="'请输入' + item.field_name" :disabled="isDetail" />

							<!-- 日期 -->
							<picker v-else-if="item.form_type === 'date'" mode="date" class="picker"
								@change="(e) => handleChange(item.id, e.detail.value)" :disabled="isDetail">
								<view class="picker-value">{{
									formValues[item.id] || "请选择日期"
								}}</view>
							</picker>

							<!-- 日期时间 -->
							<picker v-else-if="item.form_type === 'datetime'" mode="datetime"
								@change="(e) => handleChange(item.id, e.detail.value)" :disabled="isDetail">
								<view class="picker-value">
									<text>{{ formValues[item.id] || "请选择日期时间" }}</text>
									<text class="tn-icon-right"></text>
								</view>
							</picker>

							<!-- 文件上传 -->
							<view v-else-if="item.form_type === 'file'" class="upload-box">
								<button class="upload-btn" @click="uploadFile(item.id)" :disabled="isDetail">
									上传文件
								</button>
								<text v-if="formValues[item.id]" class="file-name">{{
									formValues[item.id].name
								}}</text>
							</view>

							<!-- 图片上传 -->
							<button v-else-if="item.form_type === 'image'" class="upload-btn" @click="uploadImage(item.id)" :disabled="isDetail">
								上传图片
							</button>

							<!-- 视频上传 -->
							<view v-else-if="item.form_type === 'video'" class="upload-box">
								<button class="upload-btn" @click="uploadVideo(item.id)" :disabled="isDetail">
									上传视频
								</button>
								<video v-if="formValues[item.id]" :src="formValues[item.id]" class="preview-video"></video>
							</view>

							<!-- 地理位置 -->
							<view v-else-if="item.form_type === 'geo'" class="geo-box">
								<button class="geo-btn" @click="getLocation(item.id)" :disabled="isDetail">
									{{ formValues[item.id] ? '重新获取位置' : '获取位置' }}
								</button>
								<text v-if="formValues[item.id]" class="geo-value">{{ formValues[item.id] }}</text>
							</view>
						</view>
					</view>
					<!-- 图片预览区域 -->
					<view v-if="item.form_type == 'image'" class="upload-preview">
						<view v-for="(img, imgIndex) in formValues[item.id]" :key="imgIndex" class="preview-item">
							<image :src="img.url" class="preview-image" />
							<view class="delete-btn" @click="deleteImage(item.id, imgIndex)" v-if="!isDetail">
								<text class="tn-icon-close"></text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 提交按钮 -->
			<view class="submit-section" v-if="!isDetail">
				<button class="submit-btn" @click="handleSubmit">{{ isEdit ? '保存修改' : '立即提交' }}</button>
			</view>
		</template>

		<!-- 跟进信息tab内容 -->
		<template v-if="isDetail && activeTab === 1">
			<view class="follow-list">
				<view v-if="followList.length === 0" class="empty-follow">暂无跟进信息</view>
				<view v-for="(item, index) in followList" :key="index" class="follow-item">
					<view class="follow-header">
						<text class="follow-user">{{ item.real_name }}</text>
						<text class="follow-time">{{ item.followup_time }}</text>
					</view>
					<view class="follow-info-grid">
						<view class="info-cell">
							<text class="info-label">方式：</text>
							<text class="info-value">{{ item.follow_way || '--' }}</text>
						</view>
						<view class="info-cell">
							<text class="info-label">状态：</text>
							<text class="info-value">{{ item.follow_state || '--' }}</text>
						</view>
						<view v-if="item.followup_venue" class="info-cell">
							<text class="info-label">场所：</text>
							<text class="info-value">{{ item.followup_venue }}</text>
						</view>
					</view>
					<view v-if="item.next_followup_time" class="next-follow-row">
						<text class="info-label">下次跟进：</text>
						<text class="info-value">{{ item.next_followup_time }}</text>
					</view>
					<view v-if="item.followup_img && item.followup_img.length" class="follow-imgs">
						<text>图片：</text>
						<view class="img-list">
							<image v-for="(img, i) in item.followup_img" :key="i" :src="img" class="follow-img" mode="aspectFill" />
						</view>
					</view>
					<view v-if="item.content" class="follow-content-blue">备注：{{ item.content }}</view>
				</view>
			</view>
		</template>
	</view>
</template>

<script>
import httpApi from '@/config/app.js'
import TnTabs from '@/tuniao-ui/components/tn-tabs/tn-tabs.vue'
export default {
	components: { TnTabs },
	data() {
		return {
			formData: [],
			formValues: {},
			submitting: false,
			baseUrl: httpApi + '/api/mini.main/upload',
			id: '',
			isEdit: false,
			isDetail: false,
			// 新增tab相关
			activeTab: 0,
			tabList: [
				{ name: '合作商信息' },
				{ name: '跟进信息' }
			],
			followList: []
		};
	},
	computed: {
		filteredFormData() {
			return this.formData.map(group => ({
				...group,
				children: group.children.filter(item => !this.isDetail || this.hasValue(item))
			}));
		}
	},
	created() {
		this.getChannelSource();
	},
	onLoad(options) {
		
		if (options.id) {
			this.id = options.id;
			if (options.status == 'edit') {
				this.isEdit = true;
				this.dataInput(1);
				this.getDetail();
			} else if (options.status == 'detail') {
				this.isDetail = true;
				this.dataInput(2);
				this.getDetail();
				this.fetchFollowInfo();
			}
		} else{
			this.dataInput(1);
		}
	},
	watch: {
		activeTab(val) {
			if (this.isDetail && val === 1) {
				this.fetchFollowInfo();
			}
		}
	},
	methods: {
		transformChannelData(apiData, id) {
			const channelData = apiData.find((item) => item.id === id);
			if (!channelData || !channelData.children) return [];

			return channelData.children.map((parent) => ({
				name: parent.name,
				value: parent.ext_value,
				children: parent.children
					? parent.children.map((child) => ({
						name: child.name,
						value: child.ext_value,
					}))
					: [],
			}));
		},
		dataInput(status) {
			this.$api.request("partner/initpartnerform", {}, (res) => {
				if (res && res.data) {
					this.formData = res.data;
					if(status == 1){
						this.formData = this.formData.map(group => ({ ...group, children: group.children.filter(item => 
						    item.id !== "create_time" && item.id !== "followup_time"
						  )
						}));
					}
					console.log(JSON.stringify(this.formData), 'this.formData')
					// 初始化表单值
					this.formData.forEach((group) => {
						if (group && group.children) {
							group.children.forEach((item) => {
								if (item) {
									this.$set(this.formValues, item.id, item.default || "");
									// 检查是否存在渠道来源字段
									if (item.id === 'channel_source_1') {
										this.$set(item, 'option_list', []);
										this.getChannelSource(item);
									}
								}
							});
						}
					});

					// 如果地址栏有id参数，获取详情数据
					if (this.id) {
						this.getDetail();
					}
				}
			});
		},
		getChannelSource(item) {
			if (!item) return;
			this.$api.request("common/dictionaryTypeList", { type: 6 }, (res) => {
				if (res && res.info) {
					// 转换数据并赋值给option_list
					const channelData = this.transformChannelData(res.info, 220);
					if (channelData && channelData.length > 0) { 
						this.$set(item, 'option_list', channelData);
					}
				}
			});
		},
		// 处理radio选择变化
		handleRadioChange(id, value, optionList) {
			if (!optionList || !optionList[value]) return;
			
			// 如果是渠道来源一级选择
			if (id === 'channel_source_1') {
				const selectedOption = optionList[value];
				if (!selectedOption) return;
				
				// 找到二级渠道来源字段
				this.formData.forEach(group => {
					if (group && group.children) {
						group.children.forEach(item => {
							if (item && item.id === 'channel_source_2') {
								this.$set(item, 'option_list', selectedOption.children || []);
								this.$set(this.formValues, 'channel_source_2', '');
							}
						});
					}
				});
			}
			// 更新选中的值
			this.$set(this.formValues, id, optionList[value].name);
		},
		// 处理其他表单值变化
		handleChange(id, value) {
			this.$set(this.formValues, id, value);
		},

		// 上传文件
		uploadFile(id) {
			uni.chooseFile({
				count: 9, // 最多选择9个文件
				success: (res) => {
					const tempFiles = res.tempFiles;
					// 显示上传中
					uni.showLoading({
						title: '上传中...'
					});
					
					// 初始化数组
					if (!this.formValues[id]) {
						this.$set(this.formValues, id, []);
					}
					
					// 逐个上传文件
					const uploadPromises = tempFiles.map(file => {
						return new Promise((resolve, reject) => {
							uni.uploadFile({
								url: this.baseUrl,
								filePath: file.path,
								name: 'file',
								header: {
									'token': uni.getStorageSync('token'),
									'content-type': 'multipart/form-data'
								},
								formData: {
									dir: 'file',
									type: 'file'
								},
								success: (uploadRes) => {
									try {
										const result = JSON.parse(uploadRes.data);
										if (result.status === 'ok') {
											resolve({
												name: file.name,
												url: result.url
											});
										} else {
											reject(new Error(result.msg || '上传失败'));
										}
									} catch (e) {
										reject(e);
									}
								},
								fail: reject
							});
						});
					});

					// 处理所有上传结果
					Promise.all(uploadPromises)
						.then(files => {
							// 将新上传的文件信息添加到数组中
							this.formValues[id] = [...this.formValues[id], ...files];
							uni.hideLoading();
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							});
						})
						.catch(err => {
							uni.hideLoading();
							uni.showToast({
								title: err.message || '上传失败',
								icon: 'none'
							});
						});
				}
			});
		},

		// 上传图片
		uploadImage(id) {
			uni.chooseImage({
				count: 9, // 最多选择9张
				success: (res) => {
					const tempFilePaths = res.tempFilePaths;
					// 显示上传中
					uni.showLoading({
						title: '上传中...'
					});
					
					// 初始化数组
					if (!this.formValues[id]) {
						this.$set(this.formValues, id, []);
					}
					
					// 逐个上传图片
					const uploadPromises = tempFilePaths.map(filePath => {
						return new Promise((resolve, reject) => {
							uni.uploadFile({
								url: this.baseUrl,
								filePath: filePath,
								name: 'file',
								header: {
									'token': uni.getStorageSync('token'),
									'content-type': 'multipart/form-data'
								},
								formData: {
									dir: 'profile',
									type: 'image'
								},
								success: (uploadRes) => {
									try {
										const result = JSON.parse(uploadRes.data);
										if (result.status === 'ok') {
											resolve({
												url: result.url,
												path: result.path
											});
										} else {
											reject(new Error(result.msg || '上传失败'));
										}
									} catch (e) {
										reject(e);
									}
								},
								fail: reject
							});
						});
					});

					// 处理所有上传结果
					Promise.all(uploadPromises)
						.then(results => {
							// 将新上传的图片信息添加到数组中
							this.formValues[id] = [...this.formValues[id], ...results];
							uni.hideLoading();
							uni.showToast({
								title: '上传成功',
								icon: 'success'
							});
						})
						.catch(err => {
							uni.hideLoading();
							uni.showToast({
								title: err.message || '上传失败',
								icon: 'none'
							});
						});
				}
			});
		},

		// 删除图片
		deleteImage(id, index) {
			// 从数组中删除指定索引的图片
			const images = [...this.formValues[id]];
			images.splice(index, 1);
			this.$set(this.formValues, id, images);
		},

		// 上传视频
		uploadVideo(id) {
			uni.chooseVideo({
				count: 9, // 最多选择9个视频
				success: (res) => {
					const tempFilePath = res.tempFilePath;
					// 显示上传中
					uni.showLoading({
						title: '上传中...'
					});
					
					// 初始化数组
					if (!this.formValues[id]) {
						this.$set(this.formValues, id, []);
					}
					
					// 上传视频
					uni.uploadFile({
						url: this.baseUrl,
						filePath: tempFilePath,
						name: 'file',
						header: {
							'token': uni.getStorageSync('token'),
							'content-type': 'multipart/form-data'
						},
						formData: {
							dir: 'video',
							type: 'video'
						},
						success: (uploadRes) => {
							uni.hideLoading();
							try {
								const result = JSON.parse(uploadRes.data);
								if (result.status === 'ok') {
									// 将新上传的视频URL添加到数组中
									this.formValues[id] = [...this.formValues[id], result.url];
									uni.showToast({
										title: '上传成功',
										icon: 'success'
									});
								} else {
									uni.showToast({
										title: result.msg || '上传失败',
										icon: 'none'
									});
								}
							} catch (e) {
								uni.showToast({
									title: '上传失败',
									icon: 'none'
								});
							}
						},
						fail: (err) => {
							uni.hideLoading();
							uni.showToast({
								title: '上传失败',
								icon: 'none'
							});
						}
					});
				}
			});
		},

		// 处理省市区选择
		handleRegionChange(fieldId, e) {
			// 获取选择的省市区值
			const [province, city, district] = e.detail.value;
			// 将值存储到表单数据中，空格替换为-
			this.formValues[fieldId] = `${province}-${city}-${district}`;
		},

		// 获取地理位置
		getLocation(id) {
			uni.showLoading({
				title: '获取位置中...'
			});
			
			uni.getLocation({
				type: 'gcj02',
				success: (res) => {
					// 保留6位小数
					const latitude = res.latitude.toFixed(6);
					const longitude = res.longitude.toFixed(6);
					const location = `${longitude},${latitude}`;
					
					this.$set(this.formValues, id, location);
					uni.hideLoading();
				},
				fail: (err) => {
					uni.hideLoading();
					uni.showToast({
						title: '获取位置失败',
						icon: 'none'
					});
				}
			});
		},

		// 获取详情
		getDetail() {
			uni.showLoading({
				title: '加载中...'
			});
			this.$api.request('partner/detail', { id: this.id }, (res) => {
				uni.hideLoading();
				if (res.status === 'ok') {
					// 设置表单数据
					if (res.data) {
						const detailData = res.data;
						
						// 处理基础字段
						this.formValues = {
							...this.formValues,
							address: detailData.address || '',
							channel_source_1: detailData.channel_source_1 || '',
							channel_source_2: detailData.channel_source_2 || '',
							contact_person: detailData.contact_person || '',
							partner_name: detailData.partner_name || '',
							phone: detailData.phone || '',
							province_city_area: detailData.province_city_area || '',
							remark: detailData.remark || '',
							create_time: detailData.create_time,
							followup_time: detailData.followup_time
						};
						console.log(this.formValues)
						// 处理扩展字段
						if (detailData.partner_ext_data && Array.isArray(detailData.partner_ext_data)) {

							detailData.partner_ext_data.forEach(item => {
							
								if (item.partner_form_id && item.value !== undefined) {
									try {
										// 尝试解析JSON字符串
										const value = JSON.parse(item.value);
										this.$set(this.formValues, item.partner_form_id, value);
									} catch (e) {

										// 如果不是JSON字符串，直接使用原值
										this.$set(this.formValues, item.partner_form_id, item.value);
									}
								}
							});
						}

						// 处理渠道来源显示名称
						if (detailData.channel_source_1) {
							const field = this.formData.find(group => {
								return group.children && group.children.find(item => item.id == 'channel_source_1');
							});
							if (field) {
								const channelField = field.children.find(item => item.id == 'channel_source_1');
								if (channelField && channelField.option_list) {
									const option = channelField.option_list.find(opt => opt.value == detailData.channel_source_1);
									if (option) {
										this.$set(this.formValues, 'channel_source_1', option.name);
										// 设置channel_source_2的option_list
										const channel2Field = field.children.find(item => item.id == 'channel_source_2');
										if (channel2Field && option.children) {
											this.$set(channel2Field, 'option_list', option.children);
											// 设置channel_source_2的显示名称
											if (detailData.channel_source_2) {
												const channel2Option = option.children.find(opt => opt.value == detailData.channel_source_2);
												if (channel2Option) {
													this.$set(this.formValues, 'channel_source_2', channel2Option.name);
												}
											}
										}
									}
								}
							}
						}
					}
				} else {
					uni.showToast({
						title: res.info || '获取详情失败',
						icon: 'none'
					});
				}
			});
		},

		// 处理表单提交
		handleSubmit() {
			if (this.submitting) return;
			
			// 表单验证
			if (!this.validateForm()) {
				return;
			}

			this.submitting = true;
			uni.showLoading({
				title: '提交中...'
			});

			// 构建提交数据
			const submitData = this.buildSubmitData();
			
			// 发送请求
			this.$api.request(this.isEdit ? 'partner/edit' : 'partner/add', submitData, (res) => {
				uni.hideLoading();
				this.submitting = false;
				
				if (res.status === 'ok') {
					uni.showToast({
						title: this.isEdit ? '修改成功' : '提交成功',
						icon: 'success',
						duration: 1000,
						success: () => {
							// 提交成功后返回列表页面
							setTimeout(() => {
								uni.redirectTo({
									url: '/franchise/list/index'
								});
							}, 1000);
						}
					});
				} else {
					uni.showToast({
						title: res.info || (this.isEdit ? '修改失败' : '提交失败'),
						icon: 'none'
					});
				}
			}, (err) => {
				uni.hideLoading();
				this.submitting = false;
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			});
		},

		// 表单验证
		validateForm() {
			let isValid = true;
			this.formData.forEach(group => {
				if (group && group.children) {
					group.children.forEach(item => {
						if (item && item.require && !this.formValues[item.id]) {
							uni.showToast({
								title: `请填写${item.field_name}`,
								icon: 'none'
							});
							isValid = false;
							return;
						}
					});
				}
			});
			return isValid;
		},

		// 构建提交数据
		buildSubmitData() {
			// 基础字段
			const baseFields = {
				contact_person: '',
				phone: '',
				partner_name: '',
				province_city_area: '',
				address: '',
				channel_source_1: '',
				channel_source_2: '',
				remark: '',
				data: ''
			};
			
			// 其他字段数据
			const otherData = [];
			

			// 遍历所有表单项
			for (const key in this.formValues) {
				if (this.formValues[key] !== undefined && this.formValues[key] !== '') {
					// 特殊处理渠道来源
					if (key === 'channel_source_1' || key === 'channel_source_2') {
						const field = this.formData.find(item => item.id === key);
						if (field) {
							const selectedOption = field.option_list.find(opt => opt.name === this.formValues[key]);
						
							if (selectedOption) {
								baseFields[key] = selectedOption.value;
							}
						}
					} else if (key in baseFields) {
						// 基础字段直接赋值
						baseFields[key] = this.formValues[key];
					} else {
						// 其他字段放入otherData
						otherData.push({
							id: key,
							value: this.formValues[key]
						});
					}
				}
			}
			
			// 将otherData转换为JSON字符串
			baseFields.data = JSON.stringify(otherData);
			
			// 便利渠道
			this.formData.forEach(group => {
				if (group && group.children) {
					group.children.forEach(item => {
						if (item) {
							const value = this.formValues[item.id] || '';
							
							// 处理基础字段
							if (item.id in baseFields) {
								// 对于渠道来源字段，需要获取选中项的value值
								if (item.id === 'channel_source_1') {
									const selectedOption = item.option_list.find(opt => opt.name == value);
									if(selectedOption){
										baseFields[item.id] = selectedOption.value;
										// 处理channel_source_2
										const channel2Field = group.children.find(child => child.id === 'channel_source_2');
										if (channel2Field && selectedOption.children) {
											const channel2Value = this.formValues['channel_source_2'];
											const channel2Option = selectedOption.children.find(opt => opt.name == channel2Value);
											if (channel2Option) {
												baseFields['channel_source_2'] = channel2Option.value;
											}
										}
									}
								} else if (item.id !== 'channel_source_2') { // 跳过channel_source_2，因为已经在channel_source_1中处理了
									baseFields[item.id] = value;
								}
							} else {
								// 其他字段放入otherData
								otherData[item.id] = value;
							}
						}
					});
				}
			});

			// 如果是编辑模式，添加id字段
			if (this.isEdit) {
				baseFields.id = this.id;
			}
			return baseFields;
		},
		hasValue(item) {
			const value = this.formValues[item.id];
			if (value === undefined || value === null || value === '') {
				return false;
			}
			if (Array.isArray(value)) {
				return value.length > 0;
			}
			if (typeof value === 'object') {
				return Object.keys(value).length > 0;
			}
			return true;
		},
		// 新增：获取跟进信息
		fetchFollowInfo() {
			this.$api.request('partner/followuplog', { id: this.id }, (res) => {
				if (Array.isArray(res.data)) {
					this.followList = res.data.map(item => ({
						...item,
						followup_img: item.followup_img
					}));
				} else if (res.status === 'ok' && Array.isArray(res.data)) {
					this.followList = res.data.map(item => ({
						...item,
						followup_img: item.followup_img
					}));
				} else {
					this.followList = [];
				}
			});
		},
	},
};
</script>

<style lang="scss" scoped>
	.container {
		padding: 30rpx;
		background-color: #f8f8f8;
		min-height: 100vh;
	}

	.form-group {
		margin-bottom: 40rpx;
		background: #fff;
		border-radius: 16rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;

		&:hover {
			transform: translateY(-2rpx);
			box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
		}
	}

	.form-title {
		padding: 28rpx 32rpx;
		font-size: 34rpx;
		font-weight: 600;
		color: #2c3e50;
		background-color: #f5f7fa;
		border-bottom: 1rpx solid #eee;
		position: relative;

		&::after {
			content: "";
			position: absolute;
			left: 0;
			top: 50%;
			transform: translateY(-50%);
			width: 6rpx;
			height: 32rpx;
			background: #409eff;
			border-radius: 3rpx;
		}
	}

	.form-item {
		display: flex;
		flex-direction: column;
		padding: 20rpx 32rpx;
		border-bottom: 1rpx solid #f0f0f0;

		.form-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
		}

		&.textarea-item {
			.form-header {
				flex-direction: column;
				align-items: flex-start;
				gap: 20rpx;

				.label {
					width: 100%;
				}

				.form-control {
					width: 100%;
				}
			}

			.textarea {
				width: 100%;
				height: 200rpx;
				padding: 20rpx;
				line-height: 1.5;
				text-align: left;
				background: #f8f8f8;
				border-radius: 12rpx;
			}
		}
	}

	.form-label {
		width: 200rpx;
		font-size: 28rpx;
		color: #606266;
		display: flex;
		align-items: center;
		font-weight: 500;
		flex-shrink: 0;
	}

	.required {
		color: #f56c6c;
		margin-right: 8rpx;
		font-weight: bold;
	}

	.form-control {
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.unit {
			margin-left: 10rpx;
			font-size: 24rpx;
			color: #666;
		}
	}

	// 单选和复选框布局
	.radio-group,
	.checkbox-group {
		display: flex;
		flex-wrap: wrap;
		gap: 30rpx;
		width: 100%;
	}

	.radio-option,
	.checkbox-option {
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333;
		transition: all 0.3s ease;
		min-width: 160rpx;

		&:hover {
			color: #409eff;
		}

		radio,
		checkbox {
			transform: scale(0.9);
			margin-right: 8rpx;
		}
	}

	// 添加picker相关样式
	.custom-picker {
		width: 160rpx;
	}

	.picker {
		width: 100%;
		height: 60rpx;
		padding: 0 20rpx;
		font-size: 24rpx;
		color: #333;
		background: transparent;
		border: none;
		border-radius: 6rpx;
		transition: all 0.3s ease;
		text-align: right;

		&:focus {
			background: transparent;
			box-shadow: none;
		}

		&::placeholder {
			color: #999;
			font-size: 24rpx;
			text-align: right;
		}
	}

	.picker-value {
		height: 60rpx;
		line-height: 60rpx;
		padding: 0 20rpx;
		font-size: 24rpx;
		color: #333;
		text-align: right;
	}

	.input-text,
	.input-number,
	.textarea,
	.picker {
		width: 100%;
		height: 60rpx;
		padding: 0 20rpx;
		font-size: 24rpx;
		color: #333;
		background: transparent;
		border: none;
		border-radius: 6rpx;
		transition: all 0.3s ease;
		text-align: right;

		&:focus {
			background: transparent;
			box-shadow: none;
		}

		&::placeholder {
			color: #999;
			font-size: 24rpx;
			text-align: right;
		}
	}

	.textarea {
		width: 100%;
		height: 200rpx;
		padding: 20rpx;
		line-height: 1.5;
		text-align: left;
		background: #f8f8f8;
		border-radius: 12rpx;

		&:focus {
			background: #f8f8f8;
		}

		&::placeholder {
			text-align: left;
		}
	}

	// 上传区域样式
	.upload-box {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-around;
		width: 100%;
		gap: 16rpx;
	}

	.upload-btn {
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 24rpx;
		background: #409eff;
		color: #fff;
		border-radius: 6rpx;
		transition: all 0.3s ease;
		padding: 0;

		&:hover {
			background: #66b1ff;
			transform: translateY(-2rpx);
		}

		&:active {
			transform: translateY(0);
		}
	}

	// 图片上传区域
	.upload-container {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: 20rpx;
	}

	.upload-header {
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.upload-preview {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-top: 20rpx;
		width: 100%;

		.preview-item {
			position: relative;
			width: 120rpx;
			height: 120rpx;
			border-radius: 8rpx;
			overflow: hidden;

			.preview-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}

			.delete-btn {
				position: absolute;
				top: 0;
				right: 0;
				width: 40rpx;
				height: 40rpx;
				background: rgba(0, 0, 0, 0.5);
				border-radius: 0 0 0 8rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.tn-icon-close {
					color: #fff;
					font-size: 24rpx;
				}
			}
		}
	}

	.file-name {
		margin-top: 20rpx;
		font-size: 26rpx;
		color: #666;
		padding: 12rpx 20rpx;
		background: #f5f7fa;
		border-radius: 6rpx;
		display: inline-block;
	}

	.preview-video {
		margin-top: 20rpx;
		width: 100%;
		height: 400rpx;
		border-radius: 8rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	}

	/* 添加动画类 */
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.form-group {
		animation: fadeIn 0.5s ease-out;
	}

	// 移除旧的radio相关样式
	.radio-group,
	.radio-option {
		display: none;
	}

	.submit-section {
		padding: 40rpx 30rpx;
		margin-top: 30rpx;
		border-radius: 16rpx;
	}

	.submit-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		background: #409eff;
		color: #fff;
		font-size: 32rpx;
		border-radius: 44rpx;
		text-align: center;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.98);
			background: #66b1ff;
		}

		&:disabled {
			background: #a0cfff;
			opacity: 0.8;
		}
	}

	.geo-box {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		width: 100%;
	}

	.geo-btn {
		width: 200rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 24rpx;
		background: #409eff;
		color: #fff;
		border-radius: 6rpx;
		text-align: center;
		transition: all 0.3s ease;
		padding: 0;

		&:active {
			transform: scale(0.98);
			background: #66b1ff;
		}
	}

	.geo-value {
		font-size: 24rpx;
		color: #666;
		padding: 12rpx 20rpx;
		background: #f5f7fa;
		border-radius: 6rpx;
		word-break: break-all;
	}

	// 添加禁用状态样式
	button[disabled],
	input[disabled],
	textarea[disabled],
	picker[disabled] {
		opacity: 0.6;
		cursor: not-allowed;
	}

	.checkbox-group[disabled] {
		opacity: 0.6;
		pointer-events: none;
	}

	.follow-list {
		padding: 30rpx 0;
	}
	.follow-item {
		background: #fff;
		border-radius: 12rpx;
		margin-bottom: 32rpx;
		padding: 24rpx;
		box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.04);
	}
	.follow-header {
		display: flex;
		justify-content: space-between;
		font-size: 28rpx;
		margin-bottom: 10rpx;
		color: #333;
	}
	.follow-user {
		font-size: 32rpx;
		font-weight: bold;
		color: #222;
	}
	.follow-info-grid {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx 0;
		margin-bottom: 10rpx;
	}
	.info-cell {
		width: 50%;
		display: flex;
		align-items: center;
		margin-bottom: 6rpx;
	}
	.info-label {
		color: #888;
		font-size: 24rpx;
		margin-right: 8rpx;
		min-width: 70rpx;
	}
	.info-value {
		color: #222;
		font-size: 26rpx;
		font-weight: 500;
		word-break: break-all;
	}
	.follow-content-blue {
		color: #2563eb;
		font-size: 26rpx;
		margin-top: 8rpx;
		word-break: break-all;
	}
	.follow-imgs {
		display: flex;
		align-items: center;
		margin-bottom: 10rpx;
		.img-list {
			display: flex;
			gap: 10rpx;
		}
		.follow-img {
			width: 80rpx;
			height: 80rpx;
			border-radius: 8rpx;
			object-fit: cover;
		}
	}
	.empty-follow {
		text-align: center;
		color: #aaa;
		padding: 60rpx 0;
	}
	.tab-bar-wrap {
		background: #fff;
		border-radius: 18rpx;
		box-shadow: 0 4rpx 18rpx rgba(37,99,235,0.08);
		margin-bottom: 36rpx;
		margin-top: 8rpx;
		position: relative;
		z-index: 10;
	}
	.next-follow-row {
		display: flex;
		align-items: center;
		margin-bottom: 8rpx;
		margin-top: 2rpx;
	}
</style>
