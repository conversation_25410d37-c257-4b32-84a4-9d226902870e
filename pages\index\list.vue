<template>
	<view class="container">
		<view class="tabs">
			<tn-tabs :list="tabsList" :current="tabsIndex" :isScroll="false" inactiveColor="#666666" :bold="true"
				backgroundColor="#ffffff" :bar-width="100" :fontSize="32" @change="tabsChange"></tn-tabs>
		</view>

		<view class="page-total">
			当前分类共有{{total}}条记录， 已加载{{list.length}}条记录
		</view>

		<view class="list-box">
			<!-- 先判断是否有数据 -->
			<block v-if="list.length>0">
				<!-- 循环渲染数据 start-->
				<view class="content-bg tn-margin tn-padding" style="position: relative;" v-for="(item, index) in list"
					:key="index" @click="$api.navigateTo('/pages/maintain/detail?id='+item.Id);">
					<view :class="['oa-' + item.ClassifyLevel]"
						style="width: 15rpx;height: 100%;position: absolute;top: 0;left:0;border-radius: 15rpx 0 0 15rpx;font-size: 32rpx;">
					</view>
					<view class="tn-margin-left-xs">
						<view class="tn-flex tn-flex-col-center tn-flex-row-between" style="margin-top: -6rpx;">
							<view
								class="justify-content-item tn-flex tn-flex tn-flex-col-center tn-text-lg tn-text-bold">
								<view class="justify-content-item">
									{{ item.RealName }} {{item.Capacity ? (" - " + item.Capacity + " kwh") : ""}}
								</view>
								<view class="justify-content-item">
									<view class="tag-state"
										:class="['tn-bg-' + item.ClassifyLevel + '--light oa-' + item.ClassifyLevel]"
										style="font-size: 16rpx;">
										<text class="">{{ item.Status }}</text>
									</view>
								</view>
							</view>
							<view class="justify-content-item tn-color-gray">
								{{ item.Distance }}
							</view>
						</view>
						<view class="tn-text-justify clamp-text-1 tn-padding-top-xs tn-color-gray--dark">
							{{ item.Describe }}
						</view>
						<view
							class="tn-flex tn-flex-direction-row tn-flex-col-center tn-flex-row-between tn-padding-top-sm">
							<view class="tn-flex">
								<view class="tn-flex tn-color-gray">
									<view class="icon1__item--icon tn-flex tn-flex-row-center tn-flex-col-center">
										<view class="tn-icon-location"></view>
									</view>
									<view class="tn-margin-left-xs tn-flex-1">{{ item.Town }}
										{{ item.Village }}
									</view>
								</view>
							</view>
							<view class="tn-color-gray">
								{{ item.CreateTime }}
							</view>
						</view>
					</view>
				</view>
				<!-- 循环渲染数据 end-->
				<view class="tn-margin-top-lg">
					<!-- 加载状态，正常显示[上拉显示更多],划到底部时显示[正在加载...],已经加载完全部数据时，显示[已加载全部数据了] -->
					<tn-load-more class="tn-margin-top-lg" :status="loadStatus"
						:loadText="$api.config.loadText"></tn-load-more>
				</view>
			</block>
			<!-- 列表中没有数据 -->
			<view class="tn-margin-top-lg" v-else>
				<!-- 如果正在加载的话，显示加载状态 -->
				<view v-if="loadding">
					<tn-load-more status="loading"></tn-load-more>
				</view>
				<!-- 没有数据且不在加载状态的，显示暂无数据 -->
				<tn-empty mode="data" v-else></tn-empty>
				<!--加载loadding-->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// tabs 参数
				tabsIndex: 0,
				tabsList: [{
						name: '全部',
						value: 0
					},
					{
						name: '待开始',
						value: 2
					},
					{
						name: '待完成',
						value: 3
					},
					{
						name: '待更换',
						value: 4
					}
				],


				// 列表页必须参数 start
				list: [],
				page: 1, //默认页数
				limit: 20, //默认每页条数
				total: 0, //总数
				loadding: true, //是否加载，true加载中，false未在加载
				loadStatus: 'loadmore', //加载状态，loading加载中，nomore无更多数据，loadmore下拉加载
				pull: true, //允许加载，true允许，false不允许
				// 列表页必须参数 start
			}
		},
		onLoad(option) {
			if (option.tabsIndex) {
				this.tabsIndex = parseInt(option.tabsIndex);
			}
			this.getList({
				load: 'new'
			});
		},
		methods: {
			tabsChange(index) {
				this.tabsIndex = index;
				this.page = 1;
				this.getList({
					load: 'new'
				})
			},
			getList(opt) {
				let that = this;
				// 如果是刷新，则清空之前的列表重新加载
				if (opt.load == 'new') {
					that.list = [];
				}

				// 构建请求参数
				opt.Status = that.tabsList[that.tabsIndex].value;
				opt.Page = that.page;
				opt.Limit = that.limit;

				// 调整加载状态
				that.loadding = true;
				that.loadStatus = 'loading';

				// 请求数据
				that.$api.request( 'Maintain/Listing', opt, function(data) {
						// 关闭加载层
						that.loadding = false;
						// 状态正常
						if (data.status == 'ok') {
							that.total = data.count;
							if (data.count > 0) {
								//是否已经加载完了
								if (data.list.length < that.limit) {
									that.loadStatus = 'nomore';
									that.pull = false;
								} else {
									that.loadStatus = 'loadmore';
									that.pull = true;
								}

								// 如果是加载更多的话，将list数据push到列表中，反之直接赋值给列表
								if (opt.load == 'more') {
									that.list.push(...data.list);
								} else {
									that.list = data.list;
								}
							}
						} else if (data.status == 'no') {
							// 数据请求出错，提示错误原因
							that.$api.toast(data.info);
						}
					}
				)
			}
		},
		onReachBottom: function() {
			// 拉到底部时触发继续加载
			if (!this.pull) return;
			this.page = this.page + 1;
			this.getList({
				load: 'more'
			});
		},
		onPullDownRefresh() {
			// 下拉刷新
			this.page = 1;
			this.getList({
				load: 'new'
			});
			setTimeout(() => {
				uni.stopPullDownRefresh();
				this.$api.toast("刷新成功");
			}, 1000);
		},
	}
</script>

<style>
	.tabs {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 80rpx;
		box-shadow: 5rpx 5rpx 5rpx #F8F7F8;
		z-index: 10000;
		/* #ifdef H5 */
		top: 88rpx
			/* #endif */
	}

	.page-total {
		position: fixed;
		top: 80rpx;
		width: 100%;
		height: 60rpx;
		line-height: 60rpx;
		padding-left: 30rpx;
		color: #666666;
		background-color: #eeeeee;
		z-index: 10000;
		/* #ifdef H5 */
		top: 168rpx
			/* #endif */
	}

	.list-box {
		padding-top: 140rpx;
		padding-bottom: 30rpx;
	}
</style>