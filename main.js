import App from './App'
import store from './store/index.js';
import Vue from 'vue'
Vue.config.productionTip = false
App.mpType = 'app'

// 引入全局uView
import uView from '@/uni_modules/uview-ui'
Vue.use(uView)

// 引入全局TuniaoUI
import TuniaoUI from 'tuniao-ui'
Vue.use(TuniaoUI)




// 引入TuniaoUI对小程序分享的mixin封装
let mpShare = require('tuniao-ui/libs/mixin/mpShare.js')
Vue.mixin(mpShare)

import api from '@/common/api'
Vue.prototype.$api = api

import util from '@/common/utils'
Vue.prototype.$util = util

const app = new Vue({
  ...App,
  store
})

// http拦截器，将此部分放在new Vue()和app.$mount()之间，才能App.vue中正常使用
import httpInterceptor from '@/common/uview.interface.js'
Vue.use(httpInterceptor, app)

app.$mount()