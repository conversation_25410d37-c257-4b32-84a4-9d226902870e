<template>
	<view class="app">
		<view class="imgs">
			<image style="width: 100%;" :src="imgUrl" mode="widthFix"></image>
			<!-- <image style="width: 100%;" src="/static/123.png" mode="widthFix"></image> -->
		</view>
		<view class="content">
			<view class="tbs">
				<view class="view1" @click="jumpsAbout">
				<!-- <view class="view1" @click="jumpsAbout"> -->
					<image style="width: 100%;height: 100%;" src="../../static/home-copy.png" mode=""></image>
				</view>
				<view class="view1" @click="phones('61419053')">
					<image style="width: 100%;height: 100%;" src="../../static/kf.png" mode=""></image>
				</view>
			</view>
			<view class="neirong">
				<view class="titles">
					博笛智家
				</view>
				<view class="texts">
					乡村住房装配式建筑的领航者。以专业铸就品质，用科技打造乡村别墅整体交付一体化服务。从主体结构到系统集成，严格把控每一个环节，为您提供设计、建造、装修和建材供应于一体的一站式服务，让您省心省力住好房。
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	import httpApi from '@/config/app.js'
	export default{
		data(){
			return{
				imgUrl: ''
			}
		},
		mounted() {
			console.log(httpApi)
			this.imgUrl = httpApi + '/mini/home1.png'
		},
		methods: {
			jumpsAbout(){
				uni.navigateTo({
					url: '/setup/aboutUs/index'
				})
			},
			phones(phoneNumber){
				uni.showModal({
					title: '确认拨号',
					content: `是否拨打 ${phoneNumber}？`,
					success: (res) => {
						if (res.confirm) {
							uni.makePhoneCall({ phoneNumber: phoneNumber.toString() })
						}
					}
				})
			}
		}
	}
</script>
<style lang="scss">
	.imgs{
		position: fixed;
		width: 100%;
		height: 100vh;
		// background-color: plum;
		z-index: 10;
	}
	.content{
		position: relative;
		width: 100%;
		height: 50vh;
		top: 50vh;
		overflow: hidden;
		overflow-y: auto;
		background-color: #fff;
		z-index: 20;
		.tbs{
			width: 85%;
			margin: 20rpx auto;
			height: 160rpx;
			box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
			display: flex;
			align-items: center;
			justify-content: space-around;
			border-radius: 10rpx;
			gap: 20rpx;
			.view1{
				width: 100rpx;
				height: 100rpx;
			}
		}
		.neirong{
			width: 85%;
			margin: 50rpx auto;
			.titles{
				font-size: 34rpx;
				width: 100%;
				text-align: center;
			}
			.texts{
				font-size: 24rpx;
				margin-top: 20rpx;
			}
		}
	}
</style>