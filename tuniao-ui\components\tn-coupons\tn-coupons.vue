<template>
	<view>
		<tn-popup v-model="show" mode="bottom" :borderRadius="30" @close="close">
			<view class="tn-coupon__box">
				<view class="tn-coupon__title">
					<text>优惠券</text>
					<view class="tn-close" @tap="close">
						<text class="tn-icon tn-icon-close-circle tn-text-xl"></text>
					</view>
				</view>
				<scroll-view scroll-y class="tn-coupon__list">
					<radio-group @change="changeIndex">
						<label class="tn-not-used tn-margin-top-sm">
							<text>不使用优惠券</text>
							<radio :value="'-1'" class="tn-coupon-radio" color="#e41f19" :checked="couponIndex==-1 || couponList.length==0"/>
						</label>
						<label v-for="(item, index) in couponList" :key="index">
							<view class="tn-coupon-item tn-margin-top-sm">
								<view class="tn-coupon-item-left">
									<view class="tn-coupon-price-box">
										<view class="tn-coupon-price-sign">￥</view>
										<view class="tn-coupon-price">{{ item.Amount }}</view>
									</view>
								</view>
								<view class="tn-coupon-item-right">
									<view class="tn-coupon-content">
										<view class="tn-coupon-title-box">
											<view class="tn-coupon-title">{{item.Name }}</view>
										</view>
										<view class="tn-coupon-rule">
											<view class="tn-rule-box tui-padding-btm">
												<view class="tn-coupon-circle"></view>
												<view class="tn-coupon-text">不可叠加使用</view>
											</view>
											<view class="tn-rule-box">
												<view class="tn-coupon-circle"></view>
												<view class="tn-coupon-text">自领取之日起30天有效</view>
											</view>
										</view>
									</view>
									<radio :value="index+''" class="tn-coupon-radio" color="#e41f19" :checked="couponIndex==index" />
								</view>
							</view>
						</label>
					</radio-group>
					<view class="tn-height-10"></view>
				</scroll-view>
				<view class="tn-btn-box">
					<tn-button backgroundColor="tn-bg-red" fontColor="tn-color-white" width="100%" size="md" :fontSize="28"
						:shadow="true"  @click="btnConfirm">确定</tn-button>
				</view>
			</view>
		</tn-popup>
	</view>
</template>

<script>
	export default {
		name: 'tnCoupons',
		emits: ['close','select'],
		props: {
			couponList: {
				type: Array,
				default () {
					return [{}, {}, {}, {}, {}];
				}
			},
			//默认选择
			couponIndex:{
				type: Number,
				default: 0
			},
			//控制显示
			show: {
				type: Boolean,
				default: false
			},
			page: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				selectIndex:0
			};
		},
		onload(){
			this.selectIndex=this.couponIndex;
		},
		methods: {
			close() {
				this.$emit('close', {});
			},
			changeIndex(e){
				this.selectIndex = Number(e.detail.value);
			},
			btnConfirm() {
				this.$emit('select', {index:this.selectIndex});
			}
		}
	};
</script>

<style lang="scss">
	.tn-coupon__box {
		width: 100%;
	}

	.tn-coupon__title {
		width: 100%;
		padding: 30rpx 30rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		font-size: 30rpx;
	}

	.tn-close {
		position: absolute;
		right: 30rpx;
		top: 50%;
		transform: translateY(-50%);
	}

	.tn-coupon__list {
		width: 100%;
		height: 640rpx;
		padding: 0 30rpx;
		box-sizing: border-box;
		background-color: #F0F0F0;
	}
	.tn-not-used{
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 28rpx;
		color: #333333;
		background-color: #fff;
		padding:20rpx 30rpx;
		box-sizing: border-box;
		border-radius:10rpx;
	}

	.tn-coupon-item {
		width: 100%;
		height: 190rpx;
		position: relative;
		display: flex;
		align-items: center;
		padding-right: 30rpx;
		box-sizing: border-box;
		overflow: hidden;
		background-color: #ffffff;
		border-radius: 10rpx;
	}

	.tui-coupon-bg {
		width: 100%;
		height: 210rpx;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 1;
	}

	.tui-coupon-sign {
		height: 110rpx;
		width: 110rpx;
		position: absolute;
		z-index: 9;
		top: -30rpx;
		right: 40rpx;
	}

	.tn-coupon-item-left {
		width: 218rpx;
		padding: 40rpx 0;
		position: relative;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		flex-shrink: 0;
		border-right: 1rpx solid #f0f0f0;
	}

	.tn-coupon-price-box {
		display: flex;
		color: #e41f19;
		align-items: flex-end;
	}

	.tn-coupon-price-sign {
		font-size: 30rpx;
	}

	.tn-coupon-price {
		font-size: 70rpx;
		line-height: 68rpx;
		font-weight: bold;
	}

	.tn-price-small {
		font-size: 58rpx !important;
		line-height: 56rpx !important;
	}

	.tn-coupon-intro {
		background: #f7f7f7;
		padding: 8rpx 10rpx;
		font-size: 26rpx;
		line-height: 26rpx;
		font-weight: 400;
		color: #666;
		margin-top: 18rpx;
	}

	.tn-coupon-item-right {
		flex: 1;
		height: 140rpx;
		position: relative;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-left: 24rpx;
		box-sizing: border-box;
		overflow: hidden;
	}

	.tn-coupon-content {
		width: 82%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.tn-coupon-rule{
		padding-top: 20rpx;
	}
	

	.tn-coupon-title-box {
		display: flex;
		align-items: center;
	}

	.tui-coupon-btn {
		padding: 6rpx;
		background: #ffebeb;
		color: #e41f19;
		font-size: 25rpx;
		line-height: 25rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transform: scale(0.9);
		transform-origin: 0 center;
		border-radius: 4rpx;
		flex-shrink: 0;
	}

	.tn-coupon-title {
		width: 100%;
		font-size: 26rpx;
		color: #333;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tn-rule-box {
		display: flex;
		align-items: center;
		transform: scale(0.8);
		transform-origin: 0 100%;
	}

	.tn-padding-btm {
		padding-bottom: 6rpx;
	}

	.tn-coupon-circle {
		width: 8rpx;
		height: 8rpx;
		background: rgb(160, 160, 160);
		border-radius: 50%;
	}

	.tn-coupon-text {
		font-size: 28rpx;
		line-height: 28rpx;
		font-weight: 400;
		color: #666;
		padding-left: 8rpx;
		white-space: nowrap;
	}


	.tn-coupon-title {
		font-size: 28rpx;
		line-height: 28rpx;
	}

	.tn-coupon-radio {
		transform: scale(0.7);
		transform-origin: 100% center;
	}

	/* #ifdef APP-PLUS || MP */
	.wx-radio-input {
		margin-right: 0 !important;
	}

	/* #endif */

	/* #ifdef H5 */
	uni-radio .uni-radio-input::v-deep {
		margin-right: 0 !important;
	}

	/* #endif */
	.tui-seat__box {
		width: 100%;
		height: 1rpx;
	}

	.tn-btn-box {
		width: 100%;
		padding: 20rpx;
		box-sizing: border-box;
	}
</style>
