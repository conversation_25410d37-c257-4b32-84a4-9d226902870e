<template>
  <view class="container">

    <view class="one-coll pd">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            线索名称 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="nameDesc" placeholder="请输入线索名称" name="nameDesc" :disabled="disabled"></input>
          </view>
        </view>
      </view>

      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            联系电话 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="mobile" placeholder="请输入手机号" name="mobile" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>

      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            详细地址 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input  placeholder="请输入详细地址" name="address" :disabled="disabled" readonly></input>
          </view>
        </view>
      </view>

      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            渠道 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input  placeholder="请选择渠道" name="channel" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>

      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            二级渠道 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input placeholder="请选择二级渠道" name="subChannel" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
    </view>

    <view class="one-coll">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            客户意向 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            意向 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <radio-group v-model="purpose" :disabled="disabled">
              <label>
                <radio value="高" />高
              </label>
              <label>
                <radio value="中" />中
              </label>
              <label>
                <radio value="低" />低
              </label>
            </radio-group>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            层数 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <radio-group v-model="plies" :disabled="disabled">
              <label>
                <radio value="一层" />一层
              </label>
              <label>
                <radio value="二层" />二层
              </label>
            </radio-group>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            结构形式 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input  placeholder="请选择结构形式" name="structure" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            建房时效 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input  placeholder="请选择建房时效" name="ageing" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            宅基地面积 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input  placeholder="请选择宅基地面积" name="area" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            区域 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="district" placeholder="请选择区域" name="district" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            预算 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="budget" placeholder="请选择预算信息" name="budget" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            喜好 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="love" placeholder="请输入喜好" name="love" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            建房风格 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="buildStyle" placeholder="请选择建房风格" name="buildStyle" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            审批情况 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="approvalState" placeholder="请选择审批情况" name="approvalState" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            有无纠纷 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="disputeState" placeholder="请选择有无纠纷" name="disputeState" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            收入来源 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="incomeSource" placeholder="请选择收入来源" name="incomeSource" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
    </view>

    <view class="one-coll">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            客户基础信息 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            姓名 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="name" placeholder="请输入姓名" name="name" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            原光伏客户名 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="nameEnergy" placeholder="请输入原光伏客户名" name="nameEnergy" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            性别 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <radio-group v-model="sex" :disabled="disabled">
              <label>
                <radio value="男" />男
              </label>
              <label>
                <radio value="女" />女
              </label>
            </radio-group>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            生日 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <picker mode="date" :value="birthday" @change="bindDateChange">
              <input v-model="birthday" placeholder="请输入生日" name="birthday" :disabled="disabled"></input>
            </picker>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            身份证号 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="idCard" placeholder="请输入身份证号" name="idCard" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>
    </view>

    <view class="one-coll">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            电话信息 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            手机号1 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="mobile1" placeholder="请输入手机号" name="mobile1" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            手机号2 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="mobile2" placeholder="请输入手机号" name="mobile2" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            手机号3 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="mobile3" placeholder="请输入手机号" name="mobile3" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            手机号4 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="mobile4" placeholder="请输入手机号" name="mobile4" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>
    </view>

    <view class="one-coll">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            家庭信息 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            家庭人口数量 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="familyNumber" placeholder="请输入家庭人口数量" name="familyNumber" type="number" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            有无配偶 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <radio-group v-model="hasMate" :disabled="disabled">
              <label>
                <radio value="有" />有
              </label>
              <label>
                <radio value="无" />无
              </label>
            </radio-group>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            家庭结构 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="familyFormation" placeholder="请选择家庭结构" name="familyFormation" :disabled="disabled" readonly @click="showPickerCom"></input>
          </view>
        </view>
      </view>
    </view>

    <view class="one-coll">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            需求信息 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            室内储物间 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="indoorStorage" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            室外储物间 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="outdoorStorage" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            车库需求 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="carbarn" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            书房需求 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="sanctum" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            健身房 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="gym" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            阳光房 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="sunlightRoom" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            卧室 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="bedroom" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            卫生间 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
		  <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <stepper v-model="bathroom" :step="1" :min="0" :disabled="disabled" @change="onStepperChange"></stepper>
          </view>


        </view>
      </view>
    </view>

    <view class="one-coll">
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            评价和备注 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            邀请人 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <input v-model="inviteId" placeholder="请输入邀请人" name="inviteId" :disabled="disabled"></input>
          </view>
        </view>
      </view>
      <view class="tn-flex tn-flex-row-between tn-strip-min tn-padding">
        <view class="justify-content-item tn-flex-1">
          <view class="tn-text-bold tn-text-lg">
            备注 <text class="tn-color-red tn-padding-left-xs">*</text>
          </view>
          <view class="tn-color-gray tn-padding-top-xs tn-color-black">
            <textarea v-model="remark" placeholder="请输入备注" name="remark" :disabled="disabled"></textarea>
          </view>
        </view>
      </view>
    </view>

    <view class="btn-cont">
      <button @click="cancleSave" class="btn">
        取消
      </button>
      <button @click="onCheckSub" class="btn">
        提交
      </button>
      <button class="btn" form-type="submit">
        保存
      </button>
    </view>
  </view>
</template>



<script>
	export default {
		data() {
			return {
				customList: [{
					Id: 1,
					RealName: "张三",
					Code: 'GJ2024010236589',
					Capacity: 20.36,
					Town: '木林镇',
					Village: '木林村'
				}],
				classifyIndex: 0,
				classifyList: [{
					Id: 1,
					Title: '提示'
				}, {
					Id: 2,
					Title: '一般'
				}, {
					Id: 3,
					Title: '紧急'
				}],
				classIndex: 0,
				classList: [{
					Id: 1,
					Title: '低发'
				}, {
					Id: 2,
					Title: '超发'
				}, {
					Id: 3,
					Title: '排查'
				}],
				describeIndex: 0,
				describeList: [],
				code: '',
				advice: '', //备注
				btnDisabled: false,
				indoorStorage:1,
			}
		},
		onLoad() {
			this.getOption();
		},
		methods: {
			bindClassifyChange: function(e) {
				this.classifyIndex = e.detail.value
			},
			bindClassChange: function(e) {
				this.classIndex = e.detail.value
			},
			bindDescribeChange: function(e) {
				this.describeIndex = e.detail.value
			},
			//加载信息
			getOption() {
				let that = this;
				let data = {};
				data.Options = 'Describe,Class';

				//加载客户维护配置
				that.$api.request('Maintain/MaintainOption', data, function(res) {
					if (res.status == 'ok') {
						that.describeList = res.option.Describe;
						that.classList = res.option.Class;
					} else {
						//错误提示
						that.$api.toast(res.info);
					}
				});
			},
			add() {
				let that = this;
				// 提示层
				that.$api.showLoading("正在提交信息");
				// 禁用按钮，防止反复提交
				that.btnDisabled = true;

				//检测指定参数是否都填写
				let dataArray = [];
				dataArray = [
					[that.code, '客户编码'],
					[that.advice, '建议']
				];
				
				// 一种写法
				// that.$api.valid(dataArray,function(res){
				// 	that.$api.hideLoading();
				// 	//验证失败
				// 	if (res.status==false){
				// 		//验证失败
				// 		console.log("验证失败了哦，信息为：" + res.info);
				// 		// 这里写其他逻辑
				// 		return false;
				// 	}else if(res.status === true){
				// 		// 验证成功
				// 		// 这里写其他逻辑
				// 	}
				// })
				// 注意：第一种写法时，后面不应该有逻辑代码了
				
				// 另一种写法
				if (that.$api.valid(dataArray)){
					// 验证成功
					//构建参数
					let data = {};
					data.Code = that.code;
					data.Advice = that.advice;
					
					that.$api.request('Maintain/AddOk', data, function(data) {
						that.$api.hideLoading();
						if (data.status == 'ok') {
							// 处理请求成功
							that.$api.toast(data.info);
							setTimeout(() => {
								that.btnDisabled = false;
								that.$api.navigateBack();
							}, 2000);
						} else {
							// 处理失败
							that.$api.toast(data.info);
							that.btnDisabled = false;
						}
					})
				}else{
					//验证失败
					console.log("这里验证失败了");
					that.btnDisabled = false;
					that.$api.hideLoading();
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.custom-select-box {
		margin: 10rpx auto;
		width: 100%;

		&_empty {
			height: 100rpx;
			line-height: 100rpx;
			text-align: center;
			color: #00aaff;
			border-bottom: 10rpx solid #EEEEEE;
		}
	}

	.custom-list {
		margin-top: 168rpx;
		padding-bottom: 30rpx;

	}

	.custom-box {
		background-color: #FFFFFF;
		border-radius: 10rpx;
		padding: 30rpx;
		box-shadow: 0 0 10rpx 5rpx rgba(0, 0, 0, 0.1);
		margin: 20rpx 20rpx 0 20rpx;
	}

	.tn-classify {
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		z-index: 10;
		width: 0rpx;
		height: 0rpx;

		&__top-right {
			top: 0;
			right: 20rpx;
			border-top: 70rpx solid;
			border-left: 70rpx solid transparent;
			border-top-color: #ff0000 !important;
			border-bottom-color: #ff0000 !important;
		}
	}

	.add-more {
		height: 100rpx;
		line-height: 100rpx;
		text-align: center;
		color: #00aaff;
		border-bottom: 10rpx solid #EEEEEE;
	}
	
	.tn-padding {
	  padding: 30rpx 30rpx;
	}
	
	.tn-strip-min {
	  border-bottom: 1rpx solid #e6e6e6;
	}
	
	.tn-text-bold {
	  font-weight: bold;
	}
	
	.tn-text-lg {
	  font-size: 32rpx;
	}
	
	.tn-color-gray {
	  color: #999;
	}
	
	.tn-color-black {
	  color: #333;
	}
	
	.tn-padding-left-xs {
	  padding-left: 10rpx;
	}
	
	.tn-padding-top-xs {
	  padding-top: 10rpx;
	}
	
	.btn-cont {
	  position: fixed;
	  bottom: 0;
	  width: 100%;
	  background-color: #fff;
	  padding: 20rpx 30rpx;
	  display: flex;
	  justify-content: space-between;
	
	  .btn {
	    width: 45%;
	    height: 80rpx;
	    line-height: 80rpx;
	    text-align: center;
	    border-radius: 10rpx;
	    color: #fff;
	    background-color: #007aff;
	  }
	}
	
	
	
	
	
</style>





<style lang="scss" scoped>
  /* 整体容器样式 */
  .container {
    min-height: 100vh;
    background-color: #F6F6F6;
  }
  
  /* 表单分组样式 */
  .one-coll {
    background-color: #FFFFFF;
    margin-bottom: 20rpx;
    &.pd {
      padding-top: 20rpx;
    }
  }
  
  /* 表单项样式 */
  .tn-padding {
    padding: 24rpx 30rpx;
  }
  
  /* 底部边框 */
  .tn-strip-min {
    border-bottom: 1rpx solid #EEEEEE;
  }
  
  /* 标签文字样式 */
  .tn-text-bold {
    font-weight: 500;
  }
  
  .tn-text-lg {
    font-size: 32rpx;
    color: #333333;
  }
  
  /* 必填标记样式 */
  .tn-color-red {
    color: #FF4D4F;
  }
  
  /* 输入框样式 */
  .tn-color-gray {
    color: #999999;
    
    input, textarea {
      width: 100%;
      font-size: 28rpx;
      padding: 10rpx 0;
      color: #333333;
      
      &::placeholder {
        color: #999999;
      }
    }
  }
  
  .tn-color-black {
    color: #333333;
  }
  
  /* 间距调整 */
  .tn-padding-left-xs {
    padding-left: 4rpx;
  }
  
  .tn-padding-top-xs {
    padding-top: 10rpx;
  }
  
  /* 单选按钮组样式 */
  radio-group {
    display: flex;
    gap: 40rpx;
    
    label {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      
      radio {
        margin-right: 8rpx;
        transform: scale(0.8);
      }
    }
  }
  
  /* 分组标题样式 */
  .justify-content-item .tn-text-bold:first-child {
    font-size: 34rpx;
    margin-bottom: 10rpx;
  }
  
  /* 底部按钮容器 */
  .btn-cont {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #FFFFFF;
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
    
    .btn {
      flex: 1;
      margin: 0 10rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      border-radius: 8rpx;
      font-size: 28rpx;
      color: #FFFFFF;
      background-color: #4080FF;
      border: none;
      
      &:first-child {
        background-color: #F5F5F5;
        color: #666666;
      }
    }
  }
  
  /* 选择器样式 */
  input[readonly] {
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 16rpx;
      height: 16rpx;
      border-top: 2rpx solid #CCCCCC;
      border-right: 2rpx solid #CCCCCC;
      transform: rotate(45deg);
    }
  }
  
  /* 数字步进器样式 */
  stepper {
    display: flex;
    align-items: center;
    
    .uni-numbox {
      height: 60rpx;
    }
  }
</style>