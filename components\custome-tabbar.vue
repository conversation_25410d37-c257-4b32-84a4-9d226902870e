<template>
  <view class="tabbar" :style="{paddingBottom: safeAreaInsets.bottom + 'px'}">
    <!-- 动态渲染菜单项 -->
    <view 
      v-for="(item, index) in tabList" 
      :key="index"
      class="tab-item"
      :class="{active: selectedIndex === index}"
      @click="switchTab(item, index)"
    >
      <text class="text">{{ item.text }}</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      safeAreaInsets: {}, // 安全区域信息
      selectedIndex: 0,
      // 完整菜单配置
      fullTabs: [
        { pagePath: "/pages/index/index", text: "首页" },
        { pagePath: "/pages/user/user", text: "我的" },
        { pagePath: "/pages/crm/crm", text: "CRM" },
        { pagePath: "/pages/flow/flow", text: "流程" }
      ],
      // 无token时的菜单配置
      guestTabs: [
        { pagePath: "/pages/index/index", text: "首页" },
        { pagePath: "/pages/user/user", text: "我的" }
      ]
    }
  },
  computed: {
    // 动态菜单列表
    tabList() {
      return this.hasToken ? this.fullTabs : this.guestTabs;
    },
    // 获取token状态
    hasToken() {
      return !!uni.getStorageSync('token');
    }
  },
  mounted() {
    this.getSafeArea();
    this.updateSelected();
  },
  methods: {
    // 获取安全区域
    getSafeArea() {
      const systemInfo = uni.getSystemInfoSync();
      this.safeAreaInsets = systemInfo.safeAreaInsets || {};
    },
    // 切换菜单
    switchTab(item, index) {
      if (this.selectedIndex === index) return;
      
      uni.switchTab({
        url: item.pagePath,
        success: () => {
          this.selectedIndex = index;
        }
      });
    },
    // 更新选中状态
    updateSelected() {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentPath = '/' + currentPage.route;
      
      this.selectedIndex = this.tabList.findIndex(
        tab => tab.pagePath === currentPath
      );
    }
  }
}
</script>

<style scoped>
.tabbar {
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: #fff;
  box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
}

.tab-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.active .text {
  color: #007AFF;
  font-weight: bold;
}

.text {
  font-size: 12px;
}
</style>