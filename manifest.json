{
	"name": "博笛智家",
	"appid": "__UNI__0FAF11F",
	"description": "博笛智家",
	"versionName": "1.0.0",
	"versionCode": "100",
	"transformPx": false,
	/* 5+App特有相关 */
	"app-plus": {
		"usingComponents": true,
		"nvueStyleCompiler": "uni-app",
		"compilerVersion": 3,
		"splashscreen": {
			"alwaysShowBeforeRender": true,
			"waiting": true,
			"autoclose": true,
			"delay": 0
		},
		/* 模块配置 */
		"modules": {},
		/* 应用发布信息 */
		"distribute": {
			/* android打包配置 */
			"android": {
				"permissions": [
					"<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
					"<uses-permission android:name=\"android.permission.VIBRATE\"/>",
					"<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
					"<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
					"<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CAMERA\"/>",
					"<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
					"<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
					"<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
					"<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
					"<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
					"<uses-feature android:name=\"android.hardware.camera\"/>",
					"<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
				]
			},
			/* ios打包配置 */
			"ios": {
				"dSYMs": false
			},
			/* SDK配置 */
			"sdkConfigs": {
				"ad": {}
			},
			"splashscreen": {
				"iosStyle": "common",
				"androidStyle": "default",
				"android": {
					"hdpi": "unpackage/res/launch/480762.png",
					"xhdpi": "unpackage/res/launch/7201242.png",
					"xxhdpi": "unpackage/res/launch/********.png"
				}
			},
			"icons": {
				"android": {
					"hdpi": "unpackage/res/iocns/72.png",
					"xhdpi": "unpackage/res/iocns/96.png",
					"xxhdpi": "unpackage/res/iocns/144.png",
					"xxxhdpi": "unpackage/res/iocns/192.png"
				}
			}
		}
	},
	/* 快应用特有相关 */
	"quickapp": {},
	/* 小程序特有相关 */
	"mp-weixin": {
		// 博笛
		"appid": "wxf28e22e85771dab1",
		// 光伏
		// "appid" : "wx38de2177fb0d6d40",	
		"setting": {
			"urlCheck": false,
			"minified": true,
			"es6": true,
			"postcss": true
		},
		"usingComponents": true,
		"permission": {
			"scope.userLocation": {
				"desc": "选择位置"
			},
			"scope.writePhotosAlbum": {
				"desc": "需要您的授权以保存图片到相册"
			}
		},
		"requiredPrivateInfos": ["getLocation", "chooseLocation", "chooseAddress"]
	},
	"mp-alipay": {
		"usingComponents": true
	},
	"mp-baidu": {
		"usingComponents": true
	},
	"mp-toutiao": {
		"usingComponents": true
	},
	"uniStatistics": {
		"enable": false
	},
	"vueVersion": "2",
	"h5": {
		"router": {
			"base": "./"
		}
	}
}