<template>
	<view class="container">
		<view class="box">
			<view class="content">
				{{ company }}（以下简称{{ abbrName }}）在此提醒您，当您使用{{ abbrName }}提供的{{ appName }}（以下简称本软件）时，即表示您已同意按照本《用户隐私声明》来合法使用和保护您的信息。
			</view>
			<view class="content">
				{{ abbrName }}十分重视用户的隐私。您在使用本软件时，本软件可能会收集和使用您的信息。{{ abbrName }}希望您通过本《用户隐私声明》知晓您在使用本软件提供的服务时，本软件将如何收集和保护您的信息。本软件尊重并保护所有使用服务用户的个人隐私权。为了给您提供更准确、更有个性化的服务，本软件会按照本隐私声明的规定使用和披露您的个人信息。除本隐私声明另有规定外，在未征得您事先许可的情况下，本软件不会将这些信息对外披露或向第三方提供。
			
			</view>
			
			<view class="title">
				一、本软件可能收集的信息
			</view>
			<view class="content">
				本软件在提供服务时，可能会收集、使用下列与您相关的信息。若您不提供相关信息，则可能无法享受本软件提供的某些服务，或无法达到某些服务拟达到的实际效果。
			</view>
			<view class="content">
				1.1. 当您注册本软件账户或者您在使用本软件提供的相关服务时，向我平台提供的相关个人信息，包括但不限于手机号、本人身份证信息、本人支付宝账号信息等。
			</view>
			<view class="content">
				1.2. 您在使用本软件提供的服务时，我们可能收集您的日志信息，指您在使用本软件提供服务时，系统可能通过cookies或其他方式自动采集的技术信息包括但不限于您的移动设备以及移动设备所用的版本和识别码。
			</view>
			
			<view class="title">
				1.3. 权限申请明细
			</view>
			<view class="table">
				<tn-table>
					<tn-tr>
						<tn-td span="6" textAlign="center">权限名称</tn-td>
						<tn-td span="6" textAlign="center">用途场景</tn-td>
						<tn-td span="6" textAlign="center">授权方式</tn-td>
						<tn-td span="6" textAlign="center">开启方式</tn-td>
					</tn-tr>
					<tn-tr>
						<tn-td span="6" textAlign="center">存储权限</tn-td>
						<tn-td span="6" textAlign="center">保存邀请二维码，分享商品信息海报保存</tn-td>
						<tn-td span="6" textAlign="center">首次使用相应功能时询问</tn-td>
						<tn-td span="6" textAlign="center">小程序设置-添加到相册</tn-td>
					</tn-tr>
				</tn-table>
			</view>
			
			<view class="title">
				二、{{ abbrName }}对您个人信息的保护
			</view>
			<view class="content">
				{{ abbrName }}将努力采取各种安全技术和程序来保护您的个人信息，包括但不限于SSL、信息加密存储，以防信息的泄漏、毁损或丢失。但同时请您理解，由于现有技术的限制以及其他可能存在的风险，即使我方平台竭尽所能加强安全措施，也无法始终保证信息绝对的安全。同时也请您妥善保护自己的账号信息，如无必要切勿向他人提供。
			</view>
			
			<view class="title">
				三、信息披露
			</view>
			<view class="content">
				3.1. 本软件未经您的许可，不会将您的信息披露给任何第三方。
			</view>
			<view class="content">
				3.2. 根据法律的有关规定，或者行政或司法机构的要求，向第三方或者行政、司法机构披露。
			</view>
			<view class="content">
				3.3. 如您出现违反中国有关法律、法规或者相关规则的情况，需要向第三方披露。
			</view>
			
			<view class="title">
				四、隐私权限说明
			</view>
			<view class="content">
				4.1. 收集手机号信息：
			</view>
			<view class="content">
				（1）点击注册新用户；
			</view>
			<view class="content">
				（2）输入手机号，获取验证码，完成注册。
			</view>
			<view class="content">
				4.2. 收集个人身份信息
			</view>
			<view class="content">
				（1）点击底部导航栏【我的】;
			</view>
			<view class="content">
				（2）点击进入系统工具栏中【设置】;
			</view>
			<view class="content">
				（3）点击【实名认证】，输入个人身份信息完成认证。
			</view>
			
			<view class="title">
				五、注销账号
			</view>
			
			<view class="content">
				如您需要注销账户，您可以在登陆状态下，通过“我的-系统设置-删除账号”中进行注销账号。账号一旦注销，您的信息将在十五日内被删除且不可恢复。
			</view>
			
			
			<view class="title">
				六、关于我们
			</view>
			<view class="content">
				公司名称：{{ company }}
			</view>
			<view class="content">
				注册地址：{{ address }}
			</view>
			<view class="content">
				办公地址：{{ address }}
			</view>
			<view class="content">
				信息保护负责人：{{ name }}（{{ tel }}）
			</view>
			<view class="content">
				{{ company }}
			</view>
			<view class="content">
				2024年05月01日
			</view>
		</view>
		
	</view>
</template>

<script>
	export default {
		data() {
			return {
				company: '--',
				abbrName: '--',
				appName: '--',
				address: '--',
				name: '--',
				tel: '-- '
			}
		},
		onLoad() {
			this.company = this.$api.config.company;
			this.abbrName = this.$api.config.companyAbbr;
			this.appName = this.$api.config.appName;
			this.address = this.$api.config.address;
			this.name = this.$api.config.contact;
			this.tel = this.$api.config.tel;
		}
	}
</script>

<style>
	.box{
		width: 95%;
		margin-left: auto;
		margin-right: auto;
		padding-bottom: 100rpx;
	}
	.content {
		margin-top: 20rpx;
		text-indent: 2rem;
		line-height: 45rpx;
	}

	.title {
		margin-top: 30rpx;
		font-weight: bold;
	}
</style>