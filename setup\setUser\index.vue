<template>
	<!-- 设置 -->
	<view class="app">
		<view class="info">
			<view class="views">
				<view class="text">
					头像
				</view>
				<view class="imgs">
					<image :src="user.avatar" style="width: 100%;height: 100%;" />
				</view>
			</view>
			<view class="views">
				<view class="text">
					姓名
				</view>
				<view class="right">
					{{ user.name }}
				</view>
			</view>
			<view class="views">
				<view class="text">
					手机号
				</view>
				<view class="right">
					{{ user.phone ? user.phone : '--' }}
				</view>
			</view>
			<view class="views">
				<view class="text">
					入职时间
				</view>
				<view class="right">
					{{ user.join_date ? user.join_date : '--' }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default{
		data(){
			return {
				user: uni.getStorageSync('userInfo')
			}
		},
		methods: {
			// getUser(){
			// 	this.$api.request('user/getIndex', {}, (res)=>{
			// 		console.log(res)
			// 	})
			// }
		},
		onLoad() {
			// this.getUser()
		}
	}
</script>

<style lang="scss" scoped>
	.app{
		.info{
			padding: 50rpx 0;
			width: calc(100% - 64rpx);
			margin: 0 auto;
			.views{
				width: 100%;
				padding: 20rpx 0 20rpx 0;
				border-bottom: 1rpx solid #ccc;
				display: flex;
				align-items: center;
				justify-content: space-between;
				.text{
					margin-left: 20rpx;
				}
				.imgs{
					width: 100rpx;
					height: 100rpx;
					border-radius: 50%;
					background: #ccc;
					margin-right: 20rpx;
					overflow: hidden;
				}
				.right{
					border-radius: 50%;
					margin-right: 20rpx;
				}
			}
		}
	}
</style>