<template>
	<view class="container">
		<view class="hr"></view>
		<image class="icon" src="../../static/<EMAIL>" mode=""></image>
		<text class="title">{{ typeObj[type].title }}</text>
		<text class="sub-title">{{ info || typeObj[type].tips }}</text>
		<text class="sub-title">点击「返回」按钮离开当前页面</text>
		<view class="button" @tap="back()">
			<text class="text">返回</text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 文案类型
				typeObj: {
					// 提交数据
					'submit': {
						navTitle: '提交成功',
						title: '提交成功',
						tips: '您已成功提交流程'
					},
					// 修改数据
					'edit': {
						navTitle: '修改成功',
						title: '修改成功',
						tips: '您已成功修改报表'
					},
					// 删除数据
					'delete': {
						navTitle: '删除成功',
						title: '删除成功',
						tips: '您已成功删除报表'
					},
					// 保存工期
					'saveDuration': {
						navTitle: '保存成功',
						title: '保存成功',
						tips: '您已成功保存工期'
					},
					// 驳回
					'reject': {
						navTitle: '驳回成功',
						title: '驳回成功',
						tips: '您已成功驳回工作'
					},
					// 通过审核
					'pass': {
						navTitle: '通过审核',
						title: '通过审核',
						tips: '生产报表通过审核'
					},
					// 撤回报表
					'withdraw': {
						navTitle: '撤回报表',
						title: '撤回报表',
						tips: '已成功撤回生产报表'
					},
					// 密码修改
					'modifyPwd': {
						navTitle: '修改成功',
						title: '修改成功',
						tips: '您已成功修改登录密码'
					}
				},
				type: 'submit', // 类型 submit 提交 edit 修改 modify 修改密码
				info: '' // 提示语
			}
		},
		onLoad(options) {
			if (options.type) {
				this.type = options.type
				this.info = options.info
			}
		},
		onReady() {
			uni.setNavigationBarTitle({
				title: this.typeObj[this.type].navTitle
			})
		},
		methods: {
			back() {
				// 返回上一页
				this.$util.navigateBack()
			}
		}
	}
</script>

<style>
	page {
		background: #fff !important;
	}
</style>
<style lang="scss" scoped>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;

		.hr {
			width: 100%;
			height: 12rpx;
			background: #F8F8F8;
		}

		.icon {
			width: 336rpx;
			height: 256rpx;
			margin-top: 200rpx;
		}

		.title {
			margin: 44rpx 0 32rpx;
			font-size: 36rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			color: #3A3B40;
			line-height: 36rpx;
		}

		.sub-title {
			font-size: 28rpx;
			font-family: PingFang SC, PingFang SC;
			font-weight: bold;
			color: #666666;
			line-height: 48rpx;
			letter-spacing: 1px;
		}

		.button {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 560rpx;
			height: 100rpx;
			margin-top: 292rpx;
			background: #039BD2;
			border-radius: 8rpx;

			.text {
				font-size: 32rpx;
				font-family: PingFang SC, PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				line-height: normal;
			}
		}
	}
</style>